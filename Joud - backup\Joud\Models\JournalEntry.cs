using System;
using System.ComponentModel.DataAnnotations;

namespace Joud.Models
{
    /// <summary>
    /// نموذج قيد اليومية
    /// </summary>
    public class JournalEntry
    {
        [Key]
        public int JournalEntryId { get; set; }

        [Required]
        [StringLength(20)]
        public string EntryNumber { get; set; } = string.Empty;

        [Required]
        public DateTime EntryDate { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public decimal TotalDebit { get; set; }

        [Required]
        public decimal TotalCredit { get; set; }

        public bool IsPosted { get; set; } = false;

        public int? UserId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        [StringLength(100)]
        public string? Reference { get; set; }

        public int CompanyId { get; set; }

        [StringLength(100)]
        public string? CreatedBy { get; set; }

        [StringLength(100)]
        public string? ModifiedBy { get; set; }

        public bool IsActive { get; set; } = true;

        public decimal TotalAmount { get; set; } = 0;

        // Navigation Properties
        public virtual User? User { get; set; }
        public virtual User? CreatedByUser { get; set; }
        public virtual Company? Company { get; set; }
        public virtual ICollection<JournalEntryDetail> Details { get; set; } = new List<JournalEntryDetail>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
    }

    /// <summary>
    /// نموذج تفاصيل قيد اليومية
    /// </summary>
    public class JournalEntryDetail
    {
        [Key]
        public int JournalEntryDetailId { get; set; }

        [Required]
        public int JournalEntryId { get; set; }

        [Required]
        public int AccountId { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public decimal DebitAmount { get; set; }

        public decimal CreditAmount { get; set; }

        // Navigation Properties
        public virtual JournalEntry JournalEntry { get; set; } = null!;
        public virtual Account Account { get; set; } = null!;
    }
}
