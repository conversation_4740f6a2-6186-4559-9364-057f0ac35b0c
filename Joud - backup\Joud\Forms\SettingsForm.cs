using System;
using System.Configuration;
using Microsoft.Data.SqlClient;
using System.Drawing.Printing;
using System.Linq;
using System.Windows.Forms;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إعدادات النظام - Settings Form
    /// تسمح بتكوين إعدادات التطبيق المختلفة
    /// </summary>
    public partial class SettingsForm : Form
    {
        #region Private Fields

        private bool _isLoading = false;
        private bool _hasChanges = false;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ شاشة الإعدادات
        /// </summary>
        public SettingsForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل الشاشة
        /// </summary>
        private void SettingsForm_Load(object sender, EventArgs e)
        {
            try
            {
                _isLoading = true;
                LoadSettings();
                LoadPrinters();
                _isLoading = false;
                _hasChanges = false;
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Button Events

        /// <summary>
        /// حدث النقر على زر الحفظ
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateSettings())
                {
                    SaveSettings();
                    _hasChanges = false;
                    UpdateUI();
                    
                    MessageBox.Show("تم حفظ الإعدادات بنجاح.", "نجح الحفظ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الإلغاء
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                if (_hasChanges)
                {
                    var result = MessageBox.Show("هناك تغييرات لم يتم حفظها. هل تريد الخروج بدون حفظ؟", 
                        "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.No)
                        return;
                }
                
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء العملية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر إعادة التعيين
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟", 
                    "تأكيد إعادة التعيين", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    ResetToDefaults();
                    _hasChanges = true;
                    UpdateUI();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر اختبار الاتصال
        /// </summary>
        private void btnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                TestDatabaseConnection();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الاتصال: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Control Events

        /// <summary>
        /// حدث تغيير حالة المصادقة المتكاملة
        /// </summary>
        private void chkIntegratedSecurity_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                bool useIntegratedSecurity = chkIntegratedSecurity.Checked;
                txtUsername.Enabled = !useIntegratedSecurity;
                txtPassword.Enabled = !useIntegratedSecurity;
                
                if (!_isLoading)
                {
                    _hasChanges = true;
                    UpdateUI();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير إعدادات المصادقة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تهيئة الشاشة
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // تعيين القيم الافتراضية للقوائم المنسدلة
                cmbLanguage.SelectedIndex = 0; // العربية
                cmbTheme.SelectedIndex = 0; // فاتح
                cmbPaperSize.SelectedIndex = 0; // A4
                
                // تعيين القيم الافتراضية للأرقام
                numPasswordExpiry.Value = 90;
                numSessionTimeout.Value = 30;
                
                // تعيين الحالة الافتراضية للخيارات
                chkIntegratedSecurity.Checked = true;
                chkEnableAuditLog.Checked = true;
                chkRequireStrongPassword.Checked = true;
                chkShowPrintPreview.Checked = true;
                chkAutoSelectPrinter.Checked = false;
                
                // ربط أحداث التغيير
                BindChangeEvents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// ربط أحداث التغيير للمكونات
        /// </summary>
        private void BindChangeEvents()
        {
            try
            {
                // ربط أحداث تغيير النصوص
                txtApplicationTitle.TextChanged += OnSettingChanged;
                txtCompanyName.TextChanged += OnSettingChanged;
                txtServer.TextChanged += OnSettingChanged;
                txtDatabase.TextChanged += OnSettingChanged;
                txtUsername.TextChanged += OnSettingChanged;
                txtPassword.TextChanged += OnSettingChanged;
                
                // ربط أحداث تغيير القوائم المنسدلة
                cmbLanguage.SelectedIndexChanged += OnSettingChanged;
                cmbTheme.SelectedIndexChanged += OnSettingChanged;
                cmbDefaultPrinter.SelectedIndexChanged += OnSettingChanged;
                cmbPaperSize.SelectedIndexChanged += OnSettingChanged;
                
                // ربط أحداث تغيير الأرقام
                numPasswordExpiry.ValueChanged += OnSettingChanged;
                numSessionTimeout.ValueChanged += OnSettingChanged;
                
                // ربط أحداث تغيير الخيارات
                chkEnableAuditLog.CheckedChanged += OnSettingChanged;
                chkRequireStrongPassword.CheckedChanged += OnSettingChanged;
                chkShowPrintPreview.CheckedChanged += OnSettingChanged;
                chkAutoSelectPrinter.CheckedChanged += OnSettingChanged;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في ربط أحداث التغيير: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث تغيير الإعدادات
        /// </summary>
        private void OnSettingChanged(object sender, EventArgs e)
        {
            if (!_isLoading)
            {
                _hasChanges = true;
                UpdateUI();
            }
        }

        /// <summary>
        /// تحميل الإعدادات من ملف التكوين
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // تحميل الإعدادات العامة
                txtApplicationTitle.Text = GetSetting("ApplicationTitle", "نظام جود للمحاسبة المالية");
                txtCompanyName.Text = GetSetting("CompanyName", "");
                
                // تحميل إعدادات قاعدة البيانات
                txtServer.Text = GetSetting("DatabaseServer", ".\\SQLEXPRESS");
                txtDatabase.Text = GetSetting("DatabaseName", "JoudAccountingDB");
                txtUsername.Text = GetSetting("DatabaseUsername", "");
                txtPassword.Text = GetSetting("DatabasePassword", "");
                chkIntegratedSecurity.Checked = bool.Parse(GetSetting("IntegratedSecurity", "true"));
                
                // تحميل إعدادات الأمان
                chkEnableAuditLog.Checked = bool.Parse(GetSetting("EnableAuditLog", "true"));
                chkRequireStrongPassword.Checked = bool.Parse(GetSetting("RequireStrongPassword", "true"));
                numPasswordExpiry.Value = decimal.Parse(GetSetting("PasswordExpiryDays", "90"));
                numSessionTimeout.Value = decimal.Parse(GetSetting("SessionTimeoutMinutes", "30"));
                
                // تحميل إعدادات الطباعة
                chkShowPrintPreview.Checked = bool.Parse(GetSetting("ShowPrintPreview", "true"));
                chkAutoSelectPrinter.Checked = bool.Parse(GetSetting("AutoSelectPrinter", "false"));
                
                string defaultPrinter = GetSetting("DefaultPrinter", "");
                if (!string.IsNullOrEmpty(defaultPrinter) && cmbDefaultPrinter.Items.Contains(defaultPrinter))
                {
                    cmbDefaultPrinter.SelectedItem = defaultPrinter;
                }
                
                string paperSize = GetSetting("PaperSize", "A4");
                if (cmbPaperSize.Items.Contains(paperSize))
                {
                    cmbPaperSize.SelectedItem = paperSize;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// حفظ الإعدادات في ملف التكوين
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                // حفظ الإعدادات العامة
                SetSetting("ApplicationTitle", txtApplicationTitle.Text);
                SetSetting("CompanyName", txtCompanyName.Text);
                
                // حفظ إعدادات قاعدة البيانات
                SetSetting("DatabaseServer", txtServer.Text);
                SetSetting("DatabaseName", txtDatabase.Text);
                SetSetting("DatabaseUsername", txtUsername.Text);
                SetSetting("DatabasePassword", txtPassword.Text);
                SetSetting("IntegratedSecurity", chkIntegratedSecurity.Checked.ToString());
                
                // حفظ إعدادات الأمان
                SetSetting("EnableAuditLog", chkEnableAuditLog.Checked.ToString());
                SetSetting("RequireStrongPassword", chkRequireStrongPassword.Checked.ToString());
                SetSetting("PasswordExpiryDays", numPasswordExpiry.Value.ToString());
                SetSetting("SessionTimeoutMinutes", numSessionTimeout.Value.ToString());
                
                // حفظ إعدادات الطباعة
                SetSetting("ShowPrintPreview", chkShowPrintPreview.Checked.ToString());
                SetSetting("AutoSelectPrinter", chkAutoSelectPrinter.Checked.ToString());
                SetSetting("DefaultPrinter", cmbDefaultPrinter.SelectedItem?.ToString() ?? "");
                SetSetting("PaperSize", cmbPaperSize.SelectedItem?.ToString() ?? "A4");
                
                // حفظ التغييرات
                ConfigurationManager.RefreshSection("appSettings");
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حفظ الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قيمة إعداد
        /// </summary>
        private string GetSetting(string key, string defaultValue = "")
        {
            try
            {
                return ConfigurationManager.AppSettings[key] ?? defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// تعيين قيمة إعداد
        /// </summary>
        private void SetSetting(string key, string value)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                
                if (config.AppSettings.Settings[key] != null)
                {
                    config.AppSettings.Settings[key].Value = value;
                }
                else
                {
                    config.AppSettings.Settings.Add(key, value);
                }
                
                config.Save(ConfigurationSaveMode.Modified);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تعيين الإعداد {key}: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل قائمة الطابعات
        /// </summary>
        private void LoadPrinters()
        {
            try
            {
                cmbDefaultPrinter.Items.Clear();
                
                foreach (string printerName in PrinterSettings.InstalledPrinters)
                {
                    cmbDefaultPrinter.Items.Add(printerName);
                }
                
                // تحديد الطابعة الافتراضية
                var defaultPrinter = new PrinterSettings();
                if (cmbDefaultPrinter.Items.Contains(defaultPrinter.PrinterName))
                {
                    cmbDefaultPrinter.SelectedItem = defaultPrinter.PrinterName;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الطابعات: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        private bool ValidateSettings()
        {
            try
            {
                // التحقق من الحقول المطلوبة
                if (string.IsNullOrWhiteSpace(txtApplicationTitle.Text))
                {
                    MessageBox.Show("يجب إدخال عنوان التطبيق.", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabControl1.SelectedTab = tabGeneral;
                    txtApplicationTitle.Focus();
                    return false;
                }
                
                if (string.IsNullOrWhiteSpace(txtServer.Text))
                {
                    MessageBox.Show("يجب إدخال اسم خادم قاعدة البيانات.", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabControl1.SelectedTab = tabDatabase;
                    txtServer.Focus();
                    return false;
                }
                
                if (string.IsNullOrWhiteSpace(txtDatabase.Text))
                {
                    MessageBox.Show("يجب إدخال اسم قاعدة البيانات.", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabControl1.SelectedTab = tabDatabase;
                    txtDatabase.Focus();
                    return false;
                }
                
                // التحقق من بيانات المصادقة إذا لم تكن متكاملة
                if (!chkIntegratedSecurity.Checked)
                {
                    if (string.IsNullOrWhiteSpace(txtUsername.Text))
                    {
                        MessageBox.Show("يجب إدخال اسم المستخدم لقاعدة البيانات.", "خطأ في التحقق", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        tabControl1.SelectedTab = tabDatabase;
                        txtUsername.Focus();
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من الإعدادات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        private void TestDatabaseConnection()
        {
            try
            {
                lblStatus.Text = "جاري اختبار الاتصال...";
                Application.DoEvents();
                
                string connectionString = BuildConnectionString();
                
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    MessageBox.Show("تم الاتصال بقاعدة البيانات بنجاح!", "نجح الاتصال", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                
                lblStatus.Text = "تم اختبار الاتصال بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في الاتصال بقاعدة البيانات:\n{ex.Message}", 
                    "فشل الاتصال", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "فشل في اختبار الاتصال";
            }
        }

        /// <summary>
        /// بناء نص الاتصال بقاعدة البيانات
        /// </summary>
        private string BuildConnectionString()
        {
            var builder = new SqlConnectionStringBuilder
            {
                DataSource = txtServer.Text,
                InitialCatalog = txtDatabase.Text,
                IntegratedSecurity = chkIntegratedSecurity.Checked
            };
            
            if (!chkIntegratedSecurity.Checked)
            {
                builder.UserID = txtUsername.Text;
                builder.Password = txtPassword.Text;
            }
            
            return builder.ConnectionString;
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        private void ResetToDefaults()
        {
            try
            {
                // الإعدادات العامة
                txtApplicationTitle.Text = "نظام جود للمحاسبة المالية";
                txtCompanyName.Text = "";
                cmbLanguage.SelectedIndex = 0;
                cmbTheme.SelectedIndex = 0;
                
                // إعدادات قاعدة البيانات
                txtServer.Text = ".\\SQLEXPRESS";
                txtDatabase.Text = "JoudAccountingDB";
                txtUsername.Text = "";
                txtPassword.Text = "";
                chkIntegratedSecurity.Checked = true;
                
                // إعدادات الأمان
                chkEnableAuditLog.Checked = true;
                chkRequireStrongPassword.Checked = true;
                numPasswordExpiry.Value = 90;
                numSessionTimeout.Value = 30;
                
                // إعدادات الطباعة
                chkShowPrintPreview.Checked = true;
                chkAutoSelectPrinter.Checked = false;
                cmbPaperSize.SelectedIndex = 0; // A4
                
                if (cmbDefaultPrinter.Items.Count > 0)
                {
                    cmbDefaultPrinter.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث واجهة المستخدم
        /// </summary>
        private void UpdateUI()
        {
            try
            {
                // تحديث حالة الأزرار
                btnSave.Enabled = _hasChanges;
                
                // تحديث شريط الحالة
                if (_hasChanges)
                {
                    lblStatus.Text = "هناك تغييرات لم يتم حفظها";
                }
                else
                {
                    lblStatus.Text = "جاهز";
                }
                
                // تحديث حالة حقول المصادقة
                txtUsername.Enabled = !chkIntegratedSecurity.Checked;
                txtPassword.Enabled = !chkIntegratedSecurity.Checked;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة المستخدم: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// عرض شاشة الإعدادات
        /// </summary>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>نتيجة الحوار</returns>
        public static DialogResult ShowSettings(IWin32Window parent = null)
        {
            try
            {
                using (var settingsForm = new SettingsForm())
                {
                    return settingsForm.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض شاشة الإعدادات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        #endregion
    }
}
