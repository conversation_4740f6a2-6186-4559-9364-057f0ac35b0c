using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات تفاصيل فواتير المبيعات
    /// Sales Invoice Detail Data Model
    /// </summary>
    [Table("SalesInvoiceDetails")]
    public class SalesInvoiceDetail
    {
        [Key]
        public int SalesInvoiceDetailId { get; set; }

        [Required]
        [Display(Name = "فاتورة المبيعات")]
        public int SalesInvoiceId { get; set; }

        [Required]
        [Display(Name = "الصنف")]
        public int ProductId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الكمية")]
        public decimal Quantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "سعر الوحدة")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ الخصم")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة الخصم")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ الضريبة")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة الضريبة")]
        public decimal TaxPercentage { get; set; } = 0;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ الإجمالي")]
        public decimal TotalAmount { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("SalesInvoiceId")]
        public virtual SalesInvoice SalesInvoice { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
