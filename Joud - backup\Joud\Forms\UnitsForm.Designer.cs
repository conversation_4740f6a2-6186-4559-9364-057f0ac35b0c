namespace Joud.Forms
{
    partial class UnitsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.btnRefresh = new Button();
            
            this.pnlStatistics = new Panel();
            this.lblTotalUnits = new Label();
            this.lblUnitsWithProducts = new Label();
            this.lblTotalProducts = new Label();
            this.lblNewUnits = new Label();
            
            this.pnlMain = new Panel();
            this.dgvUnits = new DataGridView();
            
            this.pnlDetails = new Panel();
            this.grpUnitDetails = new GroupBox();
            this.lblUnitCode = new Label();
            this.txtUnitCode = new TextBox();
            this.lblUnitName = new Label();
            this.txtUnitName = new TextBox();
            this.lblCommonUnits = new Label();
            this.cmbCommonUnits = new ComboBox();
            this.lblUnitNameEn = new Label();
            this.txtUnitNameEn = new TextBox();
            this.lblDescription = new Label();
            this.txtDescription = new TextBox();
            this.lblProductsCount = new Label();
            
            this.pnlButtons = new Panel();
            this.btnNew = new Button();
            this.btnEdit = new Button();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.btnDelete = new Button();
            this.lblStatus = new Label();

            this.SuspendLayout();

            // Header Panel
            this.pnlHeader.BackColor = Color.FromArgb(52, 73, 94);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Controls.Add(this.txtSearch);
            this.pnlHeader.Controls.Add(this.lblSearch);
            this.pnlHeader.Controls.Add(this.btnRefresh);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Size = new Size(1000, 60);

            this.lblTitle.Text = "إدارة الوحدات";
            this.lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 15);

            this.lblSearch.Text = "البحث:";
            this.lblSearch.Font = new Font("Segoe UI", 10F);
            this.lblSearch.ForeColor = Color.White;
            this.lblSearch.Location = new Point(450, 20);

            this.txtSearch.PlaceholderText = "ابحث بالاسم أو الكود...";
            this.txtSearch.Font = new Font("Segoe UI", 10F);
            this.txtSearch.Location = new Point(250, 17);
            this.txtSearch.Size = new Size(190, 25);
            this.txtSearch.TextChanged += new EventHandler(this.txtSearch_TextChanged);

            this.btnRefresh.Text = "تحديث";
            this.btnRefresh.BackColor = Color.FromArgb(39, 174, 96);
            this.btnRefresh.FlatStyle = FlatStyle.Flat;
            this.btnRefresh.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnRefresh.ForeColor = Color.White;
            this.btnRefresh.Location = new Point(160, 15);
            this.btnRefresh.Size = new Size(80, 30);
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);

            // Statistics Panel
            this.pnlStatistics.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlStatistics.Controls.Add(this.lblTotalUnits);
            this.pnlStatistics.Controls.Add(this.lblUnitsWithProducts);
            this.pnlStatistics.Controls.Add(this.lblTotalProducts);
            this.pnlStatistics.Controls.Add(this.lblNewUnits);
            this.pnlStatistics.Dock = DockStyle.Top;
            this.pnlStatistics.Location = new Point(0, 60);
            this.pnlStatistics.Size = new Size(1000, 40);

            this.lblTotalUnits.Text = "إجمالي الوحدات: 0";
            this.lblTotalUnits.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalUnits.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalUnits.Location = new Point(800, 12);

            this.lblUnitsWithProducts.Text = "وحدات تحتوي على أصناف: 0";
            this.lblUnitsWithProducts.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblUnitsWithProducts.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblUnitsWithProducts.Location = new Point(600, 12);

            this.lblTotalProducts.Text = "إجمالي الأصناف: 0";
            this.lblTotalProducts.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalProducts.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalProducts.Location = new Point(450, 12);

            this.lblNewUnits.Text = "وحدات جديدة هذا الشهر: 0";
            this.lblNewUnits.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblNewUnits.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblNewUnits.Location = new Point(250, 12);

            // Main Panel with DataGridView
            this.pnlMain.Controls.Add(this.dgvUnits);
            this.pnlMain.Dock = DockStyle.Left;
            this.pnlMain.Location = new Point(0, 100);
            this.pnlMain.Size = new Size(600, 440);

            this.dgvUnits.BackgroundColor = Color.White;
            this.dgvUnits.BorderStyle = BorderStyle.None;
            this.dgvUnits.Dock = DockStyle.Fill;

            // Details Panel
            this.pnlDetails.BackColor = Color.FromArgb(240, 244, 247);
            this.pnlDetails.Controls.Add(this.grpUnitDetails);
            this.pnlDetails.Dock = DockStyle.Fill;
            this.pnlDetails.Location = new Point(600, 100);
            this.pnlDetails.Size = new Size(400, 440);

            this.grpUnitDetails.Text = "تفاصيل الوحدة";
            this.grpUnitDetails.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpUnitDetails.Location = new Point(20, 20);
            this.grpUnitDetails.Size = new Size(360, 320);

            // Unit Details Controls
            // Unit Code
            this.lblUnitCode.Text = "كود الوحدة:";
            this.lblUnitCode.Font = new Font("Segoe UI", 9F);
            this.lblUnitCode.Location = new Point(280, 30);
            this.lblUnitCode.Size = new Size(70, 15);

            this.txtUnitCode.Font = new Font("Segoe UI", 9F);
            this.txtUnitCode.Location = new Point(20, 27);
            this.txtUnitCode.Size = new Size(250, 23);
            this.txtUnitCode.ReadOnly = true;

            // Unit Name
            this.lblUnitName.Text = "اسم الوحدة:";
            this.lblUnitName.Font = new Font("Segoe UI", 9F);
            this.lblUnitName.Location = new Point(280, 75);
            this.lblUnitName.Size = new Size(70, 15);

            this.txtUnitName.Font = new Font("Segoe UI", 9F);
            this.txtUnitName.Location = new Point(20, 72);
            this.txtUnitName.Size = new Size(250, 23);

            // Common Units
            this.lblCommonUnits.Text = "وحدات شائعة:";
            this.lblCommonUnits.Font = new Font("Segoe UI", 9F);
            this.lblCommonUnits.Location = new Point(280, 120);
            this.lblCommonUnits.Size = new Size(80, 15);

            this.cmbCommonUnits.Font = new Font("Segoe UI", 9F);
            this.cmbCommonUnits.Location = new Point(20, 117);
            this.cmbCommonUnits.Size = new Size(250, 23);
            this.cmbCommonUnits.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCommonUnits.SelectedIndexChanged += new EventHandler(this.cmbCommonUnits_SelectedIndexChanged);

            // Unit Name English
            this.lblUnitNameEn.Text = "الاسم بالإنجليزية:";
            this.lblUnitNameEn.Font = new Font("Segoe UI", 9F);
            this.lblUnitNameEn.Location = new Point(280, 165);
            this.lblUnitNameEn.Size = new Size(100, 15);

            this.txtUnitNameEn.Font = new Font("Segoe UI", 9F);
            this.txtUnitNameEn.Location = new Point(20, 162);
            this.txtUnitNameEn.Size = new Size(250, 23);

            // Description
            this.lblDescription.Text = "الوصف:";
            this.lblDescription.Font = new Font("Segoe UI", 9F);
            this.lblDescription.Location = new Point(280, 210);
            this.lblDescription.Size = new Size(50, 15);

            this.txtDescription.Font = new Font("Segoe UI", 9F);
            this.txtDescription.Location = new Point(20, 207);
            this.txtDescription.Size = new Size(250, 60);
            this.txtDescription.Multiline = true;

            // Products Count
            this.lblProductsCount.Text = "عدد الأصناف: 0";
            this.lblProductsCount.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblProductsCount.ForeColor = Color.FromArgb(39, 174, 96);
            this.lblProductsCount.Location = new Point(20, 287);
            this.lblProductsCount.Size = new Size(150, 15);

            // Add all controls to group box
            this.grpUnitDetails.Controls.Add(this.lblUnitCode);
            this.grpUnitDetails.Controls.Add(this.txtUnitCode);
            this.grpUnitDetails.Controls.Add(this.lblUnitName);
            this.grpUnitDetails.Controls.Add(this.txtUnitName);
            this.grpUnitDetails.Controls.Add(this.lblCommonUnits);
            this.grpUnitDetails.Controls.Add(this.cmbCommonUnits);
            this.grpUnitDetails.Controls.Add(this.lblUnitNameEn);
            this.grpUnitDetails.Controls.Add(this.txtUnitNameEn);
            this.grpUnitDetails.Controls.Add(this.lblDescription);
            this.grpUnitDetails.Controls.Add(this.txtDescription);
            this.grpUnitDetails.Controls.Add(this.lblProductsCount);

            // Buttons Panel
            this.pnlButtons.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlButtons.Controls.Add(this.btnNew);
            this.pnlButtons.Controls.Add(this.btnEdit);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.btnDelete);
            this.pnlButtons.Controls.Add(this.lblStatus);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Size = new Size(1000, 60);

            // Buttons
            this.btnNew.Text = "جديد";
            this.btnNew.BackColor = Color.FromArgb(39, 174, 96);
            this.btnNew.FlatStyle = FlatStyle.Flat;
            this.btnNew.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnNew.ForeColor = Color.White;
            this.btnNew.Location = new Point(880, 15);
            this.btnNew.Size = new Size(100, 35);
            this.btnNew.Click += new EventHandler(this.btnNew_Click);

            this.btnEdit.Text = "تعديل";
            this.btnEdit.BackColor = Color.FromArgb(41, 128, 185);
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.Location = new Point(770, 15);
            this.btnEdit.Size = new Size(100, 35);
            this.btnEdit.Click += new EventHandler(this.btnEdit_Click);

            this.btnSave.Text = "حفظ";
            this.btnSave.BackColor = Color.FromArgb(39, 174, 96);
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(660, 15);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            this.btnCancel.Text = "إلغاء";
            this.btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(550, 15);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.btnDelete.Text = "حذف";
            this.btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.Location = new Point(440, 15);
            this.btnDelete.Size = new Size(100, 35);
            this.btnDelete.Click += new EventHandler(this.btnDelete_Click);

            this.lblStatus.Text = "جاهز...";
            this.lblStatus.Font = new Font("Segoe UI", 9F);
            this.lblStatus.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblStatus.Location = new Point(20, 25);
            this.lblStatus.Size = new Size(200, 15);

            // Form
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.ClientSize = new Size(1000, 600);
            this.Controls.Add(this.pnlDetails);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlStatistics);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.pnlButtons);
            this.Name = "UnitsForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة الوحدات - نظام جود للمحاسبة المالية";
            this.Load += new EventHandler(this.UnitsForm_Load);

            this.ResumeLayout(false);
        }

        #endregion

        private Panel pnlHeader;
        private Label lblTitle;
        private TextBox txtSearch;
        private Label lblSearch;
        private Button btnRefresh;
        private Panel pnlStatistics;
        private Label lblTotalUnits;
        private Label lblUnitsWithProducts;
        private Label lblTotalProducts;
        private Label lblNewUnits;
        private Panel pnlMain;
        private DataGridView dgvUnits;
        private Panel pnlDetails;
        private GroupBox grpUnitDetails;
        private Label lblUnitCode;
        private TextBox txtUnitCode;
        private Label lblUnitName;
        private TextBox txtUnitName;
        private Label lblCommonUnits;
        private ComboBox cmbCommonUnits;
        private Label lblUnitNameEn;
        private TextBox txtUnitNameEn;
        private Label lblDescription;
        private TextBox txtDescription;
        private Label lblProductsCount;
        private Panel pnlButtons;
        private Button btnNew;
        private Button btnEdit;
        private Button btnSave;
        private Button btnCancel;
        private Button btnDelete;
        private Label lblStatus;
    }
}
