using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة العملاء
    /// Customers Management Form
    /// </summary>
    public partial class CustomersForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly CustomerService _customerService;
        private List<Customer> _customers;
        private Customer? _selectedCustomer;
        private bool _isEditing = false;

        public CustomersForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _customerService = new CustomerService();
            _customers = new List<Customer>();
            
            InitializeFormSettings();
            SetupDataGridView();
            _ = LoadCustomersAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة العملاء - نظام جود للمحاسبة المالية";
            this.Size = new Size(1200, 700);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private void SetupDataGridView()
        {
            // إعداد DataGridView
            dgvCustomers.AutoGenerateColumns = false;
            dgvCustomers.AllowUserToAddRows = false;
            dgvCustomers.AllowUserToDeleteRows = false;
            dgvCustomers.ReadOnly = true;
            dgvCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCustomers.MultiSelect = false;
            dgvCustomers.BackgroundColor = Color.White;
            dgvCustomers.BorderStyle = BorderStyle.None;
            dgvCustomers.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvCustomers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvCustomers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvCustomers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvCustomers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCustomers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvCustomers.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerCode",
                HeaderText = "كود العميل",
                DataPropertyName = "CustomerCode",
                Width = 100,
                ReadOnly = true
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = "اسم العميل",
                DataPropertyName = "CustomerName",
                Width = 200,
                ReadOnly = true
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120,
                ReadOnly = true
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Mobile",
                HeaderText = "الجوال",
                DataPropertyName = "Mobile",
                Width = 120,
                ReadOnly = true
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 180,
                ReadOnly = true
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "City",
                HeaderText = "المدينة",
                DataPropertyName = "City",
                Width = 100,
                ReadOnly = true
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreditLimit",
                HeaderText = "الحد الائتماني",
                DataPropertyName = "CreditLimit",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentBalance",
                HeaderText = "الرصيد الحالي",
                DataPropertyName = "CurrentBalance",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            // أحداث DataGridView
            dgvCustomers.SelectionChanged += DgvCustomers_SelectionChanged;
            dgvCustomers.CellDoubleClick += DgvCustomers_CellDoubleClick;
        }

        private async Task LoadCustomersAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل بيانات العملاء...";
                lblStatus.ForeColor = Color.Blue;

                _customers = await _customerService.GetAllCustomersAsync(_currentCompany.CompanyId);
                dgvCustomers.DataSource = _customers;

                // تحديث الإحصائيات
                await UpdateStatisticsAsync();

                lblStatus.Text = $"تم تحميل {_customers.Count} عميل";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات العملاء: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _customerService.GetCustomerStatisticsAsync(_currentCompany.CompanyId);
                lblTotalCustomers.Text = $"إجمالي العملاء: {stats.TotalCustomers}";
                lblCustomersWithBalance.Text = $"عملاء لديهم رصيد: {stats.CustomersWithBalance}";
                lblTotalBalance.Text = $"إجمالي الأرصدة: {stats.TotalBalance:N2} {_currentCompany.Currency}";
                lblNewCustomers.Text = $"عملاء جدد هذا الشهر: {stats.NewCustomersThisMonth}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        private void DgvCustomers_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                _selectedCustomer = dgvCustomers.SelectedRows[0].DataBoundItem as Customer;
                LoadCustomerDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
            }
            else
            {
                _selectedCustomer = null;
                ClearCustomerDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void DgvCustomers_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadCustomerDetails()
        {
            if (_selectedCustomer == null) return;

            txtCustomerCode.Text = _selectedCustomer.CustomerCode;
            txtCustomerName.Text = _selectedCustomer.CustomerName;
            txtCustomerNameEn.Text = _selectedCustomer.CustomerNameEn;
            txtPhone.Text = _selectedCustomer.Phone;
            txtMobile.Text = _selectedCustomer.Mobile;
            txtEmail.Text = _selectedCustomer.Email;
            txtAddress.Text = _selectedCustomer.Address;
            txtCity.Text = _selectedCustomer.City;
            txtCountry.Text = _selectedCustomer.Country;
            txtTaxNumber.Text = _selectedCustomer.TaxNumber;
            numCreditLimit.Value = _selectedCustomer.CreditLimit;
            lblCurrentBalance.Text = $"الرصيد الحالي: {_selectedCustomer.CurrentBalance:N2} {_currentCompany.Currency}";
        }

        private void ClearCustomerDetails()
        {
            txtCustomerCode.Clear();
            txtCustomerName.Clear();
            txtCustomerNameEn.Clear();
            txtPhone.Clear();
            txtMobile.Clear();
            txtEmail.Clear();
            txtAddress.Clear();
            txtCity.Clear();
            txtCountry.Clear();
            txtTaxNumber.Clear();
            numCreditLimit.Value = 0;
            lblCurrentBalance.Text = "الرصيد الحالي: 0.00";
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                _isEditing = false;
                _selectedCustomer = null;
                ClearCustomerDetails();
                
                // إنشاء كود تلقائي
                string newCode = await _customerService.GenerateCustomerCodeAsync(_currentCompany.CompanyId);
                txtCustomerCode.Text = newCode;
                
                SetEditMode(true);
                txtCustomerName.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء عميل جديد: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedCustomer == null)
            {
                ShowError("يرجى اختيار عميل للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            txtCustomerName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ بيانات العميل...";
                lblStatus.ForeColor = Color.Blue;

                var customer = CreateCustomerFromInput();

                if (_isEditing && _selectedCustomer != null)
                {
                    customer.CustomerId = _selectedCustomer.CustomerId;
                    customer.CreatedDate = _selectedCustomer.CreatedDate;
                    customer.CreatedBy = _selectedCustomer.CreatedBy;
                    customer.CurrentBalance = _selectedCustomer.CurrentBalance;
                    customer.ModifiedBy = _currentUser.UserId;

                    await _customerService.UpdateCustomerAsync(customer);
                    lblStatus.Text = "تم تحديث بيانات العميل بنجاح";
                }
                else
                {
                    customer.CreatedBy = _currentUser.UserId;
                    customer.CompanyId = _currentCompany.CompanyId;

                    await _customerService.AddCustomerAsync(customer);
                    lblStatus.Text = "تم إضافة العميل بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadCustomersAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ بيانات العميل: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedCustomer != null)
            {
                LoadCustomerDetails();
            }
            else
            {
                ClearCustomerDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedCustomer == null)
            {
                ShowError("يرجى اختيار عميل للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف العميل '{_selectedCustomer.CustomerName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف العميل...";
                    lblStatus.ForeColor = Color.Blue;

                    await _customerService.DeleteCustomerAsync(_selectedCustomer.CustomerId, _currentUser.UserId);
                    
                    lblStatus.Text = "تم حذف العميل بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadCustomersAsync();
                    ClearCustomerDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف العميل: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvCustomers.DataSource = _customers;
                }
                else
                {
                    var searchResults = await _customerService.SearchCustomersAsync(txtSearch.Text, _currentCompany.CompanyId);
                    dgvCustomers.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadCustomersAsync();
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
                errors.Add("اسم العميل مطلوب");

            if (!string.IsNullOrEmpty(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
                errors.Add("البريد الإلكتروني غير صحيح");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private Customer CreateCustomerFromInput()
        {
            return new Customer
            {
                CustomerCode = txtCustomerCode.Text.Trim(),
                CustomerName = txtCustomerName.Text.Trim(),
                CustomerNameEn = txtCustomerNameEn.Text.Trim(),
                Phone = txtPhone.Text.Trim(),
                Mobile = txtMobile.Text.Trim(),
                Email = txtEmail.Text.Trim(),
                Address = txtAddress.Text.Trim(),
                City = txtCity.Text.Trim(),
                Country = txtCountry.Text.Trim(),
                TaxNumber = txtTaxNumber.Text.Trim(),
                CreditLimit = numCreditLimit.Value
            };
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال
            txtCustomerCode.ReadOnly = isEditing && _isEditing; // الكود للقراءة فقط عند التعديل
            txtCustomerName.ReadOnly = !isEditing;
            txtCustomerNameEn.ReadOnly = !isEditing;
            txtPhone.ReadOnly = !isEditing;
            txtMobile.ReadOnly = !isEditing;
            txtEmail.ReadOnly = !isEditing;
            txtAddress.ReadOnly = !isEditing;
            txtCity.ReadOnly = !isEditing;
            txtCountry.ReadOnly = !isEditing;
            txtTaxNumber.ReadOnly = !isEditing;
            numCreditLimit.ReadOnly = !isEditing;

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedCustomer != null;
            btnDelete.Enabled = !isEditing && _selectedCustomer != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            dgvCustomers.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is NumericUpDown || control is DataGridView)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void CustomersForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
        }
    }
}
