using System;
using System.IO;
using System.Text;
using Joud.Models;

namespace Joud.Services
{
    /// <summary>
    /// خدمة التصدير
    /// </summary>
    public class ExportService
    {
        /// <summary>
        /// تصدير إلى PDF
        /// </summary>
        public void ExportToPDF(ReportData reportData, string title, string filePath)
        {
            // محاكاة تصدير PDF
            var content = GenerateHTMLContent(reportData, title);
            File.WriteAllText(filePath.Replace(".pdf", ".html"), content, Encoding.UTF8);
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        public void ExportToExcel(ReportData reportData, string title, string filePath)
        {
            // محاكاة تصدير Excel
            var csv = GenerateCSVContent(reportData);
            File.WriteAllText(filePath.Replace(".xlsx", ".csv"), csv, Encoding.UTF8);
        }

        /// <summary>
        /// تصدير إلى Word
        /// </summary>
        public void ExportToWord(ReportData reportData, string title, string filePath)
        {
            // محاكاة تصدير Word
            var content = GenerateHTMLContent(reportData, title);
            File.WriteAllText(filePath.Replace(".docx", ".html"), content, Encoding.UTF8);
        }

        private string GenerateHTMLContent(ReportData reportData, string title)
        {
            var html = new StringBuilder();
            html.AppendLine($"<h1>{title}</h1>");
            html.AppendLine("<table border='1'>");
            
            // رأس الجدول
            html.AppendLine("<tr>");
            foreach (var column in reportData.Columns)
            {
                html.AppendLine($"<th>{column}</th>");
            }
            html.AppendLine("</tr>");
            
            // البيانات
            foreach (var row in reportData.Rows)
            {
                html.AppendLine("<tr>");
                foreach (var cell in row)
                {
                    html.AppendLine($"<td>{cell}</td>");
                }
                html.AppendLine("</tr>");
            }
            
            html.AppendLine("</table>");
            return html.ToString();
        }

        private string GenerateCSVContent(ReportData reportData)
        {
            var csv = new StringBuilder();
            
            // رأس الجدول
            csv.AppendLine(string.Join(",", reportData.Columns));
            
            // البيانات
            foreach (var row in reportData.Rows)
            {
                csv.AppendLine(string.Join(",", row));
            }
            
            return csv.ToString();
        }
    }
}
