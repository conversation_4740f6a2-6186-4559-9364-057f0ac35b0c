namespace Joud.Forms
{
    partial class CustomersForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.btnRefresh = new Button();

            this.pnlStatistics = new Panel();
            this.lblTotalCustomers = new Label();
            this.lblCustomersWithBalance = new Label();
            this.lblTotalBalance = new Label();
            this.lblNewCustomers = new Label();

            this.pnlMain = new Panel();
            this.dgvCustomers = new DataGridView();

            this.pnlDetails = new Panel();
            this.grpCustomerDetails = new GroupBox();
            this.lblCustomerCode = new Label();
            this.txtCustomerCode = new TextBox();
            this.lblCustomerName = new Label();
            this.txtCustomerName = new TextBox();
            this.lblCustomerNameEn = new Label();
            this.txtCustomerNameEn = new TextBox();
            this.lblPhone = new Label();
            this.txtPhone = new TextBox();
            this.lblMobile = new Label();
            this.txtMobile = new TextBox();
            this.lblEmail = new Label();
            this.txtEmail = new TextBox();
            this.lblAddress = new Label();
            this.txtAddress = new TextBox();
            this.lblCity = new Label();
            this.txtCity = new TextBox();
            this.lblCountry = new Label();
            this.txtCountry = new TextBox();
            this.lblTaxNumber = new Label();
            this.txtTaxNumber = new TextBox();
            this.lblCreditLimit = new Label();
            this.numCreditLimit = new NumericUpDown();
            this.lblCurrentBalance = new Label();

            this.pnlButtons = new Panel();
            this.btnNew = new Button();
            this.btnEdit = new Button();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.btnDelete = new Button();
            this.lblStatus = new Label();

            this.SuspendLayout();

            //
            // pnlHeader
            //
            this.pnlHeader.BackColor = Color.FromArgb(52, 73, 94);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Controls.Add(this.txtSearch);
            this.pnlHeader.Controls.Add(this.lblSearch);
            this.pnlHeader.Controls.Add(this.btnRefresh);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Location = new Point(0, 0);
            this.pnlHeader.Name = "pnlHeader";
            this.pnlHeader.Size = new Size(1200, 60);
            this.pnlHeader.TabIndex = 0;

            //
            // lblTitle
            //
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(120, 30);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "إدارة العملاء";

            //
            // lblSearch
            //
            this.lblSearch.AutoSize = true;
            this.lblSearch.Font = new Font("Segoe UI", 10F);
            this.lblSearch.ForeColor = Color.White;
            this.lblSearch.Location = new Point(550, 20);
            this.lblSearch.Name = "lblSearch";
            this.lblSearch.Size = new Size(40, 19);
            this.lblSearch.TabIndex = 1;
            this.lblSearch.Text = "البحث:";

            //
            // txtSearch
            //
            this.txtSearch.Font = new Font("Segoe UI", 10F);
            this.txtSearch.Location = new Point(300, 17);
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.PlaceholderText = "ابحث بالاسم أو الكود أو الهاتف...";
            this.txtSearch.Size = new Size(240, 25);
            this.txtSearch.TabIndex = 2;
            this.txtSearch.TextChanged += new EventHandler(this.txtSearch_TextChanged);

            //
            // btnRefresh
            //
            this.btnRefresh.BackColor = Color.FromArgb(39, 174, 96);
            this.btnRefresh.FlatStyle = FlatStyle.Flat;
            this.btnRefresh.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnRefresh.ForeColor = Color.White;
            this.btnRefresh.Location = new Point(200, 15);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new Size(80, 30);
            this.btnRefresh.TabIndex = 3;
            this.btnRefresh.Text = "تحديث";
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);

            //
            // pnlStatistics
            //
            this.pnlStatistics.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlStatistics.Controls.Add(this.lblTotalCustomers);
            this.pnlStatistics.Controls.Add(this.lblCustomersWithBalance);
            this.pnlStatistics.Controls.Add(this.lblTotalBalance);
            this.pnlStatistics.Controls.Add(this.lblNewCustomers);
            this.pnlStatistics.Dock = DockStyle.Top;
            this.pnlStatistics.Location = new Point(0, 60);
            this.pnlStatistics.Name = "pnlStatistics";
            this.pnlStatistics.Size = new Size(1200, 40);
            this.pnlStatistics.TabIndex = 1;

            // Statistics Labels
            this.lblTotalCustomers.AutoSize = true;
            this.lblTotalCustomers.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalCustomers.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalCustomers.Location = new Point(950, 12);
            this.lblTotalCustomers.Name = "lblTotalCustomers";
            this.lblTotalCustomers.Size = new Size(100, 15);
            this.lblTotalCustomers.TabIndex = 0;
            this.lblTotalCustomers.Text = "إجمالي العملاء: 0";

            this.lblCustomersWithBalance.AutoSize = true;
            this.lblCustomersWithBalance.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCustomersWithBalance.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblCustomersWithBalance.Location = new Point(750, 12);
            this.lblCustomersWithBalance.Name = "lblCustomersWithBalance";
            this.lblCustomersWithBalance.Size = new Size(120, 15);
            this.lblCustomersWithBalance.TabIndex = 1;
            this.lblCustomersWithBalance.Text = "عملاء لديهم رصيد: 0";

            this.lblTotalBalance.AutoSize = true;
            this.lblTotalBalance.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalBalance.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalBalance.Location = new Point(500, 12);
            this.lblTotalBalance.Name = "lblTotalBalance";
            this.lblTotalBalance.Size = new Size(120, 15);
            this.lblTotalBalance.TabIndex = 2;
            this.lblTotalBalance.Text = "إجمالي الأرصدة: 0.00";

            this.lblNewCustomers.AutoSize = true;
            this.lblNewCustomers.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblNewCustomers.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblNewCustomers.Location = new Point(250, 12);
            this.lblNewCustomers.Name = "lblNewCustomers";
            this.lblNewCustomers.Size = new Size(150, 15);
            this.lblNewCustomers.TabIndex = 3;
            this.lblNewCustomers.Text = "عملاء جدد هذا الشهر: 0";

            //
            // pnlMain
            //
            this.pnlMain.Controls.Add(this.dgvCustomers);
            this.pnlMain.Dock = DockStyle.Left;
            this.pnlMain.Location = new Point(0, 100);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new Size(700, 540);
            this.pnlMain.TabIndex = 2;

            //
            // dgvCustomers
            //
            this.dgvCustomers.BackgroundColor = Color.White;
            this.dgvCustomers.BorderStyle = BorderStyle.None;
            this.dgvCustomers.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvCustomers.Dock = DockStyle.Fill;
            this.dgvCustomers.Location = new Point(0, 0);
            this.dgvCustomers.Name = "dgvCustomers";
            this.dgvCustomers.Size = new Size(700, 540);
            this.dgvCustomers.TabIndex = 0;

            //
            // pnlDetails
            //
            this.pnlDetails.BackColor = Color.FromArgb(240, 244, 247);
            this.pnlDetails.Controls.Add(this.grpCustomerDetails);
            this.pnlDetails.Dock = DockStyle.Fill;
            this.pnlDetails.Location = new Point(700, 100);
            this.pnlDetails.Name = "pnlDetails";
            this.pnlDetails.Size = new Size(500, 540);
            this.pnlDetails.TabIndex = 3;

            //
            // grpCustomerDetails
            //
            this.grpCustomerDetails.Controls.Add(this.lblCustomerCode);
            this.grpCustomerDetails.Controls.Add(this.txtCustomerCode);
            this.grpCustomerDetails.Controls.Add(this.lblCustomerName);
            this.grpCustomerDetails.Controls.Add(this.txtCustomerName);
            this.grpCustomerDetails.Controls.Add(this.lblCustomerNameEn);
            this.grpCustomerDetails.Controls.Add(this.txtCustomerNameEn);
            this.grpCustomerDetails.Controls.Add(this.lblPhone);
            this.grpCustomerDetails.Controls.Add(this.txtPhone);
            this.grpCustomerDetails.Controls.Add(this.lblMobile);
            this.grpCustomerDetails.Controls.Add(this.txtMobile);
            this.grpCustomerDetails.Controls.Add(this.lblEmail);
            this.grpCustomerDetails.Controls.Add(this.txtEmail);
            this.grpCustomerDetails.Controls.Add(this.lblAddress);
            this.grpCustomerDetails.Controls.Add(this.txtAddress);
            this.grpCustomerDetails.Controls.Add(this.lblCity);
            this.grpCustomerDetails.Controls.Add(this.txtCity);
            this.grpCustomerDetails.Controls.Add(this.lblCountry);
            this.grpCustomerDetails.Controls.Add(this.txtCountry);
            this.grpCustomerDetails.Controls.Add(this.lblTaxNumber);
            this.grpCustomerDetails.Controls.Add(this.txtTaxNumber);
            this.grpCustomerDetails.Controls.Add(this.lblCreditLimit);
            this.grpCustomerDetails.Controls.Add(this.numCreditLimit);
            this.grpCustomerDetails.Controls.Add(this.lblCurrentBalance);
            this.grpCustomerDetails.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpCustomerDetails.Location = new Point(20, 20);
            this.grpCustomerDetails.Name = "grpCustomerDetails";
            this.grpCustomerDetails.Size = new Size(460, 420);
            this.grpCustomerDetails.TabIndex = 0;
            this.grpCustomerDetails.TabStop = false;
            this.grpCustomerDetails.Text = "تفاصيل العميل";

            // Customer Details Controls
            // Customer Code
            this.lblCustomerCode.AutoSize = true;
            this.lblCustomerCode.Font = new Font("Segoe UI", 9F);
            this.lblCustomerCode.Location = new Point(350, 30);
            this.lblCustomerCode.Name = "lblCustomerCode";
            this.lblCustomerCode.Size = new Size(70, 15);
            this.lblCustomerCode.TabIndex = 0;
            this.lblCustomerCode.Text = "كود العميل:";

            this.txtCustomerCode.Font = new Font("Segoe UI", 9F);
            this.txtCustomerCode.Location = new Point(20, 27);
            this.txtCustomerCode.Name = "txtCustomerCode";
            this.txtCustomerCode.Size = new Size(320, 23);
            this.txtCustomerCode.TabIndex = 1;
            this.txtCustomerCode.ReadOnly = true;
            this.txtCustomerCode.BackColor = Color.FromArgb(236, 240, 241);

            // Customer Name
            this.lblCustomerName.AutoSize = true;
            this.lblCustomerName.Font = new Font("Segoe UI", 9F);
            this.lblCustomerName.Location = new Point(350, 65);
            this.lblCustomerName.Name = "lblCustomerName";
            this.lblCustomerName.Size = new Size(70, 15);
            this.lblCustomerName.TabIndex = 2;
            this.lblCustomerName.Text = "اسم العميل:";

            this.txtCustomerName.Font = new Font("Segoe UI", 9F);
            this.txtCustomerName.Location = new Point(20, 62);
            this.txtCustomerName.Name = "txtCustomerName";
            this.txtCustomerName.Size = new Size(320, 23);
            this.txtCustomerName.TabIndex = 3;

            // Customer Name English
            this.lblCustomerNameEn.AutoSize = true;
            this.lblCustomerNameEn.Font = new Font("Segoe UI", 9F);
            this.lblCustomerNameEn.Location = new Point(350, 100);
            this.lblCustomerNameEn.Name = "lblCustomerNameEn";
            this.lblCustomerNameEn.Size = new Size(100, 15);
            this.lblCustomerNameEn.TabIndex = 4;
            this.lblCustomerNameEn.Text = "الاسم بالإنجليزية:";

            this.txtCustomerNameEn.Font = new Font("Segoe UI", 9F);
            this.txtCustomerNameEn.Location = new Point(20, 97);
            this.txtCustomerNameEn.Name = "txtCustomerNameEn";
            this.txtCustomerNameEn.Size = new Size(320, 23);
            this.txtCustomerNameEn.TabIndex = 5;

            // Phone and Mobile (side by side)
            this.lblPhone.AutoSize = true;
            this.lblPhone.Font = new Font("Segoe UI", 9F);
            this.lblPhone.Location = new Point(350, 135);
            this.lblPhone.Name = "lblPhone";
            this.lblPhone.Size = new Size(50, 15);
            this.lblPhone.TabIndex = 6;
            this.lblPhone.Text = "الهاتف:";

            this.txtPhone.Font = new Font("Segoe UI", 9F);
            this.txtPhone.Location = new Point(180, 132);
            this.txtPhone.Name = "txtPhone";
            this.txtPhone.Size = new Size(160, 23);
            this.txtPhone.TabIndex = 7;

            this.lblMobile.AutoSize = true;
            this.lblMobile.Font = new Font("Segoe UI", 9F);
            this.lblMobile.Location = new Point(130, 135);
            this.lblMobile.Name = "lblMobile";
            this.lblMobile.Size = new Size(40, 15);
            this.lblMobile.TabIndex = 8;
            this.lblMobile.Text = "الجوال:";

            this.txtMobile.Font = new Font("Segoe UI", 9F);
            this.txtMobile.Location = new Point(20, 132);
            this.txtMobile.Name = "txtMobile";
            this.txtMobile.Size = new Size(100, 23);
            this.txtMobile.TabIndex = 9;

            // Email
            this.lblEmail.AutoSize = true;
            this.lblEmail.Font = new Font("Segoe UI", 9F);
            this.lblEmail.Location = new Point(350, 170);
            this.lblEmail.Name = "lblEmail";
            this.lblEmail.Size = new Size(100, 15);
            this.lblEmail.TabIndex = 10;
            this.lblEmail.Text = "البريد الإلكتروني:";

            this.txtEmail.Font = new Font("Segoe UI", 9F);
            this.txtEmail.Location = new Point(20, 167);
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.Size = new Size(320, 23);
            this.txtEmail.TabIndex = 11;

            // Address
            this.lblAddress.AutoSize = true;
            this.lblAddress.Font = new Font("Segoe UI", 9F);
            this.lblAddress.Location = new Point(350, 205);
            this.lblAddress.Name = "lblAddress";
            this.lblAddress.Size = new Size(50, 15);
            this.lblAddress.TabIndex = 12;
            this.lblAddress.Text = "العنوان:";

            this.txtAddress.Font = new Font("Segoe UI", 9F);
            this.txtAddress.Location = new Point(20, 202);
            this.txtAddress.Name = "txtAddress";
            this.txtAddress.Size = new Size(320, 23);
            this.txtAddress.TabIndex = 13;

            // City and Country (side by side)
            this.lblCity.AutoSize = true;
            this.lblCity.Font = new Font("Segoe UI", 9F);
            this.lblCity.Location = new Point(350, 240);
            this.lblCity.Name = "lblCity";
            this.lblCity.Size = new Size(50, 15);
            this.lblCity.TabIndex = 14;
            this.lblCity.Text = "المدينة:";

            this.txtCity.Font = new Font("Segoe UI", 9F);
            this.txtCity.Location = new Point(180, 237);
            this.txtCity.Name = "txtCity";
            this.txtCity.Size = new Size(160, 23);
            this.txtCity.TabIndex = 15;

            this.lblCountry.AutoSize = true;
            this.lblCountry.Font = new Font("Segoe UI", 9F);
            this.lblCountry.Location = new Point(130, 240);
            this.lblCountry.Name = "lblCountry";
            this.lblCountry.Size = new Size(40, 15);
            this.lblCountry.TabIndex = 16;
            this.lblCountry.Text = "البلد:";

            this.txtCountry.Font = new Font("Segoe UI", 9F);
            this.txtCountry.Location = new Point(20, 237);
            this.txtCountry.Name = "txtCountry";
            this.txtCountry.Size = new Size(100, 23);
            this.txtCountry.TabIndex = 17;

            // Tax Number
            this.lblTaxNumber.AutoSize = true;
            this.lblTaxNumber.Font = new Font("Segoe UI", 9F);
            this.lblTaxNumber.Location = new Point(350, 275);
            this.lblTaxNumber.Name = "lblTaxNumber";
            this.lblTaxNumber.Size = new Size(80, 15);
            this.lblTaxNumber.TabIndex = 18;
            this.lblTaxNumber.Text = "الرقم الضريبي:";

            this.txtTaxNumber.Font = new Font("Segoe UI", 9F);
            this.txtTaxNumber.Location = new Point(20, 272);
            this.txtTaxNumber.Name = "txtTaxNumber";
            this.txtTaxNumber.Size = new Size(320, 23);
            this.txtTaxNumber.TabIndex = 19;

            // Credit Limit
            this.lblCreditLimit.AutoSize = true;
            this.lblCreditLimit.Font = new Font("Segoe UI", 9F);
            this.lblCreditLimit.Location = new Point(350, 310);
            this.lblCreditLimit.Name = "lblCreditLimit";
            this.lblCreditLimit.Size = new Size(80, 15);
            this.lblCreditLimit.TabIndex = 20;
            this.lblCreditLimit.Text = "الحد الائتماني:";

            this.numCreditLimit.Font = new Font("Segoe UI", 9F);
            this.numCreditLimit.Location = new Point(20, 307);
            this.numCreditLimit.Name = "numCreditLimit";
            this.numCreditLimit.Size = new Size(320, 23);
            this.numCreditLimit.TabIndex = 21;
            this.numCreditLimit.Maximum = *********;
            this.numCreditLimit.DecimalPlaces = 2;
            this.numCreditLimit.ThousandsSeparator = true;

            // Current Balance (Read-only)
            this.lblCurrentBalance.AutoSize = true;
            this.lblCurrentBalance.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCurrentBalance.ForeColor = Color.FromArgb(39, 174, 96);
            this.lblCurrentBalance.Location = new Point(20, 345);
            this.lblCurrentBalance.Name = "lblCurrentBalance";
            this.lblCurrentBalance.Size = new Size(120, 15);
            this.lblCurrentBalance.TabIndex = 22;
            this.lblCurrentBalance.Text = "الرصيد الحالي: 0.00";

            //
            // pnlButtons
            //
            this.pnlButtons.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlButtons.Controls.Add(this.btnNew);
            this.pnlButtons.Controls.Add(this.btnEdit);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.btnDelete);
            this.pnlButtons.Controls.Add(this.lblStatus);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Location = new Point(0, 640);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Size = new Size(1200, 60);
            this.pnlButtons.TabIndex = 4;

            // Buttons
            this.btnNew.BackColor = Color.FromArgb(39, 174, 96);
            this.btnNew.FlatStyle = FlatStyle.Flat;
            this.btnNew.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnNew.ForeColor = Color.White;
            this.btnNew.Location = new Point(1080, 15);
            this.btnNew.Name = "btnNew";
            this.btnNew.Size = new Size(100, 35);
            this.btnNew.TabIndex = 0;
            this.btnNew.Text = "جديد";
            this.btnNew.UseVisualStyleBackColor = false;
            this.btnNew.Click += new EventHandler(this.btnNew_Click);

            this.btnEdit.BackColor = Color.FromArgb(41, 128, 185);
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.Location = new Point(970, 15);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Size = new Size(100, 35);
            this.btnEdit.TabIndex = 1;
            this.btnEdit.Text = "تعديل";
            this.btnEdit.UseVisualStyleBackColor = false;
            this.btnEdit.Click += new EventHandler(this.btnEdit_Click);

            this.btnSave.BackColor = Color.FromArgb(39, 174, 96);
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(860, 15);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.TabIndex = 2;
            this.btnSave.Text = "حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            this.btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(750, 15);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.Location = new Point(640, 15);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new Size(100, 35);
            this.btnDelete.TabIndex = 4;
            this.btnDelete.Text = "حذف";
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new EventHandler(this.btnDelete_Click);

            //
            // lblStatus
            //
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new Font("Segoe UI", 9F);
            this.lblStatus.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblStatus.Location = new Point(20, 25);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(100, 15);
            this.lblStatus.TabIndex = 5;
            this.lblStatus.Text = "جاهز...";

            //
            // CustomersForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.ClientSize = new Size(1200, 700);
            this.Controls.Add(this.pnlDetails);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlStatistics);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.pnlButtons);
            this.Name = "CustomersForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة العملاء - نظام جود للمحاسبة المالية";
            this.Load += new EventHandler(this.CustomersForm_Load);
            this.pnlHeader.ResumeLayout(false);
            this.pnlHeader.PerformLayout();
            this.pnlStatistics.ResumeLayout(false);
            this.pnlStatistics.PerformLayout();
            this.pnlMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvCustomers)).EndInit();
            this.pnlDetails.ResumeLayout(false);
            this.grpCustomerDetails.ResumeLayout(false);
            this.grpCustomerDetails.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCreditLimit)).EndInit();
            this.pnlButtons.ResumeLayout(false);
            this.pnlButtons.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion

        private Panel pnlHeader;
        private Label lblTitle;
        private TextBox txtSearch;
        private Label lblSearch;
        private Button btnRefresh;
        private Panel pnlStatistics;
        private Label lblTotalCustomers;
        private Label lblCustomersWithBalance;
        private Label lblTotalBalance;
        private Label lblNewCustomers;
        private Panel pnlMain;
        private DataGridView dgvCustomers;
        private Panel pnlDetails;
        private GroupBox grpCustomerDetails;
        private Label lblCustomerCode;
        private TextBox txtCustomerCode;
        private Label lblCustomerName;
        private TextBox txtCustomerName;
        private Label lblCustomerNameEn;
        private TextBox txtCustomerNameEn;
        private Label lblPhone;
        private TextBox txtPhone;
        private Label lblMobile;
        private TextBox txtMobile;
        private Label lblEmail;
        private TextBox txtEmail;
        private Label lblAddress;
        private TextBox txtAddress;
        private Label lblCity;
        private TextBox txtCity;
        private Label lblCountry;
        private TextBox txtCountry;
        private Label lblTaxNumber;
        private TextBox txtTaxNumber;
        private Label lblCreditLimit;
        private NumericUpDown numCreditLimit;
        private Label lblCurrentBalance;
        private Panel pnlButtons;
        private Button btnNew;
        private Button btnEdit;
        private Button btnSave;
        private Button btnCancel;
        private Button btnDelete;
        private Label lblStatus;
    }
}