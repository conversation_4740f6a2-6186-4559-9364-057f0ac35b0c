using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Joud.Models;

namespace Joud.Services
{
    /// <summary>
    /// خدمة إدارة المستخدمين
    /// </summary>
    public class UserService
    {
        private static List<User> _users = new List<User>();
        private static int _nextId = 1;

        static UserService()
        {
            // إضافة مستخدم افتراضي
            _users.Add(new User
            {
                UserId = _nextId++,
                Username = "admin",
                FullName = "مدير النظام",
                Email = "<EMAIL>",
                PasswordHash = "jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg=", // 123456
                IsActive = true,
                RoleId = 1,
                RoleName = "مدير النظام",
                CreatedDate = DateTime.Now
            });
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        public IEnumerable<User> GetAllUsers()
        {
            return _users.AsEnumerable();
        }

        /// <summary>
        /// الحصول على جميع المستخدمين (async)
        /// </summary>
        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return await Task.FromResult(_users.AsEnumerable());
        }

        /// <summary>
        /// البحث عن المستخدمين
        /// </summary>
        public async Task<IEnumerable<User>> SearchUsersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetAllUsersAsync();
            }

            var term = searchTerm.ToLower();
            var results = _users.Where(u =>
                u.Username.ToLower().Contains(term) ||
                u.FullName.ToLower().Contains(term) ||
                (u.Email != null && u.Email.ToLower().Contains(term)) ||
                (u.Phone != null && u.Phone.Contains(term))
            );

            return await Task.FromResult(results);
        }

        /// <summary>
        /// الحصول على مستخدم بالمعرف
        /// </summary>
        public User? GetUserById(int id)
        {
            return _users.FirstOrDefault(u => u.UserId == id);
        }

        /// <summary>
        /// الحصول على مستخدم باسم المستخدم
        /// </summary>
        public User? GetUserByUsername(string username)
        {
            return _users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        public User AddUser(User user)
        {
            user.UserId = _nextId++;
            user.CreatedDate = DateTime.Now;
            _users.Add(user);
            return user;
        }

        /// <summary>
        /// تحديث مستخدم
        /// </summary>
        public void UpdateUser(User user)
        {
            var existingUser = GetUserById(user.UserId);
            if (existingUser != null)
            {
                var index = _users.IndexOf(existingUser);
                user.ModifiedDate = DateTime.Now;
                _users[index] = user;
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        public void DeleteUser(int id)
        {
            var user = GetUserById(id);
            if (user != null)
            {
                _users.Remove(user);
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة مرور المستخدم
        /// </summary>
        public void ResetUserPassword(int userId, string newPasswordHash)
        {
            var user = GetUserById(userId);
            if (user != null)
            {
                user.PasswordHash = newPasswordHash;
                user.ModifiedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// التحقق من صحة تسجيل الدخول
        /// </summary>
        public User? ValidateLogin(string username, string passwordHash)
        {
            var user = GetUserByUsername(username);
            if (user != null && user.IsActive && user.PasswordHash == passwordHash)
            {
                user.LastLoginDate = DateTime.Now;
                return user;
            }
            return null;
        }
    }
}
