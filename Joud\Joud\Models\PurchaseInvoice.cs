using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات فواتير المشتريات
    /// Purchase Invoice Data Model
    /// </summary>
    [Table("PurchaseInvoices")]
    public class PurchaseInvoice
    {
        [Key]
        public int PurchaseInvoiceId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم الفاتورة")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "تاريخ الفاتورة")]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "المورد")]
        public int SupplierId { get; set; }

        [Required]
        [Display(Name = "المخزن")]
        public int WarehouseId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المجموع الفرعي")]
        public decimal SubTotal { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ الخصم")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة الخصم")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ الضريبة")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة الضريبة")]
        public decimal TaxPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "معدل الضريبة")]
        public decimal TaxRate { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "معدل الخصم")]
        public decimal DiscountRate { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المجموع الفرعي")]
        public decimal Subtotal { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ الإجمالي")]
        public decimal TotalAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ المدفوع")]
        public decimal PaidAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ المتبقي")]
        public decimal RemainingAmount { get; set; } = 0;

        [Required]
        [StringLength(20)]
        [Display(Name = "حالة الدفع")]
        public string PaymentStatus { get; set; } = "Pending"; // Pending, Partial, Paid

        [StringLength(1000)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Required]
        [Display(Name = "الشركة")]
        public int CompanyId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "أنشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "عدل بواسطة")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; } = null!;

        [ForeignKey("WarehouseId")]
        public virtual Warehouse Warehouse { get; set; } = null!;

        [ForeignKey("CompanyId")]
        public virtual Company Company { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User CreatedByUser { get; set; } = null!;

        [ForeignKey("ModifiedBy")]
        public virtual User? ModifiedByUser { get; set; }

        // Collections
        public virtual ICollection<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; } = new List<PurchaseInvoiceDetail>();
        public virtual ICollection<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; } = new List<PurchaseInvoiceItem>();
    }
}
