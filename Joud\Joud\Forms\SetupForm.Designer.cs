namespace Joud.Forms
{
    partial class SetupForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.lblSubtitle = new Label();
            
            this.grpDatabase = new GroupBox();
            this.lblServerName = new Label();
            this.txtServerName = new TextBox();
            
            this.grpCompany = new GroupBox();
            this.lblCompanyName = new Label();
            this.txtCompanyName = new TextBox();
            this.lblCompanyNameEn = new Label();
            this.txtCompanyNameEn = new TextBox();
            this.lblAddress = new Label();
            this.txtAddress = new TextBox();
            this.lblPhone = new Label();
            this.txtPhone = new TextBox();
            this.lblEmail = new Label();
            this.txtEmail = new TextBox();
            this.lblCountry = new Label();
            this.txtCountry = new TextBox();
            this.lblCurrency = new Label();
            this.cmbCurrency = new ComboBox();
            
            this.grpUser = new GroupBox();
            this.lblUsername = new Label();
            this.txtUsername = new TextBox();
            this.lblPassword = new Label();
            this.txtPassword = new TextBox();
            this.lblConfirmPassword = new Label();
            this.txtConfirmPassword = new TextBox();
            this.lblFullName = new Label();
            this.txtFullName = new TextBox();
            
            this.pnlButtons = new Panel();
            this.btnSetup = new Button();
            this.btnCancel = new Button();
            this.lblStatus = new Label();

            this.SuspendLayout();

            // 
            // pnlHeader
            // 
            this.pnlHeader.BackColor = Color.FromArgb(41, 128, 185);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Controls.Add(this.lblSubtitle);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Location = new Point(0, 0);
            this.pnlHeader.Name = "pnlHeader";
            this.pnlHeader.Size = new Size(800, 80);
            this.pnlHeader.TabIndex = 0;

            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(300, 30);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "نظام جود للمحاسبة المالية";

            // 
            // lblSubtitle
            // 
            this.lblSubtitle.AutoSize = true;
            this.lblSubtitle.Font = new Font("Segoe UI", 10F);
            this.lblSubtitle.ForeColor = Color.White;
            this.lblSubtitle.Location = new Point(20, 50);
            this.lblSubtitle.Name = "lblSubtitle";
            this.lblSubtitle.Size = new Size(200, 19);
            this.lblSubtitle.TabIndex = 1;
            this.lblSubtitle.Text = "الإعداد الأولي للنظام";

            // 
            // grpDatabase
            // 
            this.grpDatabase.Controls.Add(this.lblServerName);
            this.grpDatabase.Controls.Add(this.txtServerName);
            this.grpDatabase.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpDatabase.Location = new Point(20, 100);
            this.grpDatabase.Name = "grpDatabase";
            this.grpDatabase.Size = new Size(760, 80);
            this.grpDatabase.TabIndex = 1;
            this.grpDatabase.TabStop = false;
            this.grpDatabase.Text = "إعدادات قاعدة البيانات";

            // 
            // lblServerName
            // 
            this.lblServerName.AutoSize = true;
            this.lblServerName.Font = new Font("Segoe UI", 9F);
            this.lblServerName.Location = new Point(650, 30);
            this.lblServerName.Name = "lblServerName";
            this.lblServerName.Size = new Size(80, 15);
            this.lblServerName.TabIndex = 0;
            this.lblServerName.Text = "اسم الخادم:";

            // 
            // txtServerName
            // 
            this.txtServerName.Font = new Font("Segoe UI", 9F);
            this.txtServerName.Location = new Point(20, 27);
            this.txtServerName.Name = "txtServerName";
            this.txtServerName.Size = new Size(620, 23);
            this.txtServerName.TabIndex = 1;
            this.txtServerName.Text = ".\\SQLEXPRESS";

            // 
            // grpCompany
            // 
            this.grpCompany.Controls.Add(this.lblCompanyName);
            this.grpCompany.Controls.Add(this.txtCompanyName);
            this.grpCompany.Controls.Add(this.lblCompanyNameEn);
            this.grpCompany.Controls.Add(this.txtCompanyNameEn);
            this.grpCompany.Controls.Add(this.lblAddress);
            this.grpCompany.Controls.Add(this.txtAddress);
            this.grpCompany.Controls.Add(this.lblPhone);
            this.grpCompany.Controls.Add(this.txtPhone);
            this.grpCompany.Controls.Add(this.lblEmail);
            this.grpCompany.Controls.Add(this.txtEmail);
            this.grpCompany.Controls.Add(this.lblCountry);
            this.grpCompany.Controls.Add(this.txtCountry);
            this.grpCompany.Controls.Add(this.lblCurrency);
            this.grpCompany.Controls.Add(this.cmbCurrency);
            this.grpCompany.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpCompany.Location = new Point(20, 190);
            this.grpCompany.Name = "grpCompany";
            this.grpCompany.Size = new Size(760, 200);
            this.grpCompany.TabIndex = 2;
            this.grpCompany.TabStop = false;
            this.grpCompany.Text = "بيانات الشركة";

            // Company Name
            this.lblCompanyName.AutoSize = true;
            this.lblCompanyName.Font = new Font("Segoe UI", 9F);
            this.lblCompanyName.Location = new Point(650, 30);
            this.lblCompanyName.Name = "lblCompanyName";
            this.lblCompanyName.Size = new Size(80, 15);
            this.lblCompanyName.TabIndex = 0;
            this.lblCompanyName.Text = "اسم الشركة:";

            this.txtCompanyName.Font = new Font("Segoe UI", 9F);
            this.txtCompanyName.Location = new Point(400, 27);
            this.txtCompanyName.Name = "txtCompanyName";
            this.txtCompanyName.Size = new Size(240, 23);
            this.txtCompanyName.TabIndex = 1;

            // Company Name English
            this.lblCompanyNameEn.AutoSize = true;
            this.lblCompanyNameEn.Font = new Font("Segoe UI", 9F);
            this.lblCompanyNameEn.Location = new Point(280, 30);
            this.lblCompanyNameEn.Name = "lblCompanyNameEn";
            this.lblCompanyNameEn.Size = new Size(100, 15);
            this.lblCompanyNameEn.TabIndex = 2;
            this.lblCompanyNameEn.Text = "الاسم بالإنجليزية:";

            this.txtCompanyNameEn.Font = new Font("Segoe UI", 9F);
            this.txtCompanyNameEn.Location = new Point(20, 27);
            this.txtCompanyNameEn.Name = "txtCompanyNameEn";
            this.txtCompanyNameEn.Size = new Size(250, 23);
            this.txtCompanyNameEn.TabIndex = 3;

            // Address
            this.lblAddress.AutoSize = true;
            this.lblAddress.Font = new Font("Segoe UI", 9F);
            this.lblAddress.Location = new Point(650, 65);
            this.lblAddress.Name = "lblAddress";
            this.lblAddress.Size = new Size(50, 15);
            this.lblAddress.TabIndex = 4;
            this.lblAddress.Text = "العنوان:";

            this.txtAddress.Font = new Font("Segoe UI", 9F);
            this.txtAddress.Location = new Point(20, 62);
            this.txtAddress.Name = "txtAddress";
            this.txtAddress.Size = new Size(620, 23);
            this.txtAddress.TabIndex = 5;

            // Phone and Email
            this.lblPhone.AutoSize = true;
            this.lblPhone.Font = new Font("Segoe UI", 9F);
            this.lblPhone.Location = new Point(650, 100);
            this.lblPhone.Name = "lblPhone";
            this.lblPhone.Size = new Size(50, 15);
            this.lblPhone.TabIndex = 6;
            this.lblPhone.Text = "الهاتف:";

            this.txtPhone.Font = new Font("Segoe UI", 9F);
            this.txtPhone.Location = new Point(400, 97);
            this.txtPhone.Name = "txtPhone";
            this.txtPhone.Size = new Size(240, 23);
            this.txtPhone.TabIndex = 7;

            this.lblEmail.AutoSize = true;
            this.lblEmail.Font = new Font("Segoe UI", 9F);
            this.lblEmail.Location = new Point(280, 100);
            this.lblEmail.Name = "lblEmail";
            this.lblEmail.Size = new Size(100, 15);
            this.lblEmail.TabIndex = 8;
            this.lblEmail.Text = "البريد الإلكتروني:";

            this.txtEmail.Font = new Font("Segoe UI", 9F);
            this.txtEmail.Location = new Point(20, 97);
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.Size = new Size(250, 23);
            this.txtEmail.TabIndex = 9;

            // Country and Currency
            this.lblCountry.AutoSize = true;
            this.lblCountry.Font = new Font("Segoe UI", 9F);
            this.lblCountry.Location = new Point(650, 135);
            this.lblCountry.Name = "lblCountry";
            this.lblCountry.Size = new Size(40, 15);
            this.lblCountry.TabIndex = 10;
            this.lblCountry.Text = "البلد:";

            this.txtCountry.Font = new Font("Segoe UI", 9F);
            this.txtCountry.Location = new Point(400, 132);
            this.txtCountry.Name = "txtCountry";
            this.txtCountry.Size = new Size(240, 23);
            this.txtCountry.TabIndex = 11;
            this.txtCountry.Text = "المملكة العربية السعودية";

            this.lblCurrency.AutoSize = true;
            this.lblCurrency.Font = new Font("Segoe UI", 9F);
            this.lblCurrency.Location = new Point(280, 135);
            this.lblCurrency.Name = "lblCurrency";
            this.lblCurrency.Size = new Size(50, 15);
            this.lblCurrency.TabIndex = 12;
            this.lblCurrency.Text = "العملة:";

            this.cmbCurrency.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCurrency.Font = new Font("Segoe UI", 9F);
            this.cmbCurrency.FormattingEnabled = true;
            this.cmbCurrency.Items.AddRange(new object[] { "SAR", "USD", "EUR", "AED", "EGP", "JOD", "KWD" });
            this.cmbCurrency.Location = new Point(20, 132);
            this.cmbCurrency.Name = "cmbCurrency";
            this.cmbCurrency.Size = new Size(250, 23);
            this.cmbCurrency.TabIndex = 13;
            this.cmbCurrency.SelectedIndex = 0;

            // 
            // grpUser
            // 
            this.grpUser.Controls.Add(this.lblUsername);
            this.grpUser.Controls.Add(this.txtUsername);
            this.grpUser.Controls.Add(this.lblPassword);
            this.grpUser.Controls.Add(this.txtPassword);
            this.grpUser.Controls.Add(this.lblConfirmPassword);
            this.grpUser.Controls.Add(this.txtConfirmPassword);
            this.grpUser.Controls.Add(this.lblFullName);
            this.grpUser.Controls.Add(this.txtFullName);
            this.grpUser.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpUser.Location = new Point(20, 400);
            this.grpUser.Name = "grpUser";
            this.grpUser.Size = new Size(760, 120);
            this.grpUser.TabIndex = 3;
            this.grpUser.TabStop = false;
            this.grpUser.Text = "بيانات المستخدم الرئيسي";

            // Username and Full Name
            this.lblUsername.AutoSize = true;
            this.lblUsername.Font = new Font("Segoe UI", 9F);
            this.lblUsername.Location = new Point(650, 30);
            this.lblUsername.Name = "lblUsername";
            this.lblUsername.Size = new Size(80, 15);
            this.lblUsername.TabIndex = 0;
            this.lblUsername.Text = "اسم المستخدم:";

            this.txtUsername.Font = new Font("Segoe UI", 9F);
            this.txtUsername.Location = new Point(400, 27);
            this.txtUsername.Name = "txtUsername";
            this.txtUsername.Size = new Size(240, 23);
            this.txtUsername.TabIndex = 1;

            this.lblFullName.AutoSize = true;
            this.lblFullName.Font = new Font("Segoe UI", 9F);
            this.lblFullName.Location = new Point(280, 30);
            this.lblFullName.Name = "lblFullName";
            this.lblFullName.Size = new Size(80, 15);
            this.lblFullName.TabIndex = 2;
            this.lblFullName.Text = "الاسم الكامل:";

            this.txtFullName.Font = new Font("Segoe UI", 9F);
            this.txtFullName.Location = new Point(20, 27);
            this.txtFullName.Name = "txtFullName";
            this.txtFullName.Size = new Size(250, 23);
            this.txtFullName.TabIndex = 3;

            // Password and Confirm Password
            this.lblPassword.AutoSize = true;
            this.lblPassword.Font = new Font("Segoe UI", 9F);
            this.lblPassword.Location = new Point(650, 65);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new Size(70, 15);
            this.lblPassword.TabIndex = 4;
            this.lblPassword.Text = "كلمة المرور:";

            this.txtPassword.Font = new Font("Segoe UI", 9F);
            this.txtPassword.Location = new Point(400, 62);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.PasswordChar = '*';
            this.txtPassword.Size = new Size(240, 23);
            this.txtPassword.TabIndex = 5;

            this.lblConfirmPassword.AutoSize = true;
            this.lblConfirmPassword.Font = new Font("Segoe UI", 9F);
            this.lblConfirmPassword.Location = new Point(280, 65);
            this.lblConfirmPassword.Name = "lblConfirmPassword";
            this.lblConfirmPassword.Size = new Size(100, 15);
            this.lblConfirmPassword.TabIndex = 6;
            this.lblConfirmPassword.Text = "تأكيد كلمة المرور:";

            this.txtConfirmPassword.Font = new Font("Segoe UI", 9F);
            this.txtConfirmPassword.Location = new Point(20, 62);
            this.txtConfirmPassword.Name = "txtConfirmPassword";
            this.txtConfirmPassword.PasswordChar = '*';
            this.txtConfirmPassword.Size = new Size(250, 23);
            this.txtConfirmPassword.TabIndex = 7;

            // 
            // pnlButtons
            // 
            this.pnlButtons.Controls.Add(this.btnSetup);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.lblStatus);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Location = new Point(0, 540);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Size = new Size(800, 60);
            this.pnlButtons.TabIndex = 4;

            // 
            // btnSetup
            // 
            this.btnSetup.BackColor = Color.FromArgb(39, 174, 96);
            this.btnSetup.FlatStyle = FlatStyle.Flat;
            this.btnSetup.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSetup.ForeColor = Color.White;
            this.btnSetup.Location = new Point(680, 15);
            this.btnSetup.Name = "btnSetup";
            this.btnSetup.Size = new Size(100, 35);
            this.btnSetup.TabIndex = 0;
            this.btnSetup.Text = "إعداد النظام";
            this.btnSetup.UseVisualStyleBackColor = false;
            this.btnSetup.Click += new EventHandler(this.btnSetup_Click);

            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(570, 15);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new Font("Segoe UI", 9F);
            this.lblStatus.Location = new Point(20, 25);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(200, 15);
            this.lblStatus.TabIndex = 2;
            this.lblStatus.Text = "جاهز للإعداد...";

            // 
            // SetupForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.grpDatabase);
            this.Controls.Add(this.grpCompany);
            this.Controls.Add(this.grpUser);
            this.Controls.Add(this.pnlButtons);
            this.Name = "SetupForm";
            this.Text = "إعداد نظام جود للمحاسبة المالية";
            this.ResumeLayout(false);
        }

        #endregion

        private Panel pnlHeader;
        private Label lblTitle;
        private Label lblSubtitle;
        private GroupBox grpDatabase;
        private Label lblServerName;
        private TextBox txtServerName;
        private GroupBox grpCompany;
        private Label lblCompanyName;
        private TextBox txtCompanyName;
        private Label lblCompanyNameEn;
        private TextBox txtCompanyNameEn;
        private Label lblAddress;
        private TextBox txtAddress;
        private Label lblPhone;
        private TextBox txtPhone;
        private Label lblEmail;
        private TextBox txtEmail;
        private Label lblCountry;
        private TextBox txtCountry;
        private Label lblCurrency;
        private ComboBox cmbCurrency;
        private GroupBox grpUser;
        private Label lblUsername;
        private TextBox txtUsername;
        private Label lblPassword;
        private TextBox txtPassword;
        private Label lblConfirmPassword;
        private TextBox txtConfirmPassword;
        private Label lblFullName;
        private TextBox txtFullName;
        private Panel pnlButtons;
        private Button btnSetup;
        private Button btnCancel;
        private Label lblStatus;
    }
}
