using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة الفئات الفرعية
    /// Sub Categories Management Form
    /// </summary>
    public partial class SubCategoriesForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly SubCategoryService _subCategoryService;
        private List<SubCategory> _subCategories;
        private List<MainCategory> _mainCategories;
        private SubCategory? _selectedSubCategory;
        private bool _isEditing = false;

        public SubCategoriesForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _subCategoryService = new SubCategoryService();
            _subCategories = new List<SubCategory>();
            _mainCategories = new List<MainCategory>();
            
            InitializeFormSettings();
            SetupDataGridView();
            LoadDataAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة الفئات الفرعية - نظام جود للمحاسبة المالية";
            this.Size = new Size(1100, 650);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private void SetupDataGridView()
        {
            // إعداد DataGridView
            dgvSubCategories.AutoGenerateColumns = false;
            dgvSubCategories.AllowUserToAddRows = false;
            dgvSubCategories.AllowUserToDeleteRows = false;
            dgvSubCategories.ReadOnly = true;
            dgvSubCategories.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSubCategories.MultiSelect = false;
            dgvSubCategories.BackgroundColor = Color.White;
            dgvSubCategories.BorderStyle = BorderStyle.None;
            dgvSubCategories.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvSubCategories.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvSubCategories.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvSubCategories.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvSubCategories.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSubCategories.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvSubCategories.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvSubCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategoryCode",
                HeaderText = "كود الفئة",
                DataPropertyName = "CategoryCode",
                Width = 100,
                ReadOnly = true
            });

            dgvSubCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategoryName",
                HeaderText = "اسم الفئة الفرعية",
                DataPropertyName = "CategoryName",
                Width = 180,
                ReadOnly = true
            });

            dgvSubCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MainCategoryName",
                HeaderText = "الفئة الرئيسية",
                Width = 150,
                ReadOnly = true
            });

            dgvSubCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategoryNameEn",
                HeaderText = "الاسم بالإنجليزية",
                DataPropertyName = "CategoryNameEn",
                Width = 150,
                ReadOnly = true
            });

            dgvSubCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200,
                ReadOnly = true
            });

            dgvSubCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductsCount",
                HeaderText = "عدد الأصناف",
                Width = 100,
                ReadOnly = true
            });

            dgvSubCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            // أحداث DataGridView
            dgvSubCategories.SelectionChanged += DgvSubCategories_SelectionChanged;
            dgvSubCategories.CellDoubleClick += DgvSubCategories_CellDoubleClick;
            dgvSubCategories.DataBindingComplete += DgvSubCategories_DataBindingComplete;
        }

        private async void LoadDataAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل البيانات...";
                lblStatus.ForeColor = Color.Blue;

                // تحميل الفئات الرئيسية
                _mainCategories = await _subCategoryService.GetMainCategoriesForSelectionAsync(_currentCompany.CompanyId);
                
                // إعداد ComboBox للفئات الرئيسية
                cmbMainCategory.DataSource = _mainCategories;
                cmbMainCategory.DisplayMember = "CategoryName";
                cmbMainCategory.ValueMember = "MainCategoryId";
                cmbMainCategory.SelectedIndex = -1;

                // تحميل الفئات الفرعية
                await LoadSubCategoriesAsync();

                lblStatus.Text = $"تم تحميل {_subCategories.Count} فئة فرعية";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private async Task LoadSubCategoriesAsync()
        {
            try
            {
                _subCategories = await _subCategoryService.GetAllSubCategoriesAsync(_currentCompany.CompanyId);
                dgvSubCategories.DataSource = _subCategories;

                // تحديث الإحصائيات
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في تحميل الفئات الفرعية: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية: {ex.InnerException.Message}";

                    // التحقق من أخطاء الأعمدة المفقودة
                    if (ex.InnerException.Message.Contains("Invalid column name"))
                    {
                        errorMessage += "\n\nيبدو أن هناك أعمدة مفقودة في قاعدة البيانات. تأكد من تنفيذ ملفات SQL المحدثة.";
                    }
                }

                MessageBox.Show(errorMessage, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "فشل في تحميل البيانات";
                lblStatus.ForeColor = Color.Red;

                // إنشاء قائمة فارغة لتجنب أخطاء أخرى
                _subCategories = new List<SubCategory>();
                dgvSubCategories.DataSource = _subCategories;
            }
        }

        private void DgvSubCategories_DataBindingComplete(object? sender, DataGridViewBindingCompleteEventArgs e)
        {
            // تحديث البيانات المحسوبة
            foreach (DataGridViewRow row in dgvSubCategories.Rows)
            {
                if (row.DataBoundItem is SubCategory subCategory)
                {
                    row.Cells["MainCategoryName"].Value = subCategory.MainCategory?.CategoryName ?? "";
                    row.Cells["ProductsCount"].Value = subCategory.Products?.Count ?? 0;
                }
            }
        }

        private void UpdateStatistics()
        {
            var totalSubCategories = _subCategories.Count;
            var categoriesWithProducts = _subCategories.Count(sc => sc.Products.Any());
            var totalProducts = _subCategories.Sum(sc => sc.Products.Count);
            var mainCategoriesWithSubs = _subCategories.Select(sc => sc.MainCategoryId).Distinct().Count();

            lblTotalSubCategories.Text = $"إجمالي الفئات الفرعية: {totalSubCategories}";
            lblCategoriesWithProducts.Text = $"فئات تحتوي على أصناف: {categoriesWithProducts}";
            lblTotalProducts.Text = $"إجمالي الأصناف: {totalProducts}";
            lblMainCategoriesWithSubs.Text = $"فئات رئيسية لها فئات فرعية: {mainCategoriesWithSubs}";
        }

        private void DgvSubCategories_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvSubCategories.SelectedRows.Count > 0)
            {
                _selectedSubCategory = dgvSubCategories.SelectedRows[0].DataBoundItem as SubCategory;
                LoadSubCategoryDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
            }
            else
            {
                _selectedSubCategory = null;
                ClearSubCategoryDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void DgvSubCategories_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadSubCategoryDetails()
        {
            if (_selectedSubCategory == null) return;

            txtCategoryCode.Text = _selectedSubCategory.CategoryCode;
            txtCategoryName.Text = _selectedSubCategory.CategoryName;
            txtCategoryNameEn.Text = _selectedSubCategory.CategoryNameEn;
            txtDescription.Text = _selectedSubCategory.Description;
            
            // اختيار الفئة الرئيسية
            cmbMainCategory.SelectedValue = _selectedSubCategory.MainCategoryId;
            
            lblProductsCount.Text = $"عدد الأصناف: {_selectedSubCategory.Products?.Count ?? 0}";
        }

        private void ClearSubCategoryDetails()
        {
            txtCategoryCode.Text = "سيتم إنشاؤه تلقائياً";
            txtCategoryName.Clear();
            txtCategoryNameEn.Clear();
            txtDescription.Clear();
            cmbMainCategory.SelectedIndex = -1;
            lblProductsCount.Text = "عدد الأصناف: 0";

            // إعادة تعيين لون النص
            lblStatus.Text = "جاهز لإدخال بيانات فئة فرعية جديدة";
            lblStatus.ForeColor = Color.Blue;
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                if (_mainCategories.Count == 0)
                {
                    ShowError("يجب إنشاء فئة رئيسية واحدة على الأقل أولاً");
                    return;
                }

                _isEditing = false;
                _selectedSubCategory = null;
                ClearSubCategoryDetails();
                
                SetEditMode(true);
                cmbMainCategory.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء فئة فرعية جديدة: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedSubCategory == null)
            {
                ShowError("يرجى اختيار فئة فرعية للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            txtCategoryName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ بيانات الفئة الفرعية...";
                lblStatus.ForeColor = Color.Blue;

                var subCategory = CreateSubCategoryFromInput();

                if (_isEditing && _selectedSubCategory != null)
                {
                    subCategory.SubCategoryId = _selectedSubCategory.SubCategoryId;
                    subCategory.CreatedDate = _selectedSubCategory.CreatedDate;
                    subCategory.CreatedBy = _selectedSubCategory.CreatedBy;
                    subCategory.ModifiedBy = _currentUser.UserId;

                    await _subCategoryService.UpdateSubCategoryAsync(subCategory);
                    lblStatus.Text = "تم تحديث بيانات الفئة الفرعية بنجاح";
                }
                else
                {
                    // إنشاء كود تلقائي
                    if (string.IsNullOrEmpty(subCategory.CategoryCode))
                    {
                        subCategory.CategoryCode = await _subCategoryService.GenerateSubCategoryCodeAsync(
                            subCategory.MainCategoryId, _currentCompany.CompanyId);

                        // تحديث الحقل في الواجهة
                        txtCategoryCode.Text = subCategory.CategoryCode;
                    }

                    subCategory.CreatedBy = _currentUser.UserId;
                    subCategory.CompanyId = _currentCompany.CompanyId;

                    await _subCategoryService.AddSubCategoryAsync(subCategory);
                    lblStatus.Text = "تم إضافة الفئة الفرعية بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadSubCategoriesAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ بيانات الفئة الفرعية: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedSubCategory != null)
            {
                LoadSubCategoryDetails();
            }
            else
            {
                ClearSubCategoryDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedSubCategory == null)
            {
                ShowError("يرجى اختيار فئة فرعية للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف الفئة الفرعية '{_selectedSubCategory.CategoryName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف الفئة الفرعية...";
                    lblStatus.ForeColor = Color.Blue;

                    await _subCategoryService.DeleteSubCategoryAsync(_selectedSubCategory.SubCategoryId, _currentUser.UserId);
                    
                    lblStatus.Text = "تم حذف الفئة الفرعية بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadSubCategoriesAsync();
                    ClearSubCategoryDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف الفئة الفرعية: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvSubCategories.DataSource = _subCategories;
                }
                else
                {
                    var searchResults = await _subCategoryService.SearchSubCategoriesAsync(txtSearch.Text, _currentCompany.CompanyId);
                    dgvSubCategories.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataAsync();
        }

        private async void cmbMainCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            // تحديث كود الفئة الفرعية عند تغيير الفئة الرئيسية (في حالة الإضافة الجديدة)
            if (!_isEditing && cmbMainCategory.SelectedValue != null && cmbMainCategory.SelectedValue is int)
            {
                try
                {
                    // إنشاء كود تلقائي وعرضه
                    int mainCategoryId = (int)cmbMainCategory.SelectedValue;

                    // التأكد من أن المعرف صحيح
                    if (mainCategoryId > 0)
                    {
                        string newCode = await _subCategoryService.GenerateSubCategoryCodeAsync(
                            mainCategoryId, _currentCompany.CompanyId);
                        txtCategoryCode.Text = newCode;

                        lblStatus.Text = $"تم إنشاء الكود: {newCode}";
                        lblStatus.ForeColor = Color.Green;
                    }
                    else
                    {
                        txtCategoryCode.Text = "اختر الفئة الرئيسية أولاً";
                    }
                }
                catch (Exception ex)
                {
                    txtCategoryCode.Text = "سيتم إنشاؤه تلقائياً";
                    lblStatus.Text = $"خطأ في إنشاء الكود: {ex.Message}";
                    lblStatus.ForeColor = Color.Red;

                    // إضافة تفاصيل إضافية للخطأ
                    if (ex.InnerException != null)
                    {
                        lblStatus.Text += $" - {ex.InnerException.Message}";
                    }
                }
            }
            else if (!_isEditing)
            {
                txtCategoryCode.Text = "اختر الفئة الرئيسية أولاً";
            }
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtCategoryName.Text))
                errors.Add("اسم الفئة مطلوب");

            if (cmbMainCategory.SelectedValue == null)
                errors.Add("يجب اختيار الفئة الرئيسية");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private SubCategory CreateSubCategoryFromInput()
        {
            return new SubCategory
            {
                CategoryCode = txtCategoryCode.Text.Trim(),
                CategoryName = txtCategoryName.Text.Trim(),
                CategoryNameEn = txtCategoryNameEn.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                MainCategoryId = (int)cmbMainCategory.SelectedValue!
            };
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال
            txtCategoryCode.ReadOnly = true; // الكود دائماً للقراءة فقط (يتم إنشاؤه تلقائياً)
            txtCategoryName.ReadOnly = !isEditing;
            txtCategoryNameEn.ReadOnly = !isEditing;
            txtDescription.ReadOnly = !isEditing;
            cmbMainCategory.Enabled = isEditing;

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedSubCategory != null;
            btnDelete.Enabled = !isEditing && _selectedSubCategory != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            dgvSubCategories.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is ComboBox || control is DataGridView)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void SubCategoriesForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
        }
    }
}
