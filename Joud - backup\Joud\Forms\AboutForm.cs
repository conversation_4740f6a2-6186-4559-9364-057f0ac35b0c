using System;
using System.Diagnostics;
using System.Reflection;
using System.Windows.Forms;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة حول البرنامج - About Form
    /// تعرض معلومات عن النظام والمطور والإصدار
    /// </summary>
    public partial class AboutForm : Form
    {
        #region Constructor

        /// <summary>
        /// منشئ شاشة حول البرنامج
        /// </summary>
        public AboutForm()
        {
            InitializeComponent();
            LoadApplicationInfo();
            LoadSystemInfo();
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل الشاشة
        /// </summary>
        private void AboutForm_Load(object sender, EventArgs e)
        {
            try
            {
                // تحديث معلومات النظام
                UpdateSystemInfo();
                
                // تحديد موضع الشاشة في المنتصف
                CenterToParent();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل شاشة حول البرنامج: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Button Events

        /// <summary>
        /// حدث النقر على زر موافق
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إغلاق الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر معلومات النظام
        /// </summary>
        private void btnSystemInfo_Click(object sender, EventArgs e)
        {
            try
            {
                // عرض معلومات النظام التفصيلية
                ShowDetailedSystemInfo();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض معلومات النظام: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تحميل معلومات التطبيق
        /// </summary>
        private void LoadApplicationInfo()
        {
            try
            {
                // الحصول على معلومات التطبيق
                Assembly assembly = Assembly.GetExecutingAssembly();
                FileVersionInfo versionInfo = FileVersionInfo.GetVersionInfo(assembly.Location);

                // تحديث معلومات التطبيق
                lblApplicationName.Text = "نظام جود للمحاسبة المالية";
                lblVersion.Text = $"الإصدار {versionInfo.FileVersion ?? "1.0.0"}";
                lblCopyright.Text = "© 2024 Joud Systems. جميع الحقوق محفوظة.";
                
                // وصف التطبيق
                lblDescription.Text = "نظام محاسبة مالية متكامل مصمم خصيصاً للشركات الصغيرة والمتوسطة. " +
                    "يوفر النظام إدارة شاملة للمبيعات والمشتريات والمخزون والحسابات المالية " +
                    "مع دعم كامل للغة العربية وواجهة مستخدم حديثة وسهلة الاستخدام.";

                // معلومات المطور
                lblDeveloper.Text = "المطور: Augment Agent";
                lblEmail.Text = "البريد: <EMAIL>";
                lblWebsite.Text = "الموقع: www.joudsystems.com";
                lblPhone.Text = "الهاتف: +966-XX-XXX-XXXX";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات التطبيق: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// تحميل معلومات النظام
        /// </summary>
        private void LoadSystemInfo()
        {
            try
            {
                // معلومات النظام الأساسية
                lblFramework.Text = $".NET Framework: {Environment.Version}";
                lblDatabase.Text = "قاعدة البيانات: SQL Server";
                lblOperatingSystem.Text = $"نظام التشغيل: {Environment.OSVersion.Platform}";
                
                // معلومات الذاكرة
                long totalMemory = GC.GetTotalMemory(false);
                lblMemory.Text = $"الذاكرة المستخدمة: {totalMemory / (1024 * 1024)} MB";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات النظام: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// تحديث معلومات النظام
        /// </summary>
        private void UpdateSystemInfo()
        {
            try
            {
                // تحديث معلومات الذاكرة
                long totalMemory = GC.GetTotalMemory(false);
                lblMemory.Text = $"الذاكرة المستخدمة: {totalMemory / (1024 * 1024)} MB";

                // تحديث معلومات نظام التشغيل
                lblOperatingSystem.Text = $"نظام التشغيل: {Environment.OSVersion.VersionString}";
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في التحديث
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث معلومات النظام: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض معلومات النظام التفصيلية
        /// </summary>
        private void ShowDetailedSystemInfo()
        {
            try
            {
                string systemInfo = GetDetailedSystemInfo();
                
                // عرض معلومات النظام في نافذة منفصلة
                MessageBox.Show(systemInfo, "معلومات النظام التفصيلية", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض معلومات النظام التفصيلية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على معلومات النظام التفصيلية
        /// </summary>
        /// <returns>نص يحتوي على معلومات النظام</returns>
        private string GetDetailedSystemInfo()
        {
            try
            {
                var info = new System.Text.StringBuilder();
                
                info.AppendLine("=== معلومات النظام التفصيلية ===");
                info.AppendLine();
                
                // معلومات التطبيق
                info.AppendLine("معلومات التطبيق:");
                info.AppendLine($"  الاسم: {Application.ProductName}");
                info.AppendLine($"  الإصدار: {Application.ProductVersion}");
                info.AppendLine($"  الشركة: {Application.CompanyName}");
                info.AppendLine();
                
                // معلومات النظام
                info.AppendLine("معلومات النظام:");
                info.AppendLine($"  نظام التشغيل: {Environment.OSVersion}");
                info.AppendLine($"  المعالج: {Environment.ProcessorCount} cores");
                info.AppendLine($"  اسم الجهاز: {Environment.MachineName}");
                info.AppendLine($"  اسم المستخدم: {Environment.UserName}");
                info.AppendLine($"  مجلد النظام: {Environment.SystemDirectory}");
                info.AppendLine();
                
                // معلومات .NET
                info.AppendLine("معلومات .NET:");
                info.AppendLine($"  إصدار .NET: {Environment.Version}");
                info.AppendLine($"  CLR Version: {Environment.Version}");
                info.AppendLine();
                
                // معلومات الذاكرة
                info.AppendLine("معلومات الذاكرة:");
                info.AppendLine($"  الذاكرة المستخدمة: {GC.GetTotalMemory(false) / (1024 * 1024)} MB");
                info.AppendLine($"  Working Set: {Environment.WorkingSet / (1024 * 1024)} MB");
                info.AppendLine();
                
                // معلومات التاريخ والوقت
                info.AppendLine("معلومات التاريخ والوقت:");
                info.AppendLine($"  التاريخ والوقت الحالي: {DateTime.Now}");
                info.AppendLine($"  وقت تشغيل النظام: {Environment.TickCount / 1000} ثانية");
                
                return info.ToString();
            }
            catch (Exception ex)
            {
                return $"خطأ في الحصول على معلومات النظام: {ex.Message}";
            }
        }

        /// <summary>
        /// تحميل صورة التطبيق
        /// </summary>
        private void LoadApplicationIcon()
        {
            try
            {
                // محاولة تحميل أيقونة التطبيق
                if (this.Icon != null)
                {
                    pictureBox1.Image = this.Icon.ToBitmap();
                }
                else
                {
                    // استخدام صورة افتراضية إذا لم تكن هناك أيقونة
                    pictureBox1.BackColor = System.Drawing.Color.FromArgb(52, 152, 219);
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء تحميل الصورة
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل أيقونة التطبيق: {ex.Message}");
                pictureBox1.BackColor = System.Drawing.Color.FromArgb(52, 152, 219);
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// عرض شاشة حول البرنامج
        /// </summary>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>نتيجة الحوار</returns>
        public static DialogResult ShowAbout(IWin32Window parent = null)
        {
            try
            {
                using (var aboutForm = new AboutForm())
                {
                    return aboutForm.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض شاشة حول البرنامج: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        #endregion
    }
}
