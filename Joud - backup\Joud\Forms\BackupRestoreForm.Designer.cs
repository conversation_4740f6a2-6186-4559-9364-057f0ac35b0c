namespace Joud.Forms
{
    partial class BackupRestoreForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;



        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabBackup = new System.Windows.Forms.TabPage();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnBrowseBackup = new System.Windows.Forms.Button();
            this.txtBackupPath = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.chkCompressBackup = new System.Windows.Forms.CheckBox();
            this.chkIncludeData = new System.Windows.Forms.CheckBox();
            this.chkIncludeSchema = new System.Windows.Forms.CheckBox();
            this.btnCreateBackup = new System.Windows.Forms.Button();
            this.tabRestore = new System.Windows.Forms.TabPage();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.btnBrowseRestore = new System.Windows.Forms.Button();
            this.txtRestorePath = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.chkOverwriteExisting = new System.Windows.Forms.CheckBox();
            this.btnRestoreBackup = new System.Windows.Forms.Button();
            this.tabHistory = new System.Windows.Forms.TabPage();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.dgvBackupHistory = new System.Windows.Forms.DataGridView();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnRefreshHistory = new System.Windows.Forms.Button();
            this.btnDeleteBackup = new System.Windows.Forms.Button();
            this.panel2 = new System.Windows.Forms.Panel();
            this.lblCurrentOperation = new System.Windows.Forms.Label();
            this.progressBar1 = new System.Windows.Forms.ProgressBar();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.lblStatus = new System.Windows.Forms.ToolStripStatusLabel();
            this.panel3 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.saveFileDialog1 = new System.Windows.Forms.SaveFileDialog();
            this.openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
            this.tabControl1.SuspendLayout();
            this.tabBackup.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabRestore.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.tabHistory.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvBackupHistory)).BeginInit();
            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.panel3.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabBackup);
            this.tabControl1.Controls.Add(this.tabRestore);
            this.tabControl1.Controls.Add(this.tabHistory);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.tabControl1.Location = new System.Drawing.Point(0, 80);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.tabControl1.RightToLeftLayout = true;
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(800, 498);
            this.tabControl1.TabIndex = 0;
            // 
            // tabBackup
            // 
            this.tabBackup.Controls.Add(this.groupBox1);
            this.tabBackup.Location = new System.Drawing.Point(4, 24);
            this.tabBackup.Name = "tabBackup";
            this.tabBackup.Padding = new System.Windows.Forms.Padding(3);
            this.tabBackup.Size = new System.Drawing.Size(792, 470);
            this.tabBackup.TabIndex = 0;
            this.tabBackup.Text = "إنشاء نسخة احتياطية";
            this.tabBackup.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnBrowseBackup);
            this.groupBox1.Controls.Add(this.txtBackupPath);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.chkCompressBackup);
            this.groupBox1.Controls.Add(this.chkIncludeData);
            this.groupBox1.Controls.Add(this.chkIncludeSchema);
            this.groupBox1.Controls.Add(this.btnCreateBackup);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(786, 464);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "إعدادات النسخة الاحتياطية";
            // 
            // btnBrowseBackup
            // 
            this.btnBrowseBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnBrowseBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnBrowseBackup.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.btnBrowseBackup.ForeColor = System.Drawing.Color.White;
            this.btnBrowseBackup.Location = new System.Drawing.Point(50, 80);
            this.btnBrowseBackup.Name = "btnBrowseBackup";
            this.btnBrowseBackup.Size = new System.Drawing.Size(80, 30);
            this.btnBrowseBackup.TabIndex = 6;
            this.btnBrowseBackup.Text = "استعراض";
            this.btnBrowseBackup.UseVisualStyleBackColor = false;
            this.btnBrowseBackup.Click += new System.EventHandler(this.btnBrowseBackup_Click);
            // 
            // txtBackupPath
            // 
            this.txtBackupPath.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.txtBackupPath.Location = new System.Drawing.Point(150, 83);
            this.txtBackupPath.Name = "txtBackupPath";
            this.txtBackupPath.ReadOnly = true;
            this.txtBackupPath.Size = new System.Drawing.Size(450, 23);
            this.txtBackupPath.TabIndex = 5;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.label2.Location = new System.Drawing.Point(606, 86);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(74, 15);
            this.label2.TabIndex = 4;
            this.label2.Text = "مسار الحفظ:";
            // 
            // chkCompressBackup
            // 
            this.chkCompressBackup.AutoSize = true;
            this.chkCompressBackup.Checked = true;
            this.chkCompressBackup.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkCompressBackup.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.chkCompressBackup.Location = new System.Drawing.Point(50, 200);
            this.chkCompressBackup.Name = "chkCompressBackup";
            this.chkCompressBackup.Size = new System.Drawing.Size(108, 19);
            this.chkCompressBackup.TabIndex = 3;
            this.chkCompressBackup.Text = "ضغط النسخة";
            this.chkCompressBackup.UseVisualStyleBackColor = true;
            // 
            // chkIncludeData
            // 
            this.chkIncludeData.AutoSize = true;
            this.chkIncludeData.Checked = true;
            this.chkIncludeData.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkIncludeData.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.chkIncludeData.Location = new System.Drawing.Point(50, 160);
            this.chkIncludeData.Name = "chkIncludeData";
            this.chkIncludeData.Size = new System.Drawing.Size(109, 19);
            this.chkIncludeData.TabIndex = 2;
            this.chkIncludeData.Text = "تضمين البيانات";
            this.chkIncludeData.UseVisualStyleBackColor = true;
            // 
            // chkIncludeSchema
            // 
            this.chkIncludeSchema.AutoSize = true;
            this.chkIncludeSchema.Checked = true;
            this.chkIncludeSchema.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkIncludeSchema.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.chkIncludeSchema.Location = new System.Drawing.Point(50, 120);
            this.chkIncludeSchema.Name = "chkIncludeSchema";
            this.chkIncludeSchema.Size = new System.Drawing.Size(118, 19);
            this.chkIncludeSchema.TabIndex = 1;
            this.chkIncludeSchema.Text = "تضمين الهيكل";
            this.chkIncludeSchema.UseVisualStyleBackColor = true;
            // 
            // btnCreateBackup
            // 
            this.btnCreateBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnCreateBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCreateBackup.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnCreateBackup.ForeColor = System.Drawing.Color.White;
            this.btnCreateBackup.Location = new System.Drawing.Point(300, 300);
            this.btnCreateBackup.Name = "btnCreateBackup";
            this.btnCreateBackup.Size = new System.Drawing.Size(200, 50);
            this.btnCreateBackup.TabIndex = 0;
            this.btnCreateBackup.Text = "إنشاء نسخة احتياطية";
            this.btnCreateBackup.UseVisualStyleBackColor = false;
            this.btnCreateBackup.Click += new System.EventHandler(this.btnCreateBackup_Click);
            // 
            // tabRestore
            // 
            this.tabRestore.Controls.Add(this.groupBox2);
            this.tabRestore.Location = new System.Drawing.Point(4, 24);
            this.tabRestore.Name = "tabRestore";
            this.tabRestore.Padding = new System.Windows.Forms.Padding(3);
            this.tabRestore.Size = new System.Drawing.Size(792, 470);
            this.tabRestore.TabIndex = 1;
            this.tabRestore.Text = "استعادة نسخة احتياطية";
            this.tabRestore.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.btnBrowseRestore);
            this.groupBox2.Controls.Add(this.txtRestorePath);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.chkOverwriteExisting);
            this.groupBox2.Controls.Add(this.btnRestoreBackup);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.groupBox2.Location = new System.Drawing.Point(3, 3);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(786, 464);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "إعدادات الاستعادة";
            // 
            // btnBrowseRestore
            // 
            this.btnBrowseRestore.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnBrowseRestore.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnBrowseRestore.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.btnBrowseRestore.ForeColor = System.Drawing.Color.White;
            this.btnBrowseRestore.Location = new System.Drawing.Point(50, 80);
            this.btnBrowseRestore.Name = "btnBrowseRestore";
            this.btnBrowseRestore.Size = new System.Drawing.Size(80, 30);
            this.btnBrowseRestore.TabIndex = 4;
            this.btnBrowseRestore.Text = "استعراض";
            this.btnBrowseRestore.UseVisualStyleBackColor = false;
            this.btnBrowseRestore.Click += new System.EventHandler(this.btnBrowseRestore_Click);
            // 
            // txtRestorePath
            // 
            this.txtRestorePath.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.txtRestorePath.Location = new System.Drawing.Point(150, 83);
            this.txtRestorePath.Name = "txtRestorePath";
            this.txtRestorePath.ReadOnly = true;
            this.txtRestorePath.Size = new System.Drawing.Size(450, 23);
            this.txtRestorePath.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.label3.Location = new System.Drawing.Point(606, 86);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 15);
            this.label3.TabIndex = 2;
            this.label3.Text = "ملف النسخة:";
            // 
            // chkOverwriteExisting
            // 
            this.chkOverwriteExisting.AutoSize = true;
            this.chkOverwriteExisting.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.chkOverwriteExisting.Location = new System.Drawing.Point(50, 140);
            this.chkOverwriteExisting.Name = "chkOverwriteExisting";
            this.chkOverwriteExisting.Size = new System.Drawing.Size(158, 19);
            this.chkOverwriteExisting.TabIndex = 1;
            this.chkOverwriteExisting.Text = "استبدال البيانات الموجودة";
            this.chkOverwriteExisting.UseVisualStyleBackColor = true;
            // 
            // btnRestoreBackup
            // 
            this.btnRestoreBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(126)))), ((int)(((byte)(34)))));
            this.btnRestoreBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRestoreBackup.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnRestoreBackup.ForeColor = System.Drawing.Color.White;
            this.btnRestoreBackup.Location = new System.Drawing.Point(300, 300);
            this.btnRestoreBackup.Name = "btnRestoreBackup";
            this.btnRestoreBackup.Size = new System.Drawing.Size(200, 50);
            this.btnRestoreBackup.TabIndex = 0;
            this.btnRestoreBackup.Text = "استعادة النسخة";
            this.btnRestoreBackup.UseVisualStyleBackColor = false;
            this.btnRestoreBackup.Click += new System.EventHandler(this.btnRestoreBackup_Click);
            // 
            // tabHistory
            // 
            this.tabHistory.Controls.Add(this.groupBox3);
            this.tabHistory.Location = new System.Drawing.Point(4, 24);
            this.tabHistory.Name = "tabHistory";
            this.tabHistory.Size = new System.Drawing.Size(792, 470);
            this.tabHistory.TabIndex = 2;
            this.tabHistory.Text = "سجل النسخ الاحتياطية";
            this.tabHistory.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.dgvBackupHistory);
            this.groupBox3.Controls.Add(this.panel1);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox3.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold);
            this.groupBox3.Location = new System.Drawing.Point(0, 0);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(792, 470);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "النسخ الاحتياطية السابقة";
            //
            // dgvBackupHistory
            //
            this.dgvBackupHistory.AllowUserToAddRows = false;
            this.dgvBackupHistory.AllowUserToDeleteRows = false;
            this.dgvBackupHistory.BackgroundColor = System.Drawing.Color.White;
            this.dgvBackupHistory.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvBackupHistory.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvBackupHistory.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvBackupHistory.Location = new System.Drawing.Point(3, 73);
            this.dgvBackupHistory.MultiSelect = false;
            this.dgvBackupHistory.Name = "dgvBackupHistory";
            this.dgvBackupHistory.ReadOnly = true;
            this.dgvBackupHistory.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvBackupHistory.Size = new System.Drawing.Size(786, 394);
            this.dgvBackupHistory.TabIndex = 1;
            //
            // panel1
            //
            this.panel1.Controls.Add(this.btnRefreshHistory);
            this.panel1.Controls.Add(this.btnDeleteBackup);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(3, 23);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(786, 50);
            this.panel1.TabIndex = 0;
            //
            // btnRefreshHistory
            //
            this.btnRefreshHistory.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnRefreshHistory.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefreshHistory.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.btnRefreshHistory.ForeColor = System.Drawing.Color.White;
            this.btnRefreshHistory.Location = new System.Drawing.Point(20, 10);
            this.btnRefreshHistory.Name = "btnRefreshHistory";
            this.btnRefreshHistory.Size = new System.Drawing.Size(80, 30);
            this.btnRefreshHistory.TabIndex = 1;
            this.btnRefreshHistory.Text = "تحديث";
            this.btnRefreshHistory.UseVisualStyleBackColor = false;
            this.btnRefreshHistory.Click += new System.EventHandler(this.btnRefreshHistory_Click);
            //
            // btnDeleteBackup
            //
            this.btnDeleteBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnDeleteBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDeleteBackup.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.btnDeleteBackup.ForeColor = System.Drawing.Color.White;
            this.btnDeleteBackup.Location = new System.Drawing.Point(110, 10);
            this.btnDeleteBackup.Name = "btnDeleteBackup";
            this.btnDeleteBackup.Size = new System.Drawing.Size(80, 30);
            this.btnDeleteBackup.TabIndex = 0;
            this.btnDeleteBackup.Text = "حذف";
            this.btnDeleteBackup.UseVisualStyleBackColor = false;
            this.btnDeleteBackup.Click += new System.EventHandler(this.btnDeleteBackup_Click);
            //
            // panel2
            //
            this.panel2.Controls.Add(this.lblCurrentOperation);
            this.panel2.Controls.Add(this.progressBar1);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel2.Location = new System.Drawing.Point(0, 578);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(800, 60);
            this.panel2.TabIndex = 1;
            //
            // lblCurrentOperation
            //
            this.lblCurrentOperation.AutoSize = true;
            this.lblCurrentOperation.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblCurrentOperation.Location = new System.Drawing.Point(20, 10);
            this.lblCurrentOperation.Name = "lblCurrentOperation";
            this.lblCurrentOperation.Size = new System.Drawing.Size(0, 15);
            this.lblCurrentOperation.TabIndex = 1;
            //
            // progressBar1
            //
            this.progressBar1.Location = new System.Drawing.Point(20, 30);
            this.progressBar1.Name = "progressBar1";
            this.progressBar1.Size = new System.Drawing.Size(760, 20);
            this.progressBar1.TabIndex = 0;
            //
            // statusStrip1
            //
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.lblStatus});
            this.statusStrip1.Location = new System.Drawing.Point(0, 638);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.statusStrip1.Size = new System.Drawing.Size(800, 22);
            this.statusStrip1.TabIndex = 2;
            this.statusStrip1.Text = "statusStrip1";
            //
            // lblStatus
            //
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(39, 17);
            this.lblStatus.Text = "جاهز";
            //
            // panel3
            //
            this.panel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(73)))), ((int)(((byte)(94)))));
            this.panel3.Controls.Add(this.label1);
            this.panel3.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel3.Location = new System.Drawing.Point(0, 0);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(800, 80);
            this.panel3.TabIndex = 3;
            //
            // label1
            //
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("Segoe UI", 16F, System.Drawing.FontStyle.Bold);
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(300, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(200, 30);
            this.label1.TabIndex = 0;
            this.label1.Text = "النسخ الاحتياطي والاستعادة";
            //
            // saveFileDialog1
            //
            this.saveFileDialog1.DefaultExt = "bak";
            this.saveFileDialog1.Filter = "ملفات النسخ الاحتياطية|*.bak|جميع الملفات|*.*";
            this.saveFileDialog1.Title = "حفظ النسخة الاحتياطية";
            //
            // openFileDialog1
            //
            this.openFileDialog1.DefaultExt = "bak";
            this.openFileDialog1.Filter = "ملفات النسخ الاحتياطية|*.bak|جميع الملفات|*.*";
            this.openFileDialog1.Title = "اختيار ملف النسخة الاحتياطية";
            //
            // BackupRestoreForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(244)))), ((int)(((byte)(247)))));
            this.ClientSize = new System.Drawing.Size(800, 660);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.panel3);
            this.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.Name = "BackupRestoreForm";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "النسخ الاحتياطي والاستعادة";
            this.Load += new System.EventHandler(this.BackupRestoreForm_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabBackup.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabRestore.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.tabHistory.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvBackupHistory)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.panel3.ResumeLayout(false);
            this.panel3.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabBackup;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Button btnBrowseBackup;
        private System.Windows.Forms.TextBox txtBackupPath;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox chkCompressBackup;
        private System.Windows.Forms.CheckBox chkIncludeData;
        private System.Windows.Forms.CheckBox chkIncludeSchema;
        private System.Windows.Forms.Button btnCreateBackup;
        private System.Windows.Forms.TabPage tabRestore;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button btnBrowseRestore;
        private System.Windows.Forms.TextBox txtRestorePath;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.CheckBox chkOverwriteExisting;
        private System.Windows.Forms.Button btnRestoreBackup;
        private System.Windows.Forms.TabPage tabHistory;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.DataGridView dgvBackupHistory;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button btnRefreshHistory;
        private System.Windows.Forms.Button btnDeleteBackup;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Label lblCurrentOperation;
        private System.Windows.Forms.ProgressBar progressBar1;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel lblStatus;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.SaveFileDialog saveFileDialog1;
        private System.Windows.Forms.OpenFileDialog openFileDialog1;
    }
}
