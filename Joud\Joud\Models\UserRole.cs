using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات أدوار المستخدمين
    /// User Role Data Model
    /// </summary>
    [Table("UserRoles")]
    public class UserRole
    {
        [Key]
        public int RoleId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الدور")]
        public string RoleName { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "اسم الدور بالإنجليزية")]
        public string? RoleNameEn { get; set; }

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }
}
