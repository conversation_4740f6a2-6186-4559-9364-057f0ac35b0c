using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة الموردين
    /// Suppliers Management Form
    /// </summary>
    public partial class SuppliersForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly SupplierService _supplierService;
        private List<Supplier> _suppliers;
        private Supplier? _selectedSupplier;
        private bool _isEditing = false;

        public SuppliersForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _supplierService = new SupplierService();
            _suppliers = new List<Supplier>();
            
            InitializeFormSettings();
            SetupDataGridView();
            _ = LoadSuppliersAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة الموردين - نظام جود للمحاسبة المالية";
            this.Size = new Size(1200, 700);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private void SetupDataGridView()
        {
            // إعداد DataGridView
            dgvSuppliers.AutoGenerateColumns = false;
            dgvSuppliers.AllowUserToAddRows = false;
            dgvSuppliers.AllowUserToDeleteRows = false;
            dgvSuppliers.ReadOnly = true;
            dgvSuppliers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSuppliers.MultiSelect = false;
            dgvSuppliers.BackgroundColor = Color.White;
            dgvSuppliers.BorderStyle = BorderStyle.None;
            dgvSuppliers.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvSuppliers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvSuppliers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvSuppliers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvSuppliers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSuppliers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvSuppliers.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SupplierCode",
                HeaderText = "كود المورد",
                DataPropertyName = "SupplierCode",
                Width = 100,
                ReadOnly = true
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SupplierName",
                HeaderText = "اسم المورد",
                DataPropertyName = "SupplierName",
                Width = 200,
                ReadOnly = true
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120,
                ReadOnly = true
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Mobile",
                HeaderText = "الجوال",
                DataPropertyName = "Mobile",
                Width = 120,
                ReadOnly = true
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 180,
                ReadOnly = true
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "City",
                HeaderText = "المدينة",
                DataPropertyName = "City",
                Width = 100,
                ReadOnly = true
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreditLimit",
                HeaderText = "الحد الائتماني",
                DataPropertyName = "CreditLimit",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentBalance",
                HeaderText = "الرصيد الحالي",
                DataPropertyName = "CurrentBalance",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            // أحداث DataGridView
            dgvSuppliers.SelectionChanged += DgvSuppliers_SelectionChanged;
            dgvSuppliers.CellDoubleClick += DgvSuppliers_CellDoubleClick;
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل بيانات الموردين...";
                lblStatus.ForeColor = Color.Blue;

                _suppliers = await _supplierService.GetAllSuppliersAsync(_currentCompany.CompanyId);
                dgvSuppliers.DataSource = _suppliers;

                // تحديث الإحصائيات
                await UpdateStatisticsAsync();

                lblStatus.Text = $"تم تحميل {_suppliers.Count} مورد";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات الموردين: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _supplierService.GetSupplierStatisticsAsync(_currentCompany.CompanyId);
                lblTotalSuppliers.Text = $"إجمالي الموردين: {stats.TotalSuppliers}";
                lblSuppliersWithBalance.Text = $"موردين لديهم رصيد: {stats.SuppliersWithBalance}";
                lblTotalBalance.Text = $"إجمالي الأرصدة: {stats.TotalBalance:N2} {_currentCompany.Currency}";
                lblNewSuppliers.Text = $"موردين جدد هذا الشهر: {stats.NewSuppliersThisMonth}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        private void DgvSuppliers_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count > 0)
            {
                _selectedSupplier = dgvSuppliers.SelectedRows[0].DataBoundItem as Supplier;
                LoadSupplierDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
            }
            else
            {
                _selectedSupplier = null;
                ClearSupplierDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void DgvSuppliers_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadSupplierDetails()
        {
            if (_selectedSupplier == null) return;

            txtSupplierCode.Text = _selectedSupplier.SupplierCode;
            txtSupplierName.Text = _selectedSupplier.SupplierName;
            txtSupplierNameEn.Text = _selectedSupplier.SupplierNameEn;
            txtPhone.Text = _selectedSupplier.Phone;
            txtMobile.Text = _selectedSupplier.Mobile;
            txtEmail.Text = _selectedSupplier.Email;
            txtAddress.Text = _selectedSupplier.Address;
            txtCity.Text = _selectedSupplier.City;
            txtCountry.Text = _selectedSupplier.Country;
            txtTaxNumber.Text = _selectedSupplier.TaxNumber;
            numCreditLimit.Value = _selectedSupplier.CreditLimit;
            lblCurrentBalance.Text = $"الرصيد الحالي: {_selectedSupplier.CurrentBalance:N2} {_currentCompany.Currency}";
        }

        private void ClearSupplierDetails()
        {
            txtSupplierCode.Clear();
            txtSupplierName.Clear();
            txtSupplierNameEn.Clear();
            txtPhone.Clear();
            txtMobile.Clear();
            txtEmail.Clear();
            txtAddress.Clear();
            txtCity.Clear();
            txtCountry.Clear();
            txtTaxNumber.Clear();
            numCreditLimit.Value = 0;
            lblCurrentBalance.Text = "الرصيد الحالي: 0.00";
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                _isEditing = false;
                _selectedSupplier = null;
                ClearSupplierDetails();
                
                // إنشاء كود تلقائي
                string newCode = await _supplierService.GenerateSupplierCodeAsync(_currentCompany.CompanyId);
                txtSupplierCode.Text = newCode;
                
                SetEditMode(true);
                txtSupplierName.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء مورد جديد: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedSupplier == null)
            {
                ShowError("يرجى اختيار مورد للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            txtSupplierName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ بيانات المورد...";
                lblStatus.ForeColor = Color.Blue;

                var supplier = CreateSupplierFromInput();

                if (_isEditing && _selectedSupplier != null)
                {
                    supplier.SupplierId = _selectedSupplier.SupplierId;
                    supplier.CreatedDate = _selectedSupplier.CreatedDate;
                    supplier.CreatedBy = _selectedSupplier.CreatedBy;
                    supplier.CurrentBalance = _selectedSupplier.CurrentBalance;
                    supplier.ModifiedBy = _currentUser.UserId;

                    await _supplierService.UpdateSupplierAsync(supplier);
                    lblStatus.Text = "تم تحديث بيانات المورد بنجاح";
                }
                else
                {
                    supplier.CreatedBy = _currentUser.UserId;
                    supplier.CompanyId = _currentCompany.CompanyId;

                    await _supplierService.AddSupplierAsync(supplier);
                    lblStatus.Text = "تم إضافة المورد بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadSuppliersAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ بيانات المورد: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedSupplier != null)
            {
                LoadSupplierDetails();
            }
            else
            {
                ClearSupplierDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedSupplier == null)
            {
                ShowError("يرجى اختيار مورد للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف المورد '{_selectedSupplier.SupplierName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف المورد...";
                    lblStatus.ForeColor = Color.Blue;

                    await _supplierService.DeleteSupplierAsync(_selectedSupplier.SupplierId, _currentUser.UserId);
                    
                    lblStatus.Text = "تم حذف المورد بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadSuppliersAsync();
                    ClearSupplierDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف المورد: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvSuppliers.DataSource = _suppliers;
                }
                else
                {
                    var searchResults = await _supplierService.SearchSuppliersAsync(txtSearch.Text, _currentCompany.CompanyId);
                    dgvSuppliers.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadSuppliersAsync();
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtSupplierName.Text))
                errors.Add("اسم المورد مطلوب");

            if (!string.IsNullOrEmpty(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
                errors.Add("البريد الإلكتروني غير صحيح");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private Supplier CreateSupplierFromInput()
        {
            return new Supplier
            {
                SupplierCode = txtSupplierCode.Text.Trim(),
                SupplierName = txtSupplierName.Text.Trim(),
                SupplierNameEn = txtSupplierNameEn.Text.Trim(),
                Phone = txtPhone.Text.Trim(),
                Mobile = txtMobile.Text.Trim(),
                Email = txtEmail.Text.Trim(),
                Address = txtAddress.Text.Trim(),
                City = txtCity.Text.Trim(),
                Country = txtCountry.Text.Trim(),
                TaxNumber = txtTaxNumber.Text.Trim(),
                CreditLimit = numCreditLimit.Value
            };
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال داخل GroupBox
            txtSupplierCode.ReadOnly = true; // الكود دائماً للقراءة فقط
            txtSupplierName.ReadOnly = !isEditing;
            txtSupplierNameEn.ReadOnly = !isEditing;
            txtPhone.ReadOnly = !isEditing;
            txtMobile.ReadOnly = !isEditing;
            txtEmail.ReadOnly = !isEditing;
            txtAddress.ReadOnly = !isEditing;
            txtCity.ReadOnly = !isEditing;
            txtCountry.ReadOnly = !isEditing;
            txtTaxNumber.ReadOnly = !isEditing;
            numCreditLimit.ReadOnly = !isEditing;

            // تغيير لون الخلفية للحقول
            Color backColor = isEditing ? Color.White : Color.FromArgb(236, 240, 241);
            txtSupplierName.BackColor = backColor;
            txtSupplierNameEn.BackColor = backColor;
            txtPhone.BackColor = backColor;
            txtMobile.BackColor = backColor;
            txtEmail.BackColor = backColor;
            txtAddress.BackColor = backColor;
            txtCity.BackColor = backColor;
            txtCountry.BackColor = backColor;
            txtTaxNumber.BackColor = backColor;
            numCreditLimit.BackColor = backColor;

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedSupplier != null;
            btnDelete.Enabled = !isEditing && _selectedSupplier != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            dgvSuppliers.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;

            // تغيير لون GroupBox
            grpSupplierDetails.ForeColor = isEditing ? Color.FromArgb(39, 174, 96) : Color.FromArgb(52, 73, 94);
        }

        private void SetControlsEnabled(bool enabled)
        {
            // تفعيل/تعطيل العناصر الرئيسية
            dgvSuppliers.Enabled = enabled;
            txtSearch.Enabled = enabled;
            btnRefresh.Enabled = enabled;

            // تفعيل/تعطيل عناصر GroupBox
            foreach (Control control in grpSupplierDetails.Controls)
            {
                if (control is TextBox || control is NumericUpDown)
                {
                    control.Enabled = enabled;
                }
            }

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = enabled;
            btnEdit.Enabled = enabled && _selectedSupplier != null;
            btnDelete.Enabled = enabled && _selectedSupplier != null;
            btnSave.Enabled = enabled;
            btnCancel.Enabled = enabled;
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void SuppliersForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
        }
    }
}
