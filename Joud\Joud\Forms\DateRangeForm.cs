using System;
using System.Globalization;
using System.Windows.Forms;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة اختيار فترة التقرير - Date Range Selection Form
    /// تسمح للمستخدم باختيار فترة زمنية للتقارير
    /// </summary>
    public partial class DateRangeForm : Form
    {
        #region Properties

        /// <summary>
        /// تاريخ البداية المحدد
        /// </summary>
        public DateTime StartDate { get; private set; }

        /// <summary>
        /// تاريخ النهاية المحدد
        /// </summary>
        public DateTime EndDate { get; private set; }

        /// <summary>
        /// نوع الفترة المحددة
        /// </summary>
        public DateRangeType SelectedRangeType { get; private set; }

        /// <summary>
        /// تاريخ البداية (اسم بديل)
        /// </summary>
        public DateTime DateFrom => StartDate;

        /// <summary>
        /// تاريخ النهاية (اسم بديل)
        /// </summary>
        public DateTime DateTo => EndDate;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ شاشة اختيار فترة التقرير
        /// </summary>
        public DateRangeForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// منشئ شاشة اختيار فترة التقرير مع فترة افتراضية
        /// </summary>
        /// <param name="startDate">تاريخ البداية الافتراضي</param>
        /// <param name="endDate">تاريخ النهاية الافتراضي</param>
        public DateRangeForm(DateTime startDate, DateTime endDate) : this()
        {
            StartDate = startDate;
            EndDate = endDate;
            dtpStartDate.Value = startDate;
            dtpEndDate.Value = endDate;
            rbCustomRange.Checked = true;
            UpdateDateRangeDisplay();
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل الشاشة
        /// </summary>
        private void DateRangeForm_Load(object sender, EventArgs e)
        {
            try
            {
                // تعيين التاريخ الافتراضي (اليوم)
                if (StartDate == DateTime.MinValue)
                {
                    SetTodayRange();
                }
                
                UpdateUI();
                UpdateDateRangeDisplay();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Button Events

        /// <summary>
        /// حدث النقر على زر موافق
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateDateRange())
                {
                    UpdateSelectedDates();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تأكيد الاختيار: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الإلغاء
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء العملية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region RadioButton Events

        /// <summary>
        /// حدث تغيير اختيار نوع الفترة
        /// </summary>
        private void DateRange_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                var radioButton = sender as RadioButton;
                if (radioButton != null && radioButton.Checked)
                {
                    UpdateDateRangeFromSelection(radioButton);
                    UpdateUI();
                    UpdateDateRangeDisplay();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير نوع الفترة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region DateTimePicker Events

        /// <summary>
        /// حدث تغيير التاريخ المخصص
        /// </summary>
        private void CustomDate_ValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (rbCustomRange.Checked)
                {
                    StartDate = dtpStartDate.Value.Date;
                    EndDate = dtpEndDate.Value.Date;
                    SelectedRangeType = DateRangeType.Custom;
                    UpdateDateRangeDisplay();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تغيير التاريخ المخصص: {ex.Message}");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تهيئة الشاشة
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // تعيين التواريخ الافتراضية
                dtpStartDate.Value = DateTime.Today;
                dtpEndDate.Value = DateTime.Today;
                
                // تعيين الحد الأقصى والأدنى للتواريخ
                dtpStartDate.MaxDate = DateTime.Today;
                dtpEndDate.MaxDate = DateTime.Today;
                dtpStartDate.MinDate = DateTime.Today.AddYears(-10);
                dtpEndDate.MinDate = DateTime.Today.AddYears(-10);
                
                // تعيين الاختيار الافتراضي
                rbToday.Checked = true;
                SelectedRangeType = DateRangeType.Today;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث نطاق التاريخ بناءً على الاختيار
        /// </summary>
        /// <param name="selectedRadioButton">الزر المحدد</param>
        private void UpdateDateRangeFromSelection(RadioButton selectedRadioButton)
        {
            try
            {
                DateTime today = DateTime.Today;
                
                if (selectedRadioButton == rbToday)
                {
                    SetTodayRange();
                }
                else if (selectedRadioButton == rbYesterday)
                {
                    SetYesterdayRange();
                }
                else if (selectedRadioButton == rbCurrentWeek)
                {
                    SetCurrentWeekRange();
                }
                else if (selectedRadioButton == rbLastWeek)
                {
                    SetLastWeekRange();
                }
                else if (selectedRadioButton == rbCurrentMonth)
                {
                    SetCurrentMonthRange();
                }
                else if (selectedRadioButton == rbLastMonth)
                {
                    SetLastMonthRange();
                }
                else if (selectedRadioButton == rbCurrentYear)
                {
                    SetCurrentYearRange();
                }
                else if (selectedRadioButton == rbLastYear)
                {
                    SetLastYearRange();
                }
                else if (selectedRadioButton == rbCustomRange)
                {
                    SetCustomRange();
                }
                
                // تحديث DateTimePickers
                dtpStartDate.Value = StartDate;
                dtpEndDate.Value = EndDate;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحديث نطاق التاريخ: {ex.Message}");
            }
        }

        /// <summary>
        /// تعيين فترة اليوم
        /// </summary>
        private void SetTodayRange()
        {
            StartDate = DateTime.Today;
            EndDate = DateTime.Today;
            SelectedRangeType = DateRangeType.Today;
        }

        /// <summary>
        /// تعيين فترة أمس
        /// </summary>
        private void SetYesterdayRange()
        {
            StartDate = DateTime.Today.AddDays(-1);
            EndDate = DateTime.Today.AddDays(-1);
            SelectedRangeType = DateRangeType.Yesterday;
        }

        /// <summary>
        /// تعيين فترة الأسبوع الحالي
        /// </summary>
        private void SetCurrentWeekRange()
        {
            DateTime today = DateTime.Today;
            int daysFromSunday = (int)today.DayOfWeek;
            
            StartDate = today.AddDays(-daysFromSunday);
            EndDate = StartDate.AddDays(6);
            SelectedRangeType = DateRangeType.CurrentWeek;
        }

        /// <summary>
        /// تعيين فترة الأسبوع الماضي
        /// </summary>
        private void SetLastWeekRange()
        {
            DateTime today = DateTime.Today;
            int daysFromSunday = (int)today.DayOfWeek;
            
            StartDate = today.AddDays(-daysFromSunday - 7);
            EndDate = StartDate.AddDays(6);
            SelectedRangeType = DateRangeType.LastWeek;
        }

        /// <summary>
        /// تعيين فترة الشهر الحالي
        /// </summary>
        private void SetCurrentMonthRange()
        {
            DateTime today = DateTime.Today;
            StartDate = new DateTime(today.Year, today.Month, 1);
            EndDate = StartDate.AddMonths(1).AddDays(-1);
            SelectedRangeType = DateRangeType.CurrentMonth;
        }

        /// <summary>
        /// تعيين فترة الشهر الماضي
        /// </summary>
        private void SetLastMonthRange()
        {
            DateTime today = DateTime.Today;
            DateTime lastMonth = today.AddMonths(-1);
            StartDate = new DateTime(lastMonth.Year, lastMonth.Month, 1);
            EndDate = StartDate.AddMonths(1).AddDays(-1);
            SelectedRangeType = DateRangeType.LastMonth;
        }

        /// <summary>
        /// تعيين فترة السنة الحالية
        /// </summary>
        private void SetCurrentYearRange()
        {
            DateTime today = DateTime.Today;
            StartDate = new DateTime(today.Year, 1, 1);
            EndDate = new DateTime(today.Year, 12, 31);
            SelectedRangeType = DateRangeType.CurrentYear;
        }

        /// <summary>
        /// تعيين فترة السنة الماضية
        /// </summary>
        private void SetLastYearRange()
        {
            DateTime today = DateTime.Today;
            int lastYear = today.Year - 1;
            StartDate = new DateTime(lastYear, 1, 1);
            EndDate = new DateTime(lastYear, 12, 31);
            SelectedRangeType = DateRangeType.LastYear;
        }

        /// <summary>
        /// تعيين فترة مخصصة
        /// </summary>
        private void SetCustomRange()
        {
            StartDate = dtpStartDate.Value.Date;
            EndDate = dtpEndDate.Value.Date;
            SelectedRangeType = DateRangeType.Custom;
        }

        /// <summary>
        /// التحقق من صحة نطاق التاريخ
        /// </summary>
        /// <returns>true إذا كان النطاق صحيح</returns>
        private bool ValidateDateRange()
        {
            try
            {
                if (rbCustomRange.Checked)
                {
                    if (dtpStartDate.Value > dtpEndDate.Value)
                    {
                        MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية.", "خطأ في التحقق", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        dtpStartDate.Focus();
                        return false;
                    }
                    
                    if (dtpEndDate.Value > DateTime.Today)
                    {
                        MessageBox.Show("لا يمكن اختيار تاريخ في المستقبل.", "خطأ في التحقق", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        dtpEndDate.Focus();
                        return false;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من نطاق التاريخ: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// تحديث التواريخ المحددة
        /// </summary>
        private void UpdateSelectedDates()
        {
            try
            {
                if (rbCustomRange.Checked)
                {
                    StartDate = dtpStartDate.Value.Date;
                    EndDate = dtpEndDate.Value.Date;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث التواريخ المحددة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث عرض نطاق التاريخ
        /// </summary>
        private void UpdateDateRangeDisplay()
        {
            try
            {
                string startDateStr = StartDate.ToString("yyyy/MM/dd", CultureInfo.InvariantCulture);
                string endDateStr = EndDate.ToString("yyyy/MM/dd", CultureInfo.InvariantCulture);
                
                if (StartDate.Date == EndDate.Date)
                {
                    lblDateRange.Text = $"التاريخ المحدد: {startDateStr}";
                }
                else
                {
                    lblDateRange.Text = $"الفترة المحددة: من {startDateStr} إلى {endDateStr}";
                }
            }
            catch (Exception ex)
            {
                lblDateRange.Text = "خطأ في عرض التاريخ";
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عرض نطاق التاريخ: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث واجهة المستخدم
        /// </summary>
        private void UpdateUI()
        {
            try
            {
                // تفعيل/إلغاء تفعيل DateTimePickers بناءً على الاختيار
                bool enableCustomDates = rbCustomRange.Checked;
                dtpStartDate.Enabled = enableCustomDates;
                dtpEndDate.Enabled = enableCustomDates;
                
                // تغيير لون الخلفية
                Color backColor = enableCustomDates ? SystemColors.Window : SystemColors.Control;
                dtpStartDate.BackColor = backColor;
                dtpEndDate.BackColor = backColor;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة المستخدم: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// عرض شاشة اختيار فترة التقرير
        /// </summary>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>نتيجة الحوار مع التواريخ المحددة</returns>
        public static DateRangeResult ShowDateRangeDialog(IWin32Window parent = null)
        {
            try
            {
                using (var dateRangeForm = new DateRangeForm())
                {
                    var result = dateRangeForm.ShowDialog(parent);
                    
                    return new DateRangeResult
                    {
                        DialogResult = result,
                        StartDate = dateRangeForm.StartDate,
                        EndDate = dateRangeForm.EndDate,
                        RangeType = dateRangeForm.SelectedRangeType
                    };
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض شاشة اختيار التاريخ: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                return new DateRangeResult
                {
                    DialogResult = DialogResult.Cancel,
                    StartDate = DateTime.Today,
                    EndDate = DateTime.Today,
                    RangeType = DateRangeType.Today
                };
            }
        }

        /// <summary>
        /// عرض شاشة اختيار فترة التقرير مع فترة افتراضية
        /// </summary>
        /// <param name="defaultStartDate">تاريخ البداية الافتراضي</param>
        /// <param name="defaultEndDate">تاريخ النهاية الافتراضي</param>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>نتيجة الحوار مع التواريخ المحددة</returns>
        public static DateRangeResult ShowDateRangeDialog(DateTime defaultStartDate, DateTime defaultEndDate, IWin32Window parent = null)
        {
            try
            {
                using (var dateRangeForm = new DateRangeForm(defaultStartDate, defaultEndDate))
                {
                    var result = dateRangeForm.ShowDialog(parent);
                    
                    return new DateRangeResult
                    {
                        DialogResult = result,
                        StartDate = dateRangeForm.StartDate,
                        EndDate = dateRangeForm.EndDate,
                        RangeType = dateRangeForm.SelectedRangeType
                    };
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض شاشة اختيار التاريخ: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                return new DateRangeResult
                {
                    DialogResult = DialogResult.Cancel,
                    StartDate = defaultStartDate,
                    EndDate = defaultEndDate,
                    RangeType = DateRangeType.Custom
                };
            }
        }

        #endregion
    }

    #region Helper Classes and Enums

    /// <summary>
    /// أنواع فترات التقارير
    /// </summary>
    public enum DateRangeType
    {
        Today,          // اليوم
        Yesterday,      // أمس
        CurrentWeek,    // الأسبوع الحالي
        LastWeek,       // الأسبوع الماضي
        CurrentMonth,   // الشهر الحالي
        LastMonth,      // الشهر الماضي
        CurrentYear,    // السنة الحالية
        LastYear,       // السنة الماضية
        Custom          // فترة مخصصة
    }

    /// <summary>
    /// نتيجة اختيار فترة التقرير
    /// </summary>
    public class DateRangeResult
    {
        /// <summary>
        /// نتيجة الحوار
        /// </summary>
        public DialogResult DialogResult { get; set; }

        /// <summary>
        /// تاريخ البداية
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// تاريخ النهاية
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// نوع الفترة
        /// </summary>
        public DateRangeType RangeType { get; set; }

        /// <summary>
        /// وصف الفترة بالعربية
        /// </summary>
        public string GetRangeDescription()
        {
            switch (RangeType)
            {
                case DateRangeType.Today:
                    return "اليوم";
                case DateRangeType.Yesterday:
                    return "أمس";
                case DateRangeType.CurrentWeek:
                    return "الأسبوع الحالي";
                case DateRangeType.LastWeek:
                    return "الأسبوع الماضي";
                case DateRangeType.CurrentMonth:
                    return "الشهر الحالي";
                case DateRangeType.LastMonth:
                    return "الشهر الماضي";
                case DateRangeType.CurrentYear:
                    return "السنة الحالية";
                case DateRangeType.LastYear:
                    return "السنة الماضية";
                case DateRangeType.Custom:
                    return $"من {StartDate:yyyy/MM/dd} إلى {EndDate:yyyy/MM/dd}";
                default:
                    return "غير محدد";
            }
        }
    }

    #endregion
}
