using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة الوحدات
    /// Unit Management Service
    /// </summary>
    public class UnitService
    {
        private readonly string _connectionString;

        public UnitService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// الحصول على جميع الوحدات للشركة
        /// Get all units for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الوحدات</returns>
        public async Task<List<Unit>> GetAllUnitsAsync(int companyId)
        {
            using var context = CreateDbContext();

            try
            {
                return await context.Units
                    .Where(u => u.CompanyId == companyId && u.IsActive)
                    .Include(u => u.CreatedByUser)
                    .Include(u => u.Products.Where(p => p.IsActive))
                    .OrderBy(u => u.UnitName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                // إذا فشل تحميل Products، نحمل Units فقط
                if (ex.InnerException?.Message.Contains("Invalid column name") == true)
                {
                    return await context.Units
                        .Where(u => u.CompanyId == companyId && u.IsActive)
                        .Include(u => u.CreatedByUser)
                        .OrderBy(u => u.UnitName)
                        .ToListAsync();
                }
                throw;
            }
        }

        /// <summary>
        /// الحصول على وحدة بالمعرف
        /// Get unit by ID
        /// </summary>
        /// <param name="unitId">معرف الوحدة</param>
        /// <returns>بيانات الوحدة</returns>
        public async Task<Unit?> GetUnitByIdAsync(int unitId)
        {
            using var context = CreateDbContext();

            try
            {
                return await context.Units
                    .Include(u => u.CreatedByUser)
                    .Include(u => u.Company)
                    .Include(u => u.Products.Where(p => p.IsActive))
                    .FirstOrDefaultAsync(u => u.UnitId == unitId && u.IsActive);
            }
            catch (Exception ex)
            {
                // إذا فشل تحميل Products، نحمل Unit فقط
                if (ex.InnerException?.Message.Contains("Invalid column name") == true)
                {
                    return await context.Units
                        .Include(u => u.CreatedByUser)
                        .Include(u => u.Company)
                        .FirstOrDefaultAsync(u => u.UnitId == unitId && u.IsActive);
                }
                throw;
            }
        }

        /// <summary>
        /// الحصول على وحدة بالكود
        /// Get unit by code
        /// </summary>
        /// <param name="unitCode">كود الوحدة</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات الوحدة</returns>
        public async Task<Unit?> GetUnitByCodeAsync(string unitCode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.Units
                .FirstOrDefaultAsync(u => u.UnitCode == unitCode && 
                                         u.CompanyId == companyId && u.IsActive);
        }

        /// <summary>
        /// البحث في الوحدات
        /// Search units
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الوحدات المطابقة</returns>
        public async Task<List<Unit>> SearchUnitsAsync(string searchTerm, int companyId)
        {
            using var context = CreateDbContext();
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllUnitsAsync(companyId);

            searchTerm = searchTerm.Trim().ToLower();

            try
            {
                return await context.Units
                    .Where(u => u.CompanyId == companyId && u.IsActive &&
                               (u.UnitName.ToLower().Contains(searchTerm) ||
                                u.UnitCode.ToLower().Contains(searchTerm) ||
                                (u.UnitNameEn != null && u.UnitNameEn.ToLower().Contains(searchTerm)) ||
                                (u.Description != null && u.Description.ToLower().Contains(searchTerm))))
                    .Include(u => u.CreatedByUser)
                    .Include(u => u.Products.Where(p => p.IsActive))
                    .OrderBy(u => u.UnitName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                // إذا فشل تحميل Products، نحمل Units فقط
                if (ex.InnerException?.Message.Contains("Invalid column name") == true)
                {
                    return await context.Units
                        .Where(u => u.CompanyId == companyId && u.IsActive &&
                                   (u.UnitName.ToLower().Contains(searchTerm) ||
                                    u.UnitCode.ToLower().Contains(searchTerm) ||
                                    (u.UnitNameEn != null && u.UnitNameEn.ToLower().Contains(searchTerm)) ||
                                    (u.Description != null && u.Description.ToLower().Contains(searchTerm))))
                        .Include(u => u.CreatedByUser)
                        .OrderBy(u => u.UnitName)
                        .ToListAsync();
                }
                throw;
            }
        }

        /// <summary>
        /// إضافة وحدة جديدة
        /// Add new unit
        /// </summary>
        /// <param name="unit">بيانات الوحدة</param>
        /// <returns>معرف الوحدة الجديدة</returns>
        public async Task<int> AddUnitAsync(Unit unit)
        {
            using var context = CreateDbContext();
            
            // التحقق من عدم تكرار الكود
            bool codeExists = await context.Units
                .AnyAsync(u => u.UnitCode == unit.UnitCode && 
                              u.CompanyId == unit.CompanyId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود الوحدة '{unit.UnitCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الاسم
            bool nameExists = await context.Units
                .AnyAsync(u => u.UnitName == unit.UnitName && 
                              u.CompanyId == unit.CompanyId);
            
            if (nameExists)
                throw new InvalidOperationException($"اسم الوحدة '{unit.UnitName}' موجود مسبقاً");

            // إنشاء كود تلقائي إذا لم يتم تحديده
            if (string.IsNullOrEmpty(unit.UnitCode))
            {
                unit.UnitCode = await GenerateUnitCodeAsync(unit.CompanyId);
            }

            unit.CreatedDate = DateTime.Now;
            unit.IsActive = true;

            context.Units.Add(unit);
            await context.SaveChangesAsync();

            return unit.UnitId;
        }

        /// <summary>
        /// تحديث بيانات وحدة
        /// Update unit
        /// </summary>
        /// <param name="unit">بيانات الوحدة المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateUnitAsync(Unit unit)
        {
            using var context = CreateDbContext();

            var existingUnit = await context.Units
                .FirstOrDefaultAsync(u => u.UnitId == unit.UnitId);

            if (existingUnit == null)
                throw new InvalidOperationException("الوحدة غير موجودة");

            // التحقق من عدم تكرار الكود مع وحدات أخرى
            bool codeExists = await context.Units
                .AnyAsync(u => u.UnitCode == unit.UnitCode && 
                              u.CompanyId == unit.CompanyId &&
                              u.UnitId != unit.UnitId);

            if (codeExists)
                throw new InvalidOperationException($"كود الوحدة '{unit.UnitCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الاسم مع وحدات أخرى
            bool nameExists = await context.Units
                .AnyAsync(u => u.UnitName == unit.UnitName && 
                              u.CompanyId == unit.CompanyId &&
                              u.UnitId != unit.UnitId);

            if (nameExists)
                throw new InvalidOperationException($"اسم الوحدة '{unit.UnitName}' موجود مسبقاً");

            // تحديث البيانات
            existingUnit.UnitCode = unit.UnitCode;
            existingUnit.UnitName = unit.UnitName;
            existingUnit.UnitNameEn = unit.UnitNameEn;
            existingUnit.Description = unit.Description;
            existingUnit.ModifiedDate = DateTime.Now;
            existingUnit.ModifiedBy = unit.ModifiedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف وحدة (حذف منطقي)
        /// Delete unit (soft delete)
        /// </summary>
        /// <param name="unitId">معرف الوحدة</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteUnitAsync(int unitId, int deletedBy)
        {
            using var context = CreateDbContext();

            var unit = await context.Units
                .FirstOrDefaultAsync(u => u.UnitId == unitId);

            if (unit == null)
                throw new InvalidOperationException("الوحدة غير موجودة");

            // التحقق من عدم وجود أصناف مرتبطة
            try
            {
                var hasProducts = await context.Products
                    .AnyAsync(p => p.UnitId == unitId && p.IsActive);

                if (hasProducts)
                    throw new InvalidOperationException("لا يمكن حذف الوحدة لوجود أصناف مرتبطة بها");
            }
            catch (Exception ex) when (ex.InnerException?.Message.Contains("Invalid column name") == true)
            {
                // إذا فشل التحقق من Products، نتجاهل التحقق ونكمل الحذف
            }

            // حذف منطقي
            unit.IsActive = false;
            unit.ModifiedDate = DateTime.Now;
            unit.ModifiedBy = deletedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// إنشاء كود وحدة تلقائي
        /// Generate automatic unit code
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>كود الوحدة الجديد</returns>
        public async Task<string> GenerateUnitCodeAsync(int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على أعلى رقم موجود
            var lastUnit = await context.Units
                .Where(u => u.CompanyId == companyId && u.UnitCode.StartsWith("UNIT"))
                .OrderByDescending(u => u.UnitCode)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastUnit != null)
            {
                string numberPart = lastUnit.UnitCode.Substring(4);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"UNIT{nextNumber:000}";
        }

        /// <summary>
        /// الحصول على إحصائيات الوحدات
        /// Get unit statistics
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>إحصائيات الوحدات</returns>
        public async Task<UnitStatistics> GetUnitStatisticsAsync(int companyId)
        {
            using var context = CreateDbContext();

            var totalUnits = await context.Units
                .CountAsync(u => u.CompanyId == companyId && u.IsActive);

            int unitsWithProducts = 0;
            int totalProducts = 0;

            try
            {
                unitsWithProducts = await context.Units
                    .Where(u => u.CompanyId == companyId && u.IsActive)
                    .CountAsync(u => u.Products.Any(p => p.IsActive));

                totalProducts = await context.Products
                    .CountAsync(p => p.CompanyId == companyId && p.IsActive);
            }
            catch (Exception ex) when (ex.InnerException?.Message.Contains("Invalid column name") == true)
            {
                // إذا فشل تحميل Products، نضع قيم افتراضية
                unitsWithProducts = 0;
                totalProducts = 0;
            }

            var newUnitsThisMonth = await context.Units
                .CountAsync(u => u.CompanyId == companyId && u.IsActive &&
                               u.CreatedDate.Month == DateTime.Now.Month &&
                               u.CreatedDate.Year == DateTime.Now.Year);

            return new UnitStatistics
            {
                TotalUnits = totalUnits,
                UnitsWithProducts = unitsWithProducts,
                TotalProducts = totalProducts,
                NewUnitsThisMonth = newUnitsThisMonth
            };
        }

        /// <summary>
        /// الحصول على الوحدات الشائعة الاستخدام
        /// Get commonly used units
        /// </summary>
        /// <returns>قائمة الوحدات الشائعة</returns>
        public List<string> GetCommonUnits()
        {
            return new List<string>
            {
                "قطعة", "كيلو", "جرام", "لتر", "متر", "سنتيمتر", "صندوق", "كرتون", 
                "علبة", "زجاجة", "كيس", "حبة", "دزينة", "طن", "مليلتر", "باكيت",
                "رول", "ورقة", "مجموعة", "وحدة", "عبوة", "جالون", "برميل"
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات الوحدة
        /// Validate unit data
        /// </summary>
        /// <param name="unit">بيانات الوحدة</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateUnit(Unit unit)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(unit.UnitName))
                errors.Add("اسم الوحدة مطلوب");

            if (unit.UnitName?.Length > 100)
                errors.Add("اسم الوحدة يجب أن يكون أقل من 100 حرف");

            if (string.IsNullOrWhiteSpace(unit.UnitCode))
                errors.Add("كود الوحدة مطلوب");

            if (unit.UnitCode?.Length > 20)
                errors.Add("كود الوحدة يجب أن يكون أقل من 20 حرف");

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }

    /// <summary>
    /// إحصائيات الوحدات
    /// Unit Statistics
    /// </summary>
    public class UnitStatistics
    {
        public int TotalUnits { get; set; }
        public int UnitsWithProducts { get; set; }
        public int TotalProducts { get; set; }
        public int NewUnitsThisMonth { get; set; }
    }
}
