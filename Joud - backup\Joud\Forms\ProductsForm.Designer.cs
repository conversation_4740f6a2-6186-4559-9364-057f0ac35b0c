namespace Joud.Forms
{
    partial class ProductsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            splitContainer1 = new SplitContainer();
            groupBox1 = new GroupBox();
            dgvProducts = new DataGridView();
            panel1 = new Panel();
            cmbCategoryFilter = new ComboBox();
            label1 = new Label();
            txtSearch = new TextBox();
            lblSearch = new Label();
            groupBox2 = new GroupBox();
            tabControl1 = new TabControl();
            tabPage1 = new TabPage();
            groupClassification = new GroupBox();
            cmbMainCategory = new ComboBox();
            label9 = new Label();
            cmbSubCategory = new ComboBox();
            label10 = new Label();
            cmbUnit = new ComboBox();
            label11 = new Label();
            cmbWarehouse = new ComboBox();
            lblWarehouse = new Label();
            numMinStock = new NumericUpDown();
            label12 = new Label();
            chkIsActive = new CheckBox();
            groupBasicInfo = new GroupBox();
            txtProductCode = new TextBox();
            label5 = new Label();
            txtProductName = new TextBox();
            label6 = new Label();
            txtProductNameEn = new TextBox();
            lblProductNameEn = new Label();
            txtBarcode = new TextBox();
            label8 = new Label();
            txtDescription = new TextBox();
            label7 = new Label();
            tabPage2 = new TabPage();
            groupCalculations = new GroupBox();
            lblProfitMargin = new Label();
            groupPricing = new GroupBox();
            numPurchasePrice = new NumericUpDown();
            label3 = new Label();
            numSalePrice = new NumericUpDown();
            label4 = new Label();
            numWholesalePrice = new NumericUpDown();
            lblWholesalePrice = new Label();
            numHalfWholesalePrice = new NumericUpDown();
            lblHalfWholesalePrice = new Label();
            numRetailPrice = new NumericUpDown();
            lblRetailPrice = new Label();
            numMinimumPrice = new NumericUpDown();
            lblMinimumPrice = new Label();
            tabPage3 = new TabPage();
            btnRemoveImage = new Button();
            btnSelectImage = new Button();
            picProductImage = new PictureBox();
            chkIsActiveForSale = new CheckBox();
            chkIsActiveForPurchase = new CheckBox();
            numTaxPercentage = new NumericUpDown();
            lblTaxPercentage = new Label();
            numDiscountPercentage = new NumericUpDown();
            lblDiscountPercentage = new Label();
            numOpeningBalance = new NumericUpDown();
            lblOpeningBalance = new Label();
            tabPage4 = new TabPage();
            groupInventory = new GroupBox();
            groupStatus = new GroupBox();
            groupImage = new GroupBox();
            panel2 = new Panel();
            btnRefresh = new Button();
            btnCancel = new Button();
            btnSave = new Button();
            btnDelete = new Button();
            btnEdit = new Button();
            btnNew = new Button();
            statusStrip1 = new StatusStrip();
            lblStatus = new ToolStripStatusLabel();
            pnlHeader = new Panel();
            lblLowStockProducts = new Label();
            lblActiveProducts = new Label();
            lblTotalProducts = new Label();
            lblNewProducts = new Label();
            lblCurrentStock = new Label();
            lblInventoryValue = new Label();
            lblMinimumStock = new Label();
            panel3 = new Panel();
            ((System.ComponentModel.ISupportInitialize)splitContainer1).BeginInit();
            splitContainer1.Panel1.SuspendLayout();
            splitContainer1.Panel2.SuspendLayout();
            splitContainer1.SuspendLayout();
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvProducts).BeginInit();
            panel1.SuspendLayout();
            groupBox2.SuspendLayout();
            tabControl1.SuspendLayout();
            tabPage1.SuspendLayout();
            groupClassification.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numMinStock).BeginInit();
            groupBasicInfo.SuspendLayout();
            tabPage2.SuspendLayout();
            groupCalculations.SuspendLayout();
            groupPricing.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numPurchasePrice).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numSalePrice).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numWholesalePrice).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numHalfWholesalePrice).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numRetailPrice).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numMinimumPrice).BeginInit();
            tabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)picProductImage).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numTaxPercentage).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numDiscountPercentage).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numOpeningBalance).BeginInit();
            panel2.SuspendLayout();
            statusStrip1.SuspendLayout();
            pnlHeader.SuspendLayout();
            panel3.SuspendLayout();
            SuspendLayout();
            // 
            // splitContainer1
            // 
            splitContainer1.Dock = DockStyle.Fill;
            splitContainer1.Location = new Point(0, 0);
            splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            splitContainer1.Panel1.Controls.Add(groupBox1);
            splitContainer1.Panel1.RightToLeft = RightToLeft.Yes;
            // 
            // splitContainer1.Panel2
            // 
            splitContainer1.Panel2.Controls.Add(groupBox2);
            splitContainer1.Panel2.RightToLeft = RightToLeft.Yes;
            splitContainer1.Size = new Size(1284, 618);
            splitContainer1.SplitterDistance = 733;
            splitContainer1.TabIndex = 0;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(dgvProducts);
            groupBox1.Controls.Add(panel1);
            groupBox1.Dock = DockStyle.Fill;
            groupBox1.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupBox1.Location = new Point(0, 0);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(733, 618);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "قائمة الأصناف";
            // 
            // dgvProducts
            // 
            dgvProducts.AllowUserToAddRows = false;
            dgvProducts.AllowUserToDeleteRows = false;
            dgvProducts.BackgroundColor = Color.White;
            dgvProducts.BorderStyle = BorderStyle.None;
            dgvProducts.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvProducts.Dock = DockStyle.Fill;
            dgvProducts.Location = new Point(3, 71);
            dgvProducts.MultiSelect = false;
            dgvProducts.Name = "dgvProducts";
            dgvProducts.ReadOnly = true;
            dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProducts.Size = new Size(727, 544);
            dgvProducts.TabIndex = 1;
            dgvProducts.SelectionChanged += DgvProducts_SelectionChanged;
            // 
            // panel1
            // 
            panel1.Controls.Add(cmbCategoryFilter);
            panel1.Controls.Add(label1);
            panel1.Controls.Add(txtSearch);
            panel1.Controls.Add(lblSearch);
            panel1.Dock = DockStyle.Top;
            panel1.Location = new Point(3, 21);
            panel1.Name = "panel1";
            panel1.Size = new Size(727, 50);
            panel1.TabIndex = 0;
            // 
            // cmbCategoryFilter
            // 
            cmbCategoryFilter.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCategoryFilter.Font = new Font("Segoe UI", 9F);
            cmbCategoryFilter.FormattingEnabled = true;
            cmbCategoryFilter.Location = new Point(12, 15);
            cmbCategoryFilter.Name = "cmbCategoryFilter";
            cmbCategoryFilter.Size = new Size(150, 23);
            cmbCategoryFilter.TabIndex = 3;
            cmbCategoryFilter.SelectedIndexChanged += cmbCategoryFilter_SelectedIndexChanged;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Segoe UI", 9F);
            label1.Location = new Point(168, 19);
            label1.Name = "label1";
            label1.Size = new Size(64, 15);
            label1.TabIndex = 2;
            label1.Text = "فلترة بالفئة:";
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Segoe UI", 9F);
            txtSearch.Location = new Point(450, 15);
            txtSearch.Name = "txtSearch";
            txtSearch.Size = new Size(200, 23);
            txtSearch.TabIndex = 1;
            txtSearch.TextChanged += txtSearch_TextChanged;
            // 
            // lblSearch
            // 
            lblSearch.AutoSize = true;
            lblSearch.Font = new Font("Segoe UI", 9F);
            lblSearch.Location = new Point(656, 19);
            lblSearch.Name = "lblSearch";
            lblSearch.Size = new Size(32, 15);
            lblSearch.TabIndex = 0;
            lblSearch.Text = "بحث:";
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(tabControl1);
            groupBox2.Dock = DockStyle.Fill;
            groupBox2.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupBox2.Location = new Point(0, 0);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(547, 618);
            groupBox2.TabIndex = 0;
            groupBox2.TabStop = false;
            groupBox2.Text = "تفاصيل الصنف";
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(tabPage1);
            tabControl1.Controls.Add(tabPage2);
            tabControl1.Controls.Add(tabPage3);
            tabControl1.Controls.Add(tabPage4);
            tabControl1.Dock = DockStyle.Fill;
            tabControl1.Font = new Font("Segoe UI", 9F);
            tabControl1.Location = new Point(3, 21);
            tabControl1.Name = "tabControl1";
            tabControl1.RightToLeft = RightToLeft.Yes;
            tabControl1.RightToLeftLayout = true;
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(541, 594);
            tabControl1.TabIndex = 0;
            // 
            // tabPage1
            // 
            tabPage1.Controls.Add(groupClassification);
            tabPage1.Controls.Add(groupBasicInfo);
            tabPage1.Location = new Point(4, 24);
            tabPage1.Name = "tabPage1";
            tabPage1.Padding = new Padding(3);
            tabPage1.Size = new Size(533, 566);
            tabPage1.TabIndex = 0;
            tabPage1.Text = "📝 البيانات الأساسية";
            tabPage1.UseVisualStyleBackColor = true;
            // 
            // groupClassification
            // 
            groupClassification.BackColor = Color.FromArgb(232, 245, 233);
            groupClassification.Controls.Add(cmbMainCategory);
            groupClassification.Controls.Add(label9);
            groupClassification.Controls.Add(cmbSubCategory);
            groupClassification.Controls.Add(label10);
            groupClassification.Controls.Add(cmbUnit);
            groupClassification.Controls.Add(label11);
            groupClassification.Controls.Add(cmbWarehouse);
            groupClassification.Controls.Add(lblWarehouse);
            groupClassification.Controls.Add(numMinStock);
            groupClassification.Controls.Add(label12);
            groupClassification.Controls.Add(chkIsActive);
            groupClassification.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupClassification.Location = new Point(10, 296);
            groupClassification.Name = "groupClassification";
            groupClassification.Size = new Size(520, 258);
            groupClassification.TabIndex = 1;
            groupClassification.TabStop = false;
            groupClassification.Text = "التصنيف والوحدات";
            // 
            // cmbMainCategory
            // 
            cmbMainCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbMainCategory.Font = new Font("Segoe UI", 9F);
            cmbMainCategory.FormattingEnabled = true;
            cmbMainCategory.Location = new Point(47, 27);
            cmbMainCategory.Name = "cmbMainCategory";
            cmbMainCategory.Size = new Size(350, 23);
            cmbMainCategory.TabIndex = 9;
            cmbMainCategory.SelectedIndexChanged += cmbMainCategory_SelectedIndexChanged;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Font = new Font("Segoe UI", 9F);
            label9.Location = new Point(403, 30);
            label9.Name = "label9";
            label9.Size = new Size(75, 15);
            label9.TabIndex = 8;
            label9.Text = "الفئة الرئيسية:";
            // 
            // cmbSubCategory
            // 
            cmbSubCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbSubCategory.Font = new Font("Segoe UI", 9F);
            cmbSubCategory.FormattingEnabled = true;
            cmbSubCategory.Location = new Point(47, 77);
            cmbSubCategory.Name = "cmbSubCategory";
            cmbSubCategory.Size = new Size(350, 23);
            cmbSubCategory.TabIndex = 11;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Font = new Font("Segoe UI", 9F);
            label10.Location = new Point(403, 80);
            label10.Name = "label10";
            label10.Size = new Size(70, 15);
            label10.TabIndex = 10;
            label10.Text = "الفئة الفرعية:";
            // 
            // cmbUnit
            // 
            cmbUnit.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbUnit.Font = new Font("Segoe UI", 9F);
            cmbUnit.FormattingEnabled = true;
            cmbUnit.Location = new Point(47, 127);
            cmbUnit.Name = "cmbUnit";
            cmbUnit.Size = new Size(350, 23);
            cmbUnit.TabIndex = 13;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Font = new Font("Segoe UI", 9F);
            label11.Location = new Point(403, 130);
            label11.Name = "label11";
            label11.Size = new Size(42, 15);
            label11.TabIndex = 12;
            label11.Text = "الوحدة:";
            // 
            // cmbWarehouse
            // 
            cmbWarehouse.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbWarehouse.Font = new Font("Segoe UI", 9F);
            cmbWarehouse.FormattingEnabled = true;
            cmbWarehouse.Location = new Point(47, 97);
            cmbWarehouse.Name = "cmbWarehouse";
            cmbWarehouse.Size = new Size(350, 23);
            cmbWarehouse.TabIndex = 17;
            // 
            // lblWarehouse
            // 
            lblWarehouse.AutoSize = true;
            lblWarehouse.Font = new Font("Segoe UI", 9F);
            lblWarehouse.Location = new Point(403, 100);
            lblWarehouse.Name = "lblWarehouse";
            lblWarehouse.Size = new Size(44, 15);
            lblWarehouse.TabIndex = 16;
            lblWarehouse.Text = "المخزن:";
            // 
            // numMinStock
            // 
            numMinStock.DecimalPlaces = 2;
            numMinStock.Font = new Font("Segoe UI", 9F);
            numMinStock.Location = new Point(47, 177);
            numMinStock.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numMinStock.Name = "numMinStock";
            numMinStock.Size = new Size(350, 23);
            numMinStock.TabIndex = 16;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Font = new Font("Segoe UI", 9F);
            label12.Location = new Point(403, 180);
            label12.Name = "label12";
            label12.Size = new Size(106, 15);
            label12.TabIndex = 15;
            label12.Text = "الحد الأدنى للمخزون:";
            // 
            // chkIsActive
            // 
            chkIsActive.AutoSize = true;
            chkIsActive.Checked = true;
            chkIsActive.CheckState = CheckState.Checked;
            chkIsActive.Font = new Font("Segoe UI", 9F);
            chkIsActive.Location = new Point(47, 227);
            chkIsActive.Name = "chkIsActive";
            chkIsActive.Size = new Size(50, 19);
            chkIsActive.TabIndex = 14;
            chkIsActive.Text = "نشط";
            chkIsActive.UseVisualStyleBackColor = true;
            // 
            // groupBasicInfo
            // 
            groupBasicInfo.BackColor = Color.FromArgb(248, 249, 250);
            groupBasicInfo.Controls.Add(txtProductCode);
            groupBasicInfo.Controls.Add(label5);
            groupBasicInfo.Controls.Add(txtProductName);
            groupBasicInfo.Controls.Add(label6);
            groupBasicInfo.Controls.Add(txtProductNameEn);
            groupBasicInfo.Controls.Add(lblProductNameEn);
            groupBasicInfo.Controls.Add(txtBarcode);
            groupBasicInfo.Controls.Add(label8);
            groupBasicInfo.Controls.Add(txtDescription);
            groupBasicInfo.Controls.Add(label7);
            groupBasicInfo.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupBasicInfo.Location = new Point(10, 10);
            groupBasicInfo.Name = "groupBasicInfo";
            groupBasicInfo.Size = new Size(520, 280);
            groupBasicInfo.TabIndex = 0;
            groupBasicInfo.TabStop = false;
            groupBasicInfo.Text = "معلومات الصنف الأساسية";
            // 
            // txtProductCode
            // 
            txtProductCode.Font = new Font("Segoe UI", 9F);
            txtProductCode.Location = new Point(20, 30);
            txtProductCode.Name = "txtProductCode";
            txtProductCode.Size = new Size(350, 23);
            txtProductCode.TabIndex = 1;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new Font("Segoe UI", 9F);
            label5.Location = new Point(380, 33);
            label5.Name = "label5";
            label5.Size = new Size(66, 15);
            label5.TabIndex = 0;
            label5.Text = "كود الصنف:";
            // 
            // txtProductName
            // 
            txtProductName.Font = new Font("Segoe UI", 9F);
            txtProductName.Location = new Point(20, 70);
            txtProductName.Name = "txtProductName";
            txtProductName.Size = new Size(350, 23);
            txtProductName.TabIndex = 3;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new Font("Segoe UI", 9F);
            label6.Location = new Point(380, 73);
            label6.Name = "label6";
            label6.Size = new Size(68, 15);
            label6.TabIndex = 2;
            label6.Text = "اسم الصنف:";
            // 
            // txtProductNameEn
            // 
            txtProductNameEn.Font = new Font("Segoe UI", 9F);
            txtProductNameEn.Location = new Point(20, 110);
            txtProductNameEn.Name = "txtProductNameEn";
            txtProductNameEn.Size = new Size(350, 23);
            txtProductNameEn.TabIndex = 4;
            // 
            // lblProductNameEn
            // 
            lblProductNameEn.AutoSize = true;
            lblProductNameEn.Font = new Font("Segoe UI", 9F);
            lblProductNameEn.Location = new Point(380, 113);
            lblProductNameEn.Name = "lblProductNameEn";
            lblProductNameEn.Size = new Size(114, 15);
            lblProductNameEn.TabIndex = 18;
            lblProductNameEn.Text = "اسم الصنف (إنجليزي):";
            // 
            // txtBarcode
            // 
            txtBarcode.Font = new Font("Segoe UI", 9F);
            txtBarcode.Location = new Point(20, 150);
            txtBarcode.Name = "txtBarcode";
            txtBarcode.Size = new Size(350, 23);
            txtBarcode.TabIndex = 7;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Font = new Font("Segoe UI", 9F);
            label8.Location = new Point(380, 153);
            label8.Name = "label8";
            label8.Size = new Size(46, 15);
            label8.TabIndex = 6;
            label8.Text = "الباركود:";
            // 
            // txtDescription
            // 
            txtDescription.Font = new Font("Segoe UI", 9F);
            txtDescription.Location = new Point(20, 190);
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.Size = new Size(350, 70);
            txtDescription.TabIndex = 5;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Font = new Font("Segoe UI", 9F);
            label7.Location = new Point(380, 193);
            label7.Name = "label7";
            label7.Size = new Size(47, 15);
            label7.TabIndex = 4;
            label7.Text = "الوصف:";
            // 
            // tabPage2
            // 
            tabPage2.Controls.Add(groupCalculations);
            tabPage2.Controls.Add(groupPricing);
            tabPage2.Location = new Point(4, 24);
            tabPage2.Name = "tabPage2";
            tabPage2.Padding = new Padding(3);
            tabPage2.Size = new Size(533, 566);
            tabPage2.TabIndex = 1;
            tabPage2.Text = "💰 الأسعار";
            tabPage2.UseVisualStyleBackColor = true;
            // 
            // groupCalculations
            // 
            groupCalculations.BackColor = Color.FromArgb(255, 248, 225);
            groupCalculations.Controls.Add(lblProfitMargin);
            groupCalculations.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupCalculations.Location = new Point(10, 296);
            groupCalculations.Name = "groupCalculations";
            groupCalculations.Size = new Size(512, 264);
            groupCalculations.TabIndex = 1;
            groupCalculations.TabStop = false;
            groupCalculations.Text = "النسب والحسابات";
            // 
            // lblProfitMargin
            // 
            lblProfitMargin.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblProfitMargin.AutoSize = true;
            lblProfitMargin.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblProfitMargin.ForeColor = Color.FromArgb(39, 174, 96);
            lblProfitMargin.Location = new Point(103, 78);
            lblProfitMargin.Name = "lblProfitMargin";
            lblProfitMargin.Size = new Size(126, 19);
            lblProfitMargin.TabIndex = 6;
            lblProfitMargin.Text = "هامش الربح: 0.00%";
            // 
            // groupPricing
            // 
            groupPricing.BackColor = Color.FromArgb(230, 244, 255);
            groupPricing.Controls.Add(numPurchasePrice);
            groupPricing.Controls.Add(label3);
            groupPricing.Controls.Add(numSalePrice);
            groupPricing.Controls.Add(label4);
            groupPricing.Controls.Add(numWholesalePrice);
            groupPricing.Controls.Add(lblWholesalePrice);
            groupPricing.Controls.Add(numHalfWholesalePrice);
            groupPricing.Controls.Add(lblHalfWholesalePrice);
            groupPricing.Controls.Add(numRetailPrice);
            groupPricing.Controls.Add(lblRetailPrice);
            groupPricing.Controls.Add(numMinimumPrice);
            groupPricing.Controls.Add(lblMinimumPrice);
            groupPricing.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupPricing.Location = new Point(10, 10);
            groupPricing.Name = "groupPricing";
            groupPricing.Size = new Size(560, 280);
            groupPricing.TabIndex = 0;
            groupPricing.TabStop = false;
            groupPricing.Text = "أسعار البيع";
            // 
            // numPurchasePrice
            // 
            numPurchasePrice.DecimalPlaces = 2;
            numPurchasePrice.Font = new Font("Segoe UI", 9F);
            numPurchasePrice.Location = new Point(20, 80);
            numPurchasePrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numPurchasePrice.Name = "numPurchasePrice";
            numPurchasePrice.Size = new Size(350, 23);
            numPurchasePrice.TabIndex = 1;
            numPurchasePrice.ValueChanged += numPurchasePrice_ValueChanged;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Segoe UI", 9F);
            label3.Location = new Point(376, 83);
            label3.Name = "label3";
            label3.Size = new Size(63, 15);
            label3.TabIndex = 0;
            label3.Text = "سعر الشراء:";
            // 
            // numSalePrice
            // 
            numSalePrice.DecimalPlaces = 2;
            numSalePrice.Font = new Font("Segoe UI", 9F);
            numSalePrice.Location = new Point(20, 130);
            numSalePrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numSalePrice.Name = "numSalePrice";
            numSalePrice.Size = new Size(350, 23);
            numSalePrice.TabIndex = 3;
            numSalePrice.ValueChanged += numSalePrice_ValueChanged;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new Font("Segoe UI", 9F);
            label4.Location = new Point(376, 133);
            label4.Name = "label4";
            label4.Size = new Size(57, 15);
            label4.TabIndex = 2;
            label4.Text = "سعر البيع:";
            // 
            // numWholesalePrice
            // 
            numWholesalePrice.DecimalPlaces = 2;
            numWholesalePrice.Font = new Font("Segoe UI", 9F);
            numWholesalePrice.Location = new Point(20, 180);
            numWholesalePrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numWholesalePrice.Name = "numWholesalePrice";
            numWholesalePrice.Size = new Size(350, 23);
            numWholesalePrice.TabIndex = 5;
            // 
            // lblWholesalePrice
            // 
            lblWholesalePrice.AutoSize = true;
            lblWholesalePrice.Font = new Font("Segoe UI", 9F);
            lblWholesalePrice.Location = new Point(376, 183);
            lblWholesalePrice.Name = "lblWholesalePrice";
            lblWholesalePrice.Size = new Size(66, 15);
            lblWholesalePrice.TabIndex = 4;
            lblWholesalePrice.Text = "سعر الجملة:";
            // 
            // numHalfWholesalePrice
            // 
            numHalfWholesalePrice.DecimalPlaces = 2;
            numHalfWholesalePrice.Font = new Font("Segoe UI", 9F);
            numHalfWholesalePrice.Location = new Point(20, 230);
            numHalfWholesalePrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numHalfWholesalePrice.Name = "numHalfWholesalePrice";
            numHalfWholesalePrice.Size = new Size(350, 23);
            numHalfWholesalePrice.TabIndex = 7;
            // 
            // lblHalfWholesalePrice
            // 
            lblHalfWholesalePrice.AutoSize = true;
            lblHalfWholesalePrice.Font = new Font("Segoe UI", 9F);
            lblHalfWholesalePrice.Location = new Point(376, 233);
            lblHalfWholesalePrice.Name = "lblHalfWholesalePrice";
            lblHalfWholesalePrice.Size = new Size(97, 15);
            lblHalfWholesalePrice.TabIndex = 6;
            lblHalfWholesalePrice.Text = "سعر نصف الجملة:";
            // 
            // numRetailPrice
            // 
            numRetailPrice.DecimalPlaces = 2;
            numRetailPrice.Font = new Font("Segoe UI", 9F);
            numRetailPrice.Location = new Point(20, 280);
            numRetailPrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numRetailPrice.Name = "numRetailPrice";
            numRetailPrice.Size = new Size(350, 23);
            numRetailPrice.TabIndex = 9;
            // 
            // lblRetailPrice
            // 
            lblRetailPrice.AutoSize = true;
            lblRetailPrice.Font = new Font("Segoe UI", 9F);
            lblRetailPrice.Location = new Point(376, 283);
            lblRetailPrice.Name = "lblRetailPrice";
            lblRetailPrice.Size = new Size(67, 15);
            lblRetailPrice.TabIndex = 8;
            lblRetailPrice.Text = "سعر التجزئة:";
            // 
            // numMinimumPrice
            // 
            numMinimumPrice.DecimalPlaces = 2;
            numMinimumPrice.Font = new Font("Segoe UI", 9F);
            numMinimumPrice.Location = new Point(20, 330);
            numMinimumPrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numMinimumPrice.Name = "numMinimumPrice";
            numMinimumPrice.Size = new Size(350, 23);
            numMinimumPrice.TabIndex = 11;
            // 
            // lblMinimumPrice
            // 
            lblMinimumPrice.AutoSize = true;
            lblMinimumPrice.Font = new Font("Segoe UI", 9F);
            lblMinimumPrice.Location = new Point(376, 333);
            lblMinimumPrice.Name = "lblMinimumPrice";
            lblMinimumPrice.Size = new Size(78, 15);
            lblMinimumPrice.TabIndex = 10;
            lblMinimumPrice.Text = "أقل سعر للبيع:";
            // 
            // tabPage3
            // 
            tabPage3.Controls.Add(btnRemoveImage);
            tabPage3.Controls.Add(btnSelectImage);
            tabPage3.Controls.Add(picProductImage);
            tabPage3.Controls.Add(chkIsActiveForSale);
            tabPage3.Controls.Add(chkIsActiveForPurchase);
            tabPage3.Controls.Add(numTaxPercentage);
            tabPage3.Controls.Add(lblTaxPercentage);
            tabPage3.Controls.Add(numDiscountPercentage);
            tabPage3.Controls.Add(lblDiscountPercentage);
            tabPage3.Controls.Add(numOpeningBalance);
            tabPage3.Controls.Add(lblOpeningBalance);
            tabPage3.Location = new Point(4, 24);
            tabPage3.Name = "tabPage3";
            tabPage3.Padding = new Padding(3);
            tabPage3.Size = new Size(533, 566);
            tabPage3.TabIndex = 2;
            tabPage3.Text = "المخزون والإعدادات";
            tabPage3.UseVisualStyleBackColor = true;
            // 
            // btnRemoveImage
            // 
            btnRemoveImage.BackColor = Color.FromArgb(231, 76, 60);
            btnRemoveImage.FlatStyle = FlatStyle.Flat;
            btnRemoveImage.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnRemoveImage.ForeColor = Color.White;
            btnRemoveImage.Location = new Point(240, 340);
            btnRemoveImage.Name = "btnRemoveImage";
            btnRemoveImage.Size = new Size(100, 30);
            btnRemoveImage.TabIndex = 10;
            btnRemoveImage.Text = "إزالة الصورة";
            btnRemoveImage.UseVisualStyleBackColor = false;
            btnRemoveImage.Click += btnRemoveImage_Click;
            // 
            // btnSelectImage
            // 
            btnSelectImage.BackColor = Color.FromArgb(52, 152, 219);
            btnSelectImage.FlatStyle = FlatStyle.Flat;
            btnSelectImage.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSelectImage.ForeColor = Color.White;
            btnSelectImage.Location = new Point(240, 300);
            btnSelectImage.Name = "btnSelectImage";
            btnSelectImage.Size = new Size(100, 30);
            btnSelectImage.TabIndex = 9;
            btnSelectImage.Text = "اختيار صورة";
            btnSelectImage.UseVisualStyleBackColor = false;
            btnSelectImage.Click += btnSelectImage_Click;
            // 
            // picProductImage
            // 
            picProductImage.BorderStyle = BorderStyle.FixedSingle;
            picProductImage.Location = new Point(20, 300);
            picProductImage.Name = "picProductImage";
            picProductImage.Size = new Size(200, 200);
            picProductImage.SizeMode = PictureBoxSizeMode.Zoom;
            picProductImage.TabIndex = 8;
            picProductImage.TabStop = false;
            // 
            // chkIsActiveForSale
            // 
            chkIsActiveForSale.AutoSize = true;
            chkIsActiveForSale.Checked = true;
            chkIsActiveForSale.CheckState = CheckState.Checked;
            chkIsActiveForSale.Font = new Font("Segoe UI", 9F);
            chkIsActiveForSale.Location = new Point(23, 263);
            chkIsActiveForSale.Name = "chkIsActiveForSale";
            chkIsActiveForSale.Size = new Size(76, 19);
            chkIsActiveForSale.TabIndex = 7;
            chkIsActiveForSale.Text = "فعال للبيع";
            chkIsActiveForSale.UseVisualStyleBackColor = true;
            // 
            // chkIsActiveForPurchase
            // 
            chkIsActiveForPurchase.AutoSize = true;
            chkIsActiveForPurchase.Checked = true;
            chkIsActiveForPurchase.CheckState = CheckState.Checked;
            chkIsActiveForPurchase.Font = new Font("Segoe UI", 9F);
            chkIsActiveForPurchase.Location = new Point(23, 233);
            chkIsActiveForPurchase.Name = "chkIsActiveForPurchase";
            chkIsActiveForPurchase.Size = new Size(82, 19);
            chkIsActiveForPurchase.TabIndex = 6;
            chkIsActiveForPurchase.Text = "فعال للشراء";
            chkIsActiveForPurchase.UseVisualStyleBackColor = true;
            // 
            // numTaxPercentage
            // 
            numTaxPercentage.DecimalPlaces = 2;
            numTaxPercentage.Font = new Font("Segoe UI", 9F);
            numTaxPercentage.Location = new Point(20, 180);
            numTaxPercentage.Name = "numTaxPercentage";
            numTaxPercentage.Size = new Size(350, 23);
            numTaxPercentage.TabIndex = 5;
            // 
            // lblTaxPercentage
            // 
            lblTaxPercentage.AutoSize = true;
            lblTaxPercentage.Font = new Font("Segoe UI", 9F);
            lblTaxPercentage.Location = new Point(379, 186);
            lblTaxPercentage.Name = "lblTaxPercentage";
            lblTaxPercentage.Size = new Size(75, 15);
            lblTaxPercentage.TabIndex = 4;
            lblTaxPercentage.Text = "نسبة الضريبة:";
            // 
            // numDiscountPercentage
            // 
            numDiscountPercentage.DecimalPlaces = 2;
            numDiscountPercentage.Font = new Font("Segoe UI", 9F);
            numDiscountPercentage.Location = new Point(20, 130);
            numDiscountPercentage.Name = "numDiscountPercentage";
            numDiscountPercentage.Size = new Size(350, 23);
            numDiscountPercentage.TabIndex = 3;
            // 
            // lblDiscountPercentage
            // 
            lblDiscountPercentage.AutoSize = true;
            lblDiscountPercentage.Font = new Font("Segoe UI", 9F);
            lblDiscountPercentage.Location = new Point(379, 136);
            lblDiscountPercentage.Name = "lblDiscountPercentage";
            lblDiscountPercentage.Size = new Size(70, 15);
            lblDiscountPercentage.TabIndex = 2;
            lblDiscountPercentage.Text = "نسبة الخصم:";
            // 
            // numOpeningBalance
            // 
            numOpeningBalance.DecimalPlaces = 2;
            numOpeningBalance.Font = new Font("Segoe UI", 9F);
            numOpeningBalance.Location = new Point(20, 80);
            numOpeningBalance.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numOpeningBalance.Name = "numOpeningBalance";
            numOpeningBalance.Size = new Size(350, 23);
            numOpeningBalance.TabIndex = 1;
            // 
            // lblOpeningBalance
            // 
            lblOpeningBalance.AutoSize = true;
            lblOpeningBalance.Font = new Font("Segoe UI", 9F);
            lblOpeningBalance.Location = new Point(379, 86);
            lblOpeningBalance.Name = "lblOpeningBalance";
            lblOpeningBalance.Size = new Size(90, 15);
            lblOpeningBalance.TabIndex = 0;
            lblOpeningBalance.Text = "الرصيد الافتتاحي:";
            // 
            // tabPage4
            // 
            tabPage4.Location = new Point(4, 24);
            tabPage4.Name = "tabPage4";
            tabPage4.Size = new Size(533, 566);
            tabPage4.TabIndex = 3;
            // 
            // groupInventory
            // 
            groupInventory.Location = new Point(0, 0);
            groupInventory.Name = "groupInventory";
            groupInventory.Size = new Size(200, 100);
            groupInventory.TabIndex = 0;
            groupInventory.TabStop = false;
            // 
            // groupStatus
            // 
            groupStatus.Location = new Point(0, 0);
            groupStatus.Name = "groupStatus";
            groupStatus.Size = new Size(200, 100);
            groupStatus.TabIndex = 0;
            groupStatus.TabStop = false;
            // 
            // groupImage
            // 
            groupImage.Location = new Point(0, 0);
            groupImage.Name = "groupImage";
            groupImage.Size = new Size(200, 100);
            groupImage.TabIndex = 0;
            groupImage.TabStop = false;
            // 
            // panel2
            // 
            panel2.Controls.Add(btnRefresh);
            panel2.Controls.Add(btnCancel);
            panel2.Controls.Add(btnSave);
            panel2.Controls.Add(btnDelete);
            panel2.Controls.Add(btnEdit);
            panel2.Controls.Add(btnNew);
            panel2.Dock = DockStyle.Bottom;
            panel2.Location = new Point(0, 678);
            panel2.Name = "panel2";
            panel2.Size = new Size(1284, 60);
            panel2.TabIndex = 1;
            // 
            // btnRefresh
            // 
            btnRefresh.BackColor = Color.FromArgb(52, 73, 94);
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Location = new Point(12, 15);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(80, 30);
            btnRefresh.TabIndex = 5;
            btnRefresh.Text = "تحديث";
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // btnCancel
            // 
            btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnCancel.ForeColor = Color.White;
            btnCancel.Location = new Point(110, 15);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(80, 30);
            btnCancel.TabIndex = 4;
            btnCancel.Text = "إلغاء";
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.Click += btnCancel_Click;
            // 
            // btnSave
            // 
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSave.ForeColor = Color.White;
            btnSave.Location = new Point(208, 15);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(80, 30);
            btnSave.TabIndex = 3;
            btnSave.Text = "حفظ";
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += btnSave_Click;
            // 
            // btnDelete
            // 
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.FlatStyle = FlatStyle.Flat;
            btnDelete.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnDelete.ForeColor = Color.White;
            btnDelete.Location = new Point(306, 15);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(80, 30);
            btnDelete.TabIndex = 2;
            btnDelete.Text = "حذف";
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnEdit
            // 
            btnEdit.BackColor = Color.FromArgb(230, 126, 34);
            btnEdit.FlatStyle = FlatStyle.Flat;
            btnEdit.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnEdit.ForeColor = Color.White;
            btnEdit.Location = new Point(404, 15);
            btnEdit.Name = "btnEdit";
            btnEdit.Size = new Size(80, 30);
            btnEdit.TabIndex = 1;
            btnEdit.Text = "تعديل";
            btnEdit.UseVisualStyleBackColor = false;
            btnEdit.Click += btnEdit_Click;
            // 
            // btnNew
            // 
            btnNew.BackColor = Color.FromArgb(52, 152, 219);
            btnNew.FlatStyle = FlatStyle.Flat;
            btnNew.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnNew.ForeColor = Color.White;
            btnNew.Location = new Point(502, 15);
            btnNew.Name = "btnNew";
            btnNew.Size = new Size(80, 30);
            btnNew.TabIndex = 0;
            btnNew.Text = "جديد";
            btnNew.UseVisualStyleBackColor = false;
            btnNew.Click += btnNew_Click;
            // 
            // statusStrip1
            // 
            statusStrip1.Items.AddRange(new ToolStripItem[] { lblStatus });
            statusStrip1.Location = new Point(0, 738);
            statusStrip1.Name = "statusStrip1";
            statusStrip1.Size = new Size(1284, 22);
            statusStrip1.TabIndex = 2;
            statusStrip1.Text = "statusStrip1";
            // 
            // lblStatus
            // 
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(30, 17);
            lblStatus.Text = "جاهز";
            // 
            // pnlHeader
            // 
            pnlHeader.BackColor = Color.FromArgb(52, 73, 94);
            pnlHeader.Controls.Add(lblLowStockProducts);
            pnlHeader.Controls.Add(lblActiveProducts);
            pnlHeader.Controls.Add(lblTotalProducts);
            pnlHeader.Controls.Add(lblNewProducts);
            pnlHeader.Controls.Add(lblCurrentStock);
            pnlHeader.Controls.Add(lblInventoryValue);
            pnlHeader.Dock = DockStyle.Top;
            pnlHeader.Location = new Point(0, 0);
            pnlHeader.Name = "pnlHeader";
            pnlHeader.Size = new Size(1284, 60);
            pnlHeader.TabIndex = 3;
            // 
            // lblLowStockProducts
            // 
            lblLowStockProducts.AutoSize = true;
            lblLowStockProducts.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblLowStockProducts.ForeColor = Color.FromArgb(231, 76, 60);
            lblLowStockProducts.Location = new Point(12, 20);
            lblLowStockProducts.Name = "lblLowStockProducts";
            lblLowStockProducts.Size = new Size(165, 19);
            lblLowStockProducts.TabIndex = 2;
            lblLowStockProducts.Text = "أصناف تحت الحد الأدنى: 0";
            // 
            // lblActiveProducts
            // 
            lblActiveProducts.AutoSize = true;
            lblActiveProducts.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblActiveProducts.ForeColor = Color.FromArgb(46, 204, 113);
            lblActiveProducts.Location = new Point(400, 20);
            lblActiveProducts.Name = "lblActiveProducts";
            lblActiveProducts.Size = new Size(124, 19);
            lblActiveProducts.TabIndex = 1;
            lblActiveProducts.Text = "الأصناف النشطة: 0";
            // 
            // lblTotalProducts
            // 
            lblTotalProducts.AutoSize = true;
            lblTotalProducts.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblTotalProducts.ForeColor = Color.White;
            lblTotalProducts.Location = new Point(800, 20);
            lblTotalProducts.Name = "lblTotalProducts";
            lblTotalProducts.Size = new Size(120, 19);
            lblTotalProducts.TabIndex = 0;
            lblTotalProducts.Text = "إجمالي الأصناف: 0";
            // 
            // lblNewProducts
            // 
            lblNewProducts.AutoSize = true;
            lblNewProducts.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblNewProducts.ForeColor = Color.FromArgb(52, 152, 219);
            lblNewProducts.Location = new Point(200, 20);
            lblNewProducts.Name = "lblNewProducts";
            lblNewProducts.Size = new Size(104, 19);
            lblNewProducts.TabIndex = 3;
            lblNewProducts.Text = "أصناف جديدة: 0";
            // 
            // lblCurrentStock
            // 
            lblCurrentStock.AutoSize = true;
            lblCurrentStock.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblCurrentStock.ForeColor = Color.FromArgb(155, 89, 182);
            lblCurrentStock.Location = new Point(600, 20);
            lblCurrentStock.Name = "lblCurrentStock";
            lblCurrentStock.Size = new Size(113, 19);
            lblCurrentStock.TabIndex = 4;
            lblCurrentStock.Text = "المخزون الحالي: 0";
            // 
            // lblInventoryValue
            // 
            lblInventoryValue.AutoSize = true;
            lblInventoryValue.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblInventoryValue.ForeColor = Color.FromArgb(230, 126, 34);
            lblInventoryValue.Location = new Point(1000, 20);
            lblInventoryValue.Name = "lblInventoryValue";
            lblInventoryValue.Size = new Size(102, 19);
            lblInventoryValue.TabIndex = 5;
            lblInventoryValue.Text = "قيمة المخزون: 0";
            // 
            // lblMinimumStock
            // 
            lblMinimumStock.AutoSize = true;
            lblMinimumStock.Font = new Font("Segoe UI", 9F);
            lblMinimumStock.Location = new Point(376, 463);
            lblMinimumStock.Name = "lblMinimumStock";
            lblMinimumStock.Size = new Size(68, 15);
            lblMinimumStock.TabIndex = 20;
            lblMinimumStock.Text = "الحد الأدنى للمخزون:";
            // 
            // panel3
            // 
            panel3.Controls.Add(splitContainer1);
            panel3.Dock = DockStyle.Fill;
            panel3.Location = new Point(0, 60);
            panel3.Name = "panel3";
            panel3.Size = new Size(1284, 618);
            panel3.TabIndex = 4;
            // 
            // ProductsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(240, 244, 247);
            ClientSize = new Size(1284, 760);
            Controls.Add(panel3);
            Controls.Add(pnlHeader);
            Controls.Add(panel2);
            Controls.Add(statusStrip1);
            Font = new Font("Segoe UI", 9F);
            Name = "ProductsForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "إدارة الأصناف";
            WindowState = FormWindowState.Maximized;
            Load += ProductsForm_Load;
            splitContainer1.Panel1.ResumeLayout(false);
            splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer1).EndInit();
            splitContainer1.ResumeLayout(false);
            groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvProducts).EndInit();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            groupBox2.ResumeLayout(false);
            tabControl1.ResumeLayout(false);
            tabPage1.ResumeLayout(false);
            groupClassification.ResumeLayout(false);
            groupClassification.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numMinStock).EndInit();
            groupBasicInfo.ResumeLayout(false);
            groupBasicInfo.PerformLayout();
            tabPage2.ResumeLayout(false);
            groupCalculations.ResumeLayout(false);
            groupCalculations.PerformLayout();
            groupPricing.ResumeLayout(false);
            groupPricing.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numPurchasePrice).EndInit();
            ((System.ComponentModel.ISupportInitialize)numSalePrice).EndInit();
            ((System.ComponentModel.ISupportInitialize)numWholesalePrice).EndInit();
            ((System.ComponentModel.ISupportInitialize)numHalfWholesalePrice).EndInit();
            ((System.ComponentModel.ISupportInitialize)numRetailPrice).EndInit();
            ((System.ComponentModel.ISupportInitialize)numMinimumPrice).EndInit();
            tabPage3.ResumeLayout(false);
            tabPage3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)picProductImage).EndInit();
            ((System.ComponentModel.ISupportInitialize)numTaxPercentage).EndInit();
            ((System.ComponentModel.ISupportInitialize)numDiscountPercentage).EndInit();
            ((System.ComponentModel.ISupportInitialize)numOpeningBalance).EndInit();
            panel2.ResumeLayout(false);
            statusStrip1.ResumeLayout(false);
            statusStrip1.PerformLayout();
            pnlHeader.ResumeLayout(false);
            pnlHeader.PerformLayout();
            panel3.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.DataGridView dgvProducts;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.ComboBox cmbCategoryFilter;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Label lblSearch;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.NumericUpDown numMinStock;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.CheckBox chkIsActive;
        private System.Windows.Forms.ComboBox cmbUnit;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.ComboBox cmbSubCategory;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.ComboBox cmbMainCategory;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox txtBarcode;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox txtProductName;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox txtProductCode;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.NumericUpDown numSalePrice;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numPurchasePrice;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnEdit;
        private System.Windows.Forms.Button btnNew;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel lblStatus;
        private System.Windows.Forms.Panel pnlHeader;
        private System.Windows.Forms.Label lblLowStockProducts;
        private System.Windows.Forms.Label lblActiveProducts;
        private System.Windows.Forms.Label lblTotalProducts;
        private System.Windows.Forms.ComboBox cmbWarehouse;
        private System.Windows.Forms.Label lblWarehouse;
        private System.Windows.Forms.TextBox txtProductNameEn;
        private System.Windows.Forms.Label lblProductNameEn;

        private System.Windows.Forms.Label lblMinimumStock;
        private System.Windows.Forms.Label lblCurrentStock;
        private System.Windows.Forms.Label lblInventoryValue;
        private System.Windows.Forms.Label lblNewProducts;
        private System.Windows.Forms.NumericUpDown numWholesalePrice;
        private System.Windows.Forms.Label lblWholesalePrice;
        private System.Windows.Forms.NumericUpDown numHalfWholesalePrice;
        private System.Windows.Forms.Label lblHalfWholesalePrice;
        private System.Windows.Forms.NumericUpDown numRetailPrice;
        private System.Windows.Forms.Label lblRetailPrice;
        private System.Windows.Forms.NumericUpDown numMinimumPrice;
        private System.Windows.Forms.Label lblMinimumPrice;
        private System.Windows.Forms.NumericUpDown numOpeningBalance;
        private System.Windows.Forms.Label lblOpeningBalance;
        private System.Windows.Forms.NumericUpDown numDiscountPercentage;
        private System.Windows.Forms.Label lblDiscountPercentage;
        private System.Windows.Forms.NumericUpDown numTaxPercentage;
        private System.Windows.Forms.Label lblTaxPercentage;
        private System.Windows.Forms.CheckBox chkIsActiveForPurchase;
        private System.Windows.Forms.CheckBox chkIsActiveForSale;
        private System.Windows.Forms.PictureBox picProductImage;
        private System.Windows.Forms.Button btnSelectImage;
        private System.Windows.Forms.Button btnRemoveImage;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.GroupBox groupBasicInfo;
        private System.Windows.Forms.GroupBox groupClassification;
        private System.Windows.Forms.GroupBox groupPricing;
        private System.Windows.Forms.GroupBox groupCalculations;
        private System.Windows.Forms.GroupBox groupInventory;
        private System.Windows.Forms.GroupBox groupStatus;
        private System.Windows.Forms.GroupBox groupImage;
        private System.Windows.Forms.Label lblProfitMargin;
        private Panel panel3;
    }
}
