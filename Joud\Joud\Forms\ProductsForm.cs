using Joud.BLL;
using Joud.Models;
using System.Drawing;
using System.IO;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة الأصناف
    /// Products Management Form
    /// </summary>
    public partial class ProductsForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly ProductService _productService;
        private List<Product> _products;
        private Product? _selectedProduct;
        private ProductReferenceData _referenceData;
        private bool _isEditing = false;

        public ProductsForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _productService = new ProductService();
            _products = new List<Product>();
            _referenceData = new ProductReferenceData();
            
            InitializeFormSettings();
            SetupDataGridView();
            LoadDataAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة الأصناف - نظام جود للمحاسبة المالية";
            this.Size = new Size(1400, 750);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private void SetupDataGridView()
        {
            // إعداد DataGridView
            dgvProducts.AutoGenerateColumns = false;
            dgvProducts.AllowUserToAddRows = false;
            dgvProducts.AllowUserToDeleteRows = false;
            dgvProducts.ReadOnly = true;
            dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProducts.MultiSelect = false;
            dgvProducts.BackgroundColor = Color.White;
            dgvProducts.BorderStyle = BorderStyle.None;
            dgvProducts.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvProducts.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvProducts.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvProducts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvProducts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvProducts.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvProducts.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductCode",
                HeaderText = "كود الصنف",
                DataPropertyName = "ProductCode",
                Width = 100,
                ReadOnly = true
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductName",
                HeaderText = "اسم الصنف",
                DataPropertyName = "ProductName",
                Width = 200,
                ReadOnly = true
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Barcode",
                HeaderText = "الباركود",
                DataPropertyName = "Barcode",
                Width = 120,
                ReadOnly = true
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MainCategoryName",
                HeaderText = "الفئة الرئيسية",
                Width = 120,
                ReadOnly = true
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SubCategoryName",
                HeaderText = "الفئة الفرعية",
                Width = 120,
                ReadOnly = true
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitName",
                HeaderText = "الوحدة",
                Width = 80,
                ReadOnly = true
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PurchasePrice",
                HeaderText = "سعر الشراء",
                DataPropertyName = "PurchasePrice",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SalePrice",
                HeaderText = "سعر البيع",
                DataPropertyName = "SalePrice",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentStock",
                HeaderText = "المخزون الحالي",
                DataPropertyName = "CurrentStock",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MinimumStock",
                HeaderText = "الحد الأدنى",
                DataPropertyName = "MinimumStock",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            // أحداث DataGridView
            dgvProducts.SelectionChanged += DgvProducts_SelectionChanged;
            dgvProducts.CellDoubleClick += DgvProducts_CellDoubleClick;
            dgvProducts.DataBindingComplete += DgvProducts_DataBindingComplete;
        }

        private async void LoadDataAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل البيانات...";
                lblStatus.ForeColor = Color.Blue;

                // تحميل البيانات المرجعية
                _referenceData = await _productService.GetProductReferenceDataAsync(_currentCompany.CompanyId);
                
                // إعداد ComboBoxes
                SetupComboBoxes();

                // تحميل الأصناف
                await LoadProductsAsync();

                lblStatus.Text = $"تم تحميل {_products.Count} صنف";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void SetupComboBoxes()
        {
            // إعداد ComboBox للفئات الرئيسية
            cmbMainCategory.DataSource = _referenceData.MainCategories;
            cmbMainCategory.DisplayMember = "CategoryName";
            cmbMainCategory.ValueMember = "MainCategoryId";
            cmbMainCategory.SelectedIndex = -1;

            // إعداد ComboBox للوحدات
            cmbUnit.DataSource = _referenceData.Units;
            cmbUnit.DisplayMember = "UnitName";
            cmbUnit.ValueMember = "UnitId";
            cmbUnit.SelectedIndex = -1;

            // إعداد ComboBox للمخازن
            cmbWarehouse.DataSource = _referenceData.Warehouses;
            cmbWarehouse.DisplayMember = "WarehouseName";
            cmbWarehouse.ValueMember = "WarehouseId";
            cmbWarehouse.SelectedIndex = -1;

            // إعداد ComboBox للفئات الفرعية (فارغ في البداية)
            cmbSubCategory.DataSource = null;
            cmbSubCategory.SelectedIndex = -1;
        }

        private async Task LoadProductsAsync()
        {
            _products = await _productService.GetAllProductsAsync(_currentCompany.CompanyId);
            dgvProducts.DataSource = _products;
            
            // تحديث الإحصائيات
            await UpdateStatisticsAsync();
        }

        private void DgvProducts_DataBindingComplete(object? sender, DataGridViewBindingCompleteEventArgs e)
        {
            // تحديث البيانات المحسوبة
            foreach (DataGridViewRow row in dgvProducts.Rows)
            {
                if (row.DataBoundItem is Product product)
                {
                    row.Cells["MainCategoryName"].Value = product.MainCategory?.CategoryName ?? "";
                    row.Cells["SubCategoryName"].Value = product.SubCategory?.CategoryName ?? "";
                    row.Cells["UnitName"].Value = product.Unit?.UnitName ?? "";
                    
                    // تلوين الصفوف حسب المخزون
                    if (product.CurrentStock <= product.MinimumStock)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                        row.DefaultCellStyle.ForeColor = Color.FromArgb(169, 68, 66);
                    }
                }
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _productService.GetProductStatisticsAsync(_currentCompany.CompanyId);
                lblTotalProducts.Text = $"إجمالي الأصناف: {stats.TotalProducts}";
                lblLowStockProducts.Text = $"أصناف بمخزون منخفض: {stats.ProductsWithLowStock}";
                lblInventoryValue.Text = $"قيمة المخزون: {stats.TotalInventoryValue:N2} {_currentCompany.Currency}";
                lblNewProducts.Text = $"أصناف جديدة هذا الشهر: {stats.NewProductsThisMonth}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        private void DgvProducts_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count > 0)
            {
                _selectedProduct = dgvProducts.SelectedRows[0].DataBoundItem as Product;
                LoadProductDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
            }
            else
            {
                _selectedProduct = null;
                ClearProductDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void DgvProducts_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadProductDetails()
        {
            if (_selectedProduct == null) return;

            txtProductCode.Text = _selectedProduct.ProductCode;
            txtProductName.Text = _selectedProduct.ProductName;
            txtProductNameEn.Text = _selectedProduct.ProductNameEn;
            txtBarcode.Text = _selectedProduct.Barcode;
            txtDescription.Text = _selectedProduct.Description;
            
            // اختيار الفئة الرئيسية
            cmbMainCategory.SelectedValue = _selectedProduct.MainCategoryId;
            
            // تحديث الفئات الفرعية وإختيار الفئة الفرعية
            UpdateSubCategories();
            if (_selectedProduct.SubCategoryId.HasValue)
                cmbSubCategory.SelectedValue = _selectedProduct.SubCategoryId.Value;
            
            // اختيار الوحدة والمخزن
            cmbUnit.SelectedValue = _selectedProduct.UnitId;
            cmbWarehouse.SelectedValue = _selectedProduct.WarehouseId;
            
            // الأسعار والمخزون
            numPurchasePrice.Value = _selectedProduct.PurchasePrice;
            numSalePrice.Value = _selectedProduct.SalePrice;
            numWholesalePrice.Value = _selectedProduct.WholesalePrice;
            numHalfWholesalePrice.Value = _selectedProduct.HalfWholesalePrice;
            numRetailPrice.Value = _selectedProduct.RetailPrice;
            numMinimumPrice.Value = _selectedProduct.MinimumPrice;
            numMinStock.Value = _selectedProduct.MinimumStock;
            numOpeningBalance.Value = _selectedProduct.OpeningBalance;
            numDiscountPercentage.Value = _selectedProduct.DiscountPercentage;
            numTaxPercentage.Value = _selectedProduct.TaxPercentage;

            // الحالات
            chkIsActive.Checked = _selectedProduct.IsActive;
            chkIsActiveForPurchase.Checked = _selectedProduct.IsActiveForPurchase;
            chkIsActiveForSale.Checked = _selectedProduct.IsActiveForSale;

            // الصورة
            if (_selectedProduct.ProductImage != null && _selectedProduct.ProductImage.Length > 0)
            {
                using (var ms = new MemoryStream(_selectedProduct.ProductImage))
                {
                    picProductImage.Image = Image.FromStream(ms);
                }
            }
            else
            {
                picProductImage.Image = null;
            }

            lblCurrentStock.Text = $"المخزون الحالي: {_selectedProduct.CurrentStock:N2}";

            // حساب هامش الربح
            CalculateProfitMargin();
        }

        private void ClearProductDetails()
        {
            txtProductCode.Clear();
            txtProductName.Clear();
            txtProductNameEn.Clear();
            txtBarcode.Clear();
            txtDescription.Clear();
            cmbMainCategory.SelectedIndex = -1;
            cmbSubCategory.SelectedIndex = -1;
            cmbUnit.SelectedIndex = -1;
            cmbWarehouse.SelectedIndex = -1;
            numPurchasePrice.Value = 0;
            numSalePrice.Value = 0;
            numWholesalePrice.Value = 0;
            numHalfWholesalePrice.Value = 0;
            numRetailPrice.Value = 0;
            numMinimumPrice.Value = 0;
            numMinStock.Value = 0;
            numOpeningBalance.Value = 0;
            numDiscountPercentage.Value = 0;
            numTaxPercentage.Value = 0;
            chkIsActive.Checked = true;
            chkIsActiveForPurchase.Checked = true;
            chkIsActiveForSale.Checked = true;
            picProductImage.Image?.Dispose();
            picProductImage.Image = null;
            lblCurrentStock.Text = "المخزون الحالي: 0.00";
        }

        private async void cmbMainCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                UpdateSubCategories();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الفئات الفرعية: {ex.Message}");
            }
        }

        private void UpdateSubCategories()
        {
            if (cmbMainCategory.SelectedValue != null)
            {
                int mainCategoryId = cmbMainCategory.SelectedValue != null ? (int)cmbMainCategory.SelectedValue : 0;
                var subCategories = _referenceData.SubCategories
                    .Where(sc => sc.MainCategoryId == mainCategoryId)
                    .ToList();
                
                cmbSubCategory.DataSource = subCategories;
                cmbSubCategory.DisplayMember = "CategoryName";
                cmbSubCategory.ValueMember = "SubCategoryId";
                cmbSubCategory.SelectedIndex = -1;
            }
            else
            {
                cmbSubCategory.DataSource = null;
            }
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                if (_referenceData.MainCategories.Count == 0)
                {
                    ShowError("يجب إنشاء فئة رئيسية واحدة على الأقل أولاً");
                    return;
                }

                if (_referenceData.Units.Count == 0)
                {
                    ShowError("يجب إنشاء وحدة واحدة على الأقل أولاً");
                    return;
                }

                if (_referenceData.Warehouses.Count == 0)
                {
                    ShowError("يجب إنشاء مخزن واحد على الأقل أولاً");
                    return;
                }

                _isEditing = false;
                _selectedProduct = null;
                ClearProductDetails();
                
                // إنشاء كود تلقائي
                string newCode = await _productService.GenerateProductCodeAsync(_currentCompany.CompanyId);
                txtProductCode.Text = newCode;
                
                SetEditMode(true);
                txtProductName.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء صنف جديد: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedProduct == null)
            {
                ShowError("يرجى اختيار صنف للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            txtProductName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ بيانات الصنف...";
                lblStatus.ForeColor = Color.Blue;

                var product = CreateProductFromInput();

                if (_isEditing && _selectedProduct != null)
                {
                    product.ProductId = _selectedProduct.ProductId;
                    product.CreatedDate = _selectedProduct.CreatedDate;
                    product.CreatedBy = _selectedProduct.CreatedBy;
                    product.CurrentStock = _selectedProduct.CurrentStock;
                    product.ModifiedBy = _currentUser.UserId;

                    await _productService.UpdateProductAsync(product);
                    lblStatus.Text = "تم تحديث بيانات الصنف بنجاح";
                }
                else
                {
                    product.CreatedBy = _currentUser.UserId;
                    product.CompanyId = _currentCompany.CompanyId;
                    product.CurrentStock = 0; // المخزون الابتدائي صفر

                    await _productService.AddProductAsync(product);
                    lblStatus.Text = "تم إضافة الصنف بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadProductsAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ بيانات الصنف: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedProduct != null)
            {
                LoadProductDetails();
            }
            else
            {
                ClearProductDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedProduct == null)
            {
                ShowError("يرجى اختيار صنف للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف الصنف '{_selectedProduct.ProductName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف الصنف...";
                    lblStatus.ForeColor = Color.Blue;

                    await _productService.DeleteProductAsync(_selectedProduct.ProductId, _currentUser.UserId);
                    
                    lblStatus.Text = "تم حذف الصنف بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadProductsAsync();
                    ClearProductDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف الصنف: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvProducts.DataSource = _products;
                }
                else
                {
                    var searchResults = await _productService.SearchProductsAsync(txtSearch.Text, _currentCompany.CompanyId);
                    dgvProducts.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataAsync();
        }

        private async void cmbCategoryFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                await LoadProductsAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تصفية الفئات: {ex.Message}");
            }
        }



        private async Task LoadSubCategoriesAsync()
        {
            try
            {
                // تحديث الفئات الفرعية بناءً على الفئة الرئيسية المختارة
                if (cmbMainCategory.SelectedValue != null)
                {
                    int mainCategoryId = cmbMainCategory.SelectedValue != null ? (int)cmbMainCategory.SelectedValue : 0;
                    var subCategories = _referenceData.SubCategories
                        .Where(sc => sc.MainCategoryId == mainCategoryId)
                        .ToList();

                    cmbSubCategory.DataSource = subCategories;
                    cmbSubCategory.DisplayMember = "CategoryName";
                    cmbSubCategory.ValueMember = "SubCategoryId";
                    cmbSubCategory.SelectedIndex = -1;
                }
                else
                {
                    cmbSubCategory.DataSource = null;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الفئات الفرعية: {ex.Message}");
            }
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtProductName.Text))
                errors.Add("اسم الصنف مطلوب");

            if (string.IsNullOrWhiteSpace(txtProductCode.Text))
                errors.Add("كود الصنف مطلوب");

            if (cmbMainCategory.SelectedValue == null)
                errors.Add("يجب اختيار الفئة الرئيسية");

            if (cmbUnit.SelectedValue == null)
                errors.Add("يجب اختيار الوحدة");

            if (cmbWarehouse.SelectedValue == null)
                errors.Add("يجب اختيار المخزن");

            if (numPurchasePrice.Value < 0)
                errors.Add("سعر الشراء لا يمكن أن يكون سالباً");

            if (numSalePrice.Value < 0)
                errors.Add("سعر البيع لا يمكن أن يكون سالباً");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private Product CreateProductFromInput()
        {
            byte[]? imageBytes = null;
            if (picProductImage.Image != null)
            {
                using (var ms = new MemoryStream())
                {
                    picProductImage.Image.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                    imageBytes = ms.ToArray();
                }
            }

            return new Product
            {
                ProductCode = txtProductCode.Text.Trim(),
                ProductName = txtProductName.Text.Trim(),
                ProductNameEn = txtProductNameEn.Text.Trim(),
                Barcode = txtBarcode.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                MainCategoryId = cmbMainCategory.SelectedValue != null ? (int)cmbMainCategory.SelectedValue : 0,
                SubCategoryId = cmbSubCategory.SelectedValue as int?,
                UnitId = cmbUnit.SelectedValue != null ? (int)cmbUnit.SelectedValue : 0,
                WarehouseId = cmbWarehouse.SelectedValue != null ? (int)cmbWarehouse.SelectedValue : 0,
                PurchasePrice = numPurchasePrice.Value,
                SalePrice = numSalePrice.Value,
                WholesalePrice = numWholesalePrice.Value,
                HalfWholesalePrice = numHalfWholesalePrice.Value,
                RetailPrice = numRetailPrice.Value,
                MinimumPrice = numMinimumPrice.Value,
                MinimumStock = numMinStock.Value,
                OpeningBalance = numOpeningBalance.Value,
                DiscountPercentage = numDiscountPercentage.Value,
                TaxPercentage = numTaxPercentage.Value,
                IsActive = chkIsActive.Checked,
                IsActiveForPurchase = chkIsActiveForPurchase.Checked,
                IsActiveForSale = chkIsActiveForSale.Checked,
                ProductImage = imageBytes
            };
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال
            txtProductCode.ReadOnly = isEditing && _isEditing; // الكود للقراءة فقط عند التعديل
            txtProductName.ReadOnly = !isEditing;
            txtProductNameEn.ReadOnly = !isEditing;
            txtBarcode.ReadOnly = !isEditing;
            txtDescription.ReadOnly = !isEditing;
            cmbMainCategory.Enabled = isEditing;
            cmbSubCategory.Enabled = isEditing;
            cmbUnit.Enabled = isEditing;
            cmbWarehouse.Enabled = isEditing;
            numPurchasePrice.ReadOnly = !isEditing;
            numSalePrice.ReadOnly = !isEditing;
            numWholesalePrice.ReadOnly = !isEditing;
            numHalfWholesalePrice.ReadOnly = !isEditing;
            numRetailPrice.ReadOnly = !isEditing;
            numMinimumPrice.ReadOnly = !isEditing;
            numMinStock.ReadOnly = !isEditing;
            numOpeningBalance.ReadOnly = !isEditing;
            numDiscountPercentage.ReadOnly = !isEditing;
            numTaxPercentage.ReadOnly = !isEditing;
            chkIsActive.Enabled = isEditing;
            chkIsActiveForPurchase.Enabled = isEditing;
            chkIsActiveForSale.Enabled = isEditing;
            btnSelectImage.Enabled = isEditing;
            btnRemoveImage.Enabled = isEditing;

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedProduct != null;
            btnDelete.Enabled = !isEditing && _selectedProduct != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            dgvProducts.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is ComboBox || 
                    control is NumericUpDown || control is DataGridView)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void ProductsForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
        }

        private void btnSelectImage_Click(object sender, EventArgs e)
        {
            try
            {
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
                    openFileDialog.Title = "اختيار صورة المنتج";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        picProductImage.Image = Image.FromFile(openFileDialog.FileName);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الصورة: {ex.Message}");
            }
        }

        private void btnRemoveImage_Click(object sender, EventArgs e)
        {
            try
            {
                picProductImage.Image?.Dispose();
                picProductImage.Image = null;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إزالة الصورة: {ex.Message}");
            }
        }

        private void CalculateProfitMargin()
        {
            try
            {
                if (numPurchasePrice.Value > 0 && numSalePrice.Value > 0)
                {
                    var margin = ((numSalePrice.Value - numPurchasePrice.Value) / numPurchasePrice.Value) * 100;
                    lblProfitMargin.Text = $"هامش الربح: {margin:F2}%";

                    // تلوين النص حسب هامش الربح
                    if (margin < 10)
                        lblProfitMargin.ForeColor = Color.FromArgb(231, 76, 60); // أحمر
                    else if (margin < 25)
                        lblProfitMargin.ForeColor = Color.FromArgb(243, 156, 18); // برتقالي
                    else
                        lblProfitMargin.ForeColor = Color.FromArgb(39, 174, 96); // أخضر
                }
                else
                {
                    lblProfitMargin.Text = "هامش الربح: 0.00%";
                    lblProfitMargin.ForeColor = Color.Gray;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حساب هامش الربح: {ex.Message}");
            }
        }

        private void numPurchasePrice_ValueChanged(object sender, EventArgs e)
        {
            CalculateProfitMargin();
        }

        private void numSalePrice_ValueChanged(object sender, EventArgs e)
        {
            CalculateProfitMargin();
        }
    }
}
