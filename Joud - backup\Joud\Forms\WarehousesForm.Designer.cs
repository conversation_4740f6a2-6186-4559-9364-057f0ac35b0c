namespace Joud.Forms
{
    partial class WarehousesForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.btnRefresh = new Button();
            
            this.pnlStatistics = new Panel();
            this.lblTotalWarehouses = new Label();
            this.lblWarehousesWithProducts = new Label();
            this.lblTotalProducts = new Label();
            this.lblTotalInvoices = new Label();
            
            this.pnlMain = new Panel();
            this.dgvWarehouses = new DataGridView();
            
            this.pnlDetails = new Panel();
            this.grpWarehouseDetails = new GroupBox();
            this.lblWarehouseCode = new Label();
            this.txtWarehouseCode = new TextBox();
            this.lblWarehouseName = new Label();
            this.txtWarehouseName = new TextBox();
            this.lblWarehouseNameEn = new Label();
            this.txtWarehouseNameEn = new TextBox();
            this.lblLocation = new Label();
            this.txtLocation = new TextBox();
            this.lblDescription = new Label();
            this.txtDescription = new TextBox();
            this.lblProductsCount = new Label();
            
            this.pnlButtons = new Panel();
            this.btnNew = new Button();
            this.btnEdit = new Button();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.btnDelete = new Button();
            this.lblStatus = new Label();

            this.SuspendLayout();

            // Header Panel
            this.pnlHeader.BackColor = Color.FromArgb(52, 73, 94);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Controls.Add(this.txtSearch);
            this.pnlHeader.Controls.Add(this.lblSearch);
            this.pnlHeader.Controls.Add(this.btnRefresh);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Size = new Size(1100, 60);

            this.lblTitle.Text = "إدارة المخازن";
            this.lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 15);

            this.lblSearch.Text = "البحث:";
            this.lblSearch.Font = new Font("Segoe UI", 10F);
            this.lblSearch.ForeColor = Color.White;
            this.lblSearch.Location = new Point(500, 20);

            this.txtSearch.PlaceholderText = "ابحث بالاسم أو الكود أو الموقع...";
            this.txtSearch.Font = new Font("Segoe UI", 10F);
            this.txtSearch.Location = new Point(250, 17);
            this.txtSearch.Size = new Size(240, 25);
            this.txtSearch.TextChanged += new EventHandler(this.txtSearch_TextChanged);

            this.btnRefresh.Text = "تحديث";
            this.btnRefresh.BackColor = Color.FromArgb(39, 174, 96);
            this.btnRefresh.FlatStyle = FlatStyle.Flat;
            this.btnRefresh.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnRefresh.ForeColor = Color.White;
            this.btnRefresh.Location = new Point(160, 15);
            this.btnRefresh.Size = new Size(80, 30);
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);

            // Statistics Panel
            this.pnlStatistics.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlStatistics.Controls.Add(this.lblTotalWarehouses);
            this.pnlStatistics.Controls.Add(this.lblWarehousesWithProducts);
            this.pnlStatistics.Controls.Add(this.lblTotalProducts);
            this.pnlStatistics.Controls.Add(this.lblTotalInvoices);
            this.pnlStatistics.Dock = DockStyle.Top;
            this.pnlStatistics.Location = new Point(0, 60);
            this.pnlStatistics.Size = new Size(1100, 40);

            this.lblTotalWarehouses.Text = "إجمالي المخازن: 0";
            this.lblTotalWarehouses.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalWarehouses.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalWarehouses.Location = new Point(850, 12);

            this.lblWarehousesWithProducts.Text = "مخازن تحتوي على أصناف: 0";
            this.lblWarehousesWithProducts.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblWarehousesWithProducts.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblWarehousesWithProducts.Location = new Point(650, 12);

            this.lblTotalProducts.Text = "إجمالي الأصناف: 0";
            this.lblTotalProducts.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalProducts.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalProducts.Location = new Point(450, 12);

            this.lblTotalInvoices.Text = "إجمالي الفواتير: 0";
            this.lblTotalInvoices.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalInvoices.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalInvoices.Location = new Point(250, 12);

            // Main Panel with DataGridView
            this.pnlMain.Controls.Add(this.dgvWarehouses);
            this.pnlMain.Dock = DockStyle.Left;
            this.pnlMain.Location = new Point(0, 100);
            this.pnlMain.Size = new Size(650, 490);

            this.dgvWarehouses.BackgroundColor = Color.White;
            this.dgvWarehouses.BorderStyle = BorderStyle.None;
            this.dgvWarehouses.Dock = DockStyle.Fill;

            // Details Panel
            this.pnlDetails.BackColor = Color.FromArgb(240, 244, 247);
            this.pnlDetails.Controls.Add(this.grpWarehouseDetails);
            this.pnlDetails.Dock = DockStyle.Fill;
            this.pnlDetails.Location = new Point(650, 100);
            this.pnlDetails.Size = new Size(450, 490);

            this.grpWarehouseDetails.Text = "تفاصيل المخزن";
            this.grpWarehouseDetails.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpWarehouseDetails.Location = new Point(20, 20);
            this.grpWarehouseDetails.Size = new Size(410, 350);

            // Warehouse Details Controls
            // Warehouse Code
            this.lblWarehouseCode.Text = "كود المخزن:";
            this.lblWarehouseCode.Font = new Font("Segoe UI", 9F);
            this.lblWarehouseCode.Location = new Point(320, 30);
            this.lblWarehouseCode.Size = new Size(80, 15);

            this.txtWarehouseCode.Font = new Font("Segoe UI", 9F);
            this.txtWarehouseCode.Location = new Point(20, 27);
            this.txtWarehouseCode.Size = new Size(290, 23);
            this.txtWarehouseCode.ReadOnly = true;

            // Warehouse Name
            this.lblWarehouseName.Text = "اسم المخزن:";
            this.lblWarehouseName.Font = new Font("Segoe UI", 9F);
            this.lblWarehouseName.Location = new Point(320, 75);
            this.lblWarehouseName.Size = new Size(80, 15);

            this.txtWarehouseName.Font = new Font("Segoe UI", 9F);
            this.txtWarehouseName.Location = new Point(20, 72);
            this.txtWarehouseName.Size = new Size(290, 23);

            // Warehouse Name English
            this.lblWarehouseNameEn.Text = "الاسم بالإنجليزية:";
            this.lblWarehouseNameEn.Font = new Font("Segoe UI", 9F);
            this.lblWarehouseNameEn.Location = new Point(320, 120);
            this.lblWarehouseNameEn.Size = new Size(100, 15);

            this.txtWarehouseNameEn.Font = new Font("Segoe UI", 9F);
            this.txtWarehouseNameEn.Location = new Point(20, 117);
            this.txtWarehouseNameEn.Size = new Size(290, 23);

            // Location
            this.lblLocation.Text = "الموقع:";
            this.lblLocation.Font = new Font("Segoe UI", 9F);
            this.lblLocation.Location = new Point(320, 165);
            this.lblLocation.Size = new Size(50, 15);

            this.txtLocation.Font = new Font("Segoe UI", 9F);
            this.txtLocation.Location = new Point(20, 162);
            this.txtLocation.Size = new Size(290, 23);

            // Description
            this.lblDescription.Text = "الوصف:";
            this.lblDescription.Font = new Font("Segoe UI", 9F);
            this.lblDescription.Location = new Point(320, 210);
            this.lblDescription.Size = new Size(50, 15);

            this.txtDescription.Font = new Font("Segoe UI", 9F);
            this.txtDescription.Location = new Point(20, 207);
            this.txtDescription.Size = new Size(290, 60);
            this.txtDescription.Multiline = true;

            // Products Count
            this.lblProductsCount.Text = "عدد الأصناف: 0";
            this.lblProductsCount.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblProductsCount.ForeColor = Color.FromArgb(39, 174, 96);
            this.lblProductsCount.Location = new Point(20, 287);
            this.lblProductsCount.Size = new Size(150, 15);

            // Add all controls to group box
            this.grpWarehouseDetails.Controls.Add(this.lblWarehouseCode);
            this.grpWarehouseDetails.Controls.Add(this.txtWarehouseCode);
            this.grpWarehouseDetails.Controls.Add(this.lblWarehouseName);
            this.grpWarehouseDetails.Controls.Add(this.txtWarehouseName);
            this.grpWarehouseDetails.Controls.Add(this.lblWarehouseNameEn);
            this.grpWarehouseDetails.Controls.Add(this.txtWarehouseNameEn);
            this.grpWarehouseDetails.Controls.Add(this.lblLocation);
            this.grpWarehouseDetails.Controls.Add(this.txtLocation);
            this.grpWarehouseDetails.Controls.Add(this.lblDescription);
            this.grpWarehouseDetails.Controls.Add(this.txtDescription);
            this.grpWarehouseDetails.Controls.Add(this.lblProductsCount);

            // Buttons Panel
            this.pnlButtons.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlButtons.Controls.Add(this.btnNew);
            this.pnlButtons.Controls.Add(this.btnEdit);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.btnDelete);
            this.pnlButtons.Controls.Add(this.lblStatus);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Size = new Size(1100, 60);

            // Buttons
            this.btnNew.Text = "جديد";
            this.btnNew.BackColor = Color.FromArgb(39, 174, 96);
            this.btnNew.FlatStyle = FlatStyle.Flat;
            this.btnNew.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnNew.ForeColor = Color.White;
            this.btnNew.Location = new Point(980, 15);
            this.btnNew.Size = new Size(100, 35);
            this.btnNew.Click += new EventHandler(this.btnNew_Click);

            this.btnEdit.Text = "تعديل";
            this.btnEdit.BackColor = Color.FromArgb(41, 128, 185);
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.Location = new Point(870, 15);
            this.btnEdit.Size = new Size(100, 35);
            this.btnEdit.Click += new EventHandler(this.btnEdit_Click);

            this.btnSave.Text = "حفظ";
            this.btnSave.BackColor = Color.FromArgb(39, 174, 96);
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(760, 15);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            this.btnCancel.Text = "إلغاء";
            this.btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(650, 15);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.btnDelete.Text = "حذف";
            this.btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.Location = new Point(540, 15);
            this.btnDelete.Size = new Size(100, 35);
            this.btnDelete.Click += new EventHandler(this.btnDelete_Click);

            this.lblStatus.Text = "جاهز...";
            this.lblStatus.Font = new Font("Segoe UI", 9F);
            this.lblStatus.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblStatus.Location = new Point(20, 25);
            this.lblStatus.Size = new Size(200, 15);

            // Form
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.ClientSize = new Size(1100, 650);
            this.Controls.Add(this.pnlDetails);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlStatistics);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.pnlButtons);
            this.Name = "WarehousesForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة المخازن - نظام جود للمحاسبة المالية";
            this.Load += new EventHandler(this.WarehousesForm_Load);

            this.ResumeLayout(false);
        }

        #endregion

        private Panel pnlHeader;
        private Label lblTitle;
        private TextBox txtSearch;
        private Label lblSearch;
        private Button btnRefresh;
        private Panel pnlStatistics;
        private Label lblTotalWarehouses;
        private Label lblWarehousesWithProducts;
        private Label lblTotalProducts;
        private Label lblTotalInvoices;
        private Panel pnlMain;
        private DataGridView dgvWarehouses;
        private Panel pnlDetails;
        private GroupBox grpWarehouseDetails;
        private Label lblWarehouseCode;
        private TextBox txtWarehouseCode;
        private Label lblWarehouseName;
        private TextBox txtWarehouseName;
        private Label lblWarehouseNameEn;
        private TextBox txtWarehouseNameEn;
        private Label lblLocation;
        private TextBox txtLocation;
        private Label lblDescription;
        private TextBox txtDescription;
        private Label lblProductsCount;
        private Panel pnlButtons;
        private Button btnNew;
        private Button btnEdit;
        private Button btnSave;
        private Button btnCancel;
        private Button btnDelete;
        private Label lblStatus;
    }
}
