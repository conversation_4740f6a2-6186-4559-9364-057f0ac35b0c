namespace Joud.Forms
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.menuStrip = new MenuStrip();
            this.mnuFile = new ToolStripMenuItem();
            this.mnuLogout = new ToolStripMenuItem();
            this.mnuExit = new ToolStripMenuItem();
            
            this.mnuCustomers = new ToolStripMenuItem();
            this.mnuSuppliers = new ToolStripMenuItem();
            
            this.mnuInventory = new ToolStripMenuItem();
            this.mnuMainCategories = new ToolStripMenuItem();
            this.mnuSubCategories = new ToolStripMenuItem();
            this.mnuUnits = new ToolStripMenuItem();
            this.mnuWarehouses = new ToolStripMenuItem();
            this.mnuProducts = new ToolStripMenuItem();
            
            this.mnuSales = new ToolStripMenuItem();
            this.mnuSalesInvoices = new ToolStripMenuItem();
            this.mnuSalesReturns = new ToolStripMenuItem();
            
            this.mnuPurchases = new ToolStripMenuItem();
            this.mnuPurchaseInvoices = new ToolStripMenuItem();
            this.mnuPurchaseReturns = new ToolStripMenuItem();
            
            this.mnuAccounting = new ToolStripMenuItem();
            this.mnuExpenses = new ToolStripMenuItem();
            this.mnuRevenues = new ToolStripMenuItem();
            this.mnuAccounts = new ToolStripMenuItem();
            
            this.mnuReports = new ToolStripMenuItem();
            this.mnuSalesReports = new ToolStripMenuItem();
            this.mnuPurchaseReports = new ToolStripMenuItem();
            this.mnuInventoryReports = new ToolStripMenuItem();
            this.mnuFinancialReports = new ToolStripMenuItem();
            
            this.mnuSettings = new ToolStripMenuItem();
            this.mnuUserManagement = new ToolStripMenuItem();
            this.mnuCompanySettings = new ToolStripMenuItem();
            this.mnuSystemSettings = new ToolStripMenuItem();
            this.mnuChangePassword = new ToolStripMenuItem();
            this.mnuBackup = new ToolStripMenuItem();
            
            this.mnuHelp = new ToolStripMenuItem();
            this.mnuAbout = new ToolStripMenuItem();
            
            this.toolStrip = new ToolStrip();
            this.toolStripBtnCustomers = new ToolStripButton();
            this.toolStripBtnSuppliers = new ToolStripButton();
            this.toolStripBtnProducts = new ToolStripButton();
            this.toolStripSeparator1 = new ToolStripSeparator();
            this.toolStripBtnSalesInvoices = new ToolStripButton();
            this.toolStripBtnPurchaseInvoices = new ToolStripButton();
            this.toolStripSeparator2 = new ToolStripSeparator();
            this.toolStripBtnReports = new ToolStripButton();

            this.statusStrip = new StatusStrip();
            this.toolStripStatusUser = new ToolStripStatusLabel();
            this.toolStripStatusRole = new ToolStripStatusLabel();
            this.toolStripStatusCompany = new ToolStripStatusLabel();
            this.toolStripStatusTime = new ToolStripStatusLabel();

            this.SuspendLayout();

            //
            // menuStrip
            //
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                this.mnuFile,
                this.mnuCustomers,
                this.mnuSuppliers,
                this.mnuInventory,
                this.mnuSales,
                this.mnuPurchases,
                this.mnuAccounting,
                this.mnuReports,
                this.mnuSettings,
                this.mnuHelp
            });
            this.menuStrip.Location = new Point(0, 0);
            this.menuStrip.Name = "menuStrip";
            this.menuStrip.Size = new Size(1200, 28);
            this.menuStrip.TabIndex = 0;
            this.menuStrip.Text = "menuStrip";
            this.menuStrip.RightToLeft = RightToLeft.Yes;
            this.menuStrip.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
            this.menuStrip.BackColor = Color.FromArgb(52, 73, 94);
            this.menuStrip.ForeColor = Color.White;

            //
            // toolStrip
            //
            this.toolStrip.Items.AddRange(new ToolStripItem[] {
                this.toolStripBtnCustomers,
                this.toolStripBtnSuppliers,
                this.toolStripBtnProducts,
                this.toolStripSeparator1,
                this.toolStripBtnSalesInvoices,
                this.toolStripBtnPurchaseInvoices,
                this.toolStripSeparator2,
                this.toolStripBtnReports
            });
            this.toolStrip.Location = new Point(0, 28);
            this.toolStrip.Name = "toolStrip";
            this.toolStrip.Size = new Size(1200, 32);
            this.toolStrip.TabIndex = 2;
            this.toolStrip.Text = "toolStrip";
            this.toolStrip.RightToLeft = RightToLeft.Yes;
            this.toolStrip.BackColor = Color.FromArgb(236, 240, 241);
            this.toolStrip.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // أزرار شريط الأدوات
            this.toolStripBtnCustomers.Text = "العملاء";
            this.toolStripBtnCustomers.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            this.toolStripBtnCustomers.TextImageRelation = TextImageRelation.ImageAboveText;
            this.toolStripBtnCustomers.Click += new EventHandler(this.mnuCustomers_Click);

            this.toolStripBtnSuppliers.Text = "الموردين";
            this.toolStripBtnSuppliers.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            this.toolStripBtnSuppliers.TextImageRelation = TextImageRelation.ImageAboveText;
            this.toolStripBtnSuppliers.Click += new EventHandler(this.mnuSuppliers_Click);

            this.toolStripBtnProducts.Text = "الأصناف";
            this.toolStripBtnProducts.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            this.toolStripBtnProducts.TextImageRelation = TextImageRelation.ImageAboveText;
            this.toolStripBtnProducts.Click += new EventHandler(this.mnuProducts_Click);

            this.toolStripBtnSalesInvoices.Text = "فواتير المبيعات";
            this.toolStripBtnSalesInvoices.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            this.toolStripBtnSalesInvoices.TextImageRelation = TextImageRelation.ImageAboveText;
            this.toolStripBtnSalesInvoices.Click += new EventHandler(this.mnuSalesInvoices_Click);

            this.toolStripBtnPurchaseInvoices.Text = "فواتير المشتريات";
            this.toolStripBtnPurchaseInvoices.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            this.toolStripBtnPurchaseInvoices.TextImageRelation = TextImageRelation.ImageAboveText;
            this.toolStripBtnPurchaseInvoices.Click += new EventHandler(this.mnuPurchaseInvoices_Click);

            this.toolStripBtnReports.Text = "التقارير";
            this.toolStripBtnReports.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            this.toolStripBtnReports.TextImageRelation = TextImageRelation.ImageAboveText;
            this.toolStripBtnReports.Click += new EventHandler(this.mnuReports_Click);

            //
            // mnuFile
            //
            this.mnuFile.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuLogout,
                new ToolStripSeparator(),
                this.mnuExit
            });
            this.mnuFile.Name = "mnuFile";
            this.mnuFile.Size = new Size(50, 20);
            this.mnuFile.Text = "ملف";

            // 
            // mnuLogout
            // 
            this.mnuLogout.Name = "mnuLogout";
            this.mnuLogout.Size = new Size(150, 22);
            this.mnuLogout.Text = "تسجيل الخروج";
            this.mnuLogout.Click += new EventHandler(this.mnuLogout_Click);

            // 
            // mnuExit
            // 
            this.mnuExit.Name = "mnuExit";
            this.mnuExit.Size = new Size(150, 22);
            this.mnuExit.Text = "إغلاق";
            this.mnuExit.Click += new EventHandler(this.mnuExit_Click);

            // 
            // mnuCustomers
            // 
            this.mnuCustomers.Name = "mnuCustomers";
            this.mnuCustomers.Size = new Size(60, 20);
            this.mnuCustomers.Text = "العملاء";
            this.mnuCustomers.ShortcutKeys = Keys.Control | Keys.C;
            this.mnuCustomers.Click += new EventHandler(this.mnuCustomers_Click);

            //
            // mnuSuppliers
            //
            this.mnuSuppliers.Name = "mnuSuppliers";
            this.mnuSuppliers.Size = new Size(60, 20);
            this.mnuSuppliers.Text = "الموردين";
            this.mnuSuppliers.ShortcutKeys = Keys.Control | Keys.S;
            this.mnuSuppliers.Click += new EventHandler(this.mnuSuppliers_Click);

            // 
            // mnuInventory
            // 
            this.mnuInventory.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuMainCategories,
                this.mnuSubCategories,
                new ToolStripSeparator(),
                this.mnuUnits,
                this.mnuWarehouses,
                new ToolStripSeparator(),
                this.mnuProducts
            });
            this.mnuInventory.Name = "mnuInventory";
            this.mnuInventory.Size = new Size(80, 20);
            this.mnuInventory.Text = "الأصناف والمخازن";

            // 
            // mnuMainCategories
            // 
            this.mnuMainCategories.Name = "mnuMainCategories";
            this.mnuMainCategories.Size = new Size(150, 22);
            this.mnuMainCategories.Text = "الفئات الرئيسية";
            this.mnuMainCategories.Click += new EventHandler(this.mnuMainCategories_Click);

            // 
            // mnuSubCategories
            // 
            this.mnuSubCategories.Name = "mnuSubCategories";
            this.mnuSubCategories.Size = new Size(150, 22);
            this.mnuSubCategories.Text = "الفئات الفرعية";
            this.mnuSubCategories.Click += new EventHandler(this.mnuSubCategories_Click);

            // 
            // mnuUnits
            // 
            this.mnuUnits.Name = "mnuUnits";
            this.mnuUnits.Size = new Size(150, 22);
            this.mnuUnits.Text = "الوحدات";
            this.mnuUnits.Click += new EventHandler(this.mnuUnits_Click);

            // 
            // mnuWarehouses
            // 
            this.mnuWarehouses.Name = "mnuWarehouses";
            this.mnuWarehouses.Size = new Size(150, 22);
            this.mnuWarehouses.Text = "المخازن";
            this.mnuWarehouses.Click += new EventHandler(this.mnuWarehouses_Click);

            // 
            // mnuProducts
            // 
            this.mnuProducts.Name = "mnuProducts";
            this.mnuProducts.Size = new Size(150, 22);
            this.mnuProducts.Text = "الأصناف";
            this.mnuProducts.ShortcutKeys = Keys.Control | Keys.P;
            this.mnuProducts.Click += new EventHandler(this.mnuProducts_Click);

            // 
            // mnuSales
            // 
            this.mnuSales.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuSalesInvoices,
                this.mnuSalesReturns
            });
            this.mnuSales.Name = "mnuSales";
            this.mnuSales.Size = new Size(60, 20);
            this.mnuSales.Text = "المبيعات";

            // 
            // mnuSalesInvoices
            // 
            this.mnuSalesInvoices.Name = "mnuSalesInvoices";
            this.mnuSalesInvoices.Size = new Size(150, 22);
            this.mnuSalesInvoices.Text = "فواتير المبيعات";
            this.mnuSalesInvoices.Click += new EventHandler(this.mnuSalesInvoices_Click);

            //
            // mnuSalesReturns
            //
            this.mnuSalesReturns.Name = "mnuSalesReturns";
            this.mnuSalesReturns.Size = new Size(150, 22);
            this.mnuSalesReturns.Text = "مردودات المبيعات";
            this.mnuSalesReturns.Click += new EventHandler(this.mnuSalesReturns_Click);

            // 
            // mnuPurchases
            // 
            this.mnuPurchases.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuPurchaseInvoices,
                this.mnuPurchaseReturns
            });
            this.mnuPurchases.Name = "mnuPurchases";
            this.mnuPurchases.Size = new Size(70, 20);
            this.mnuPurchases.Text = "المشتريات";

            // 
            // mnuPurchaseInvoices
            // 
            this.mnuPurchaseInvoices.Name = "mnuPurchaseInvoices";
            this.mnuPurchaseInvoices.Size = new Size(150, 22);
            this.mnuPurchaseInvoices.Text = "فواتير المشتريات";
            this.mnuPurchaseInvoices.Click += new EventHandler(this.mnuPurchaseInvoices_Click);

            //
            // mnuPurchaseReturns
            //
            this.mnuPurchaseReturns.Name = "mnuPurchaseReturns";
            this.mnuPurchaseReturns.Size = new Size(150, 22);
            this.mnuPurchaseReturns.Text = "مردودات المشتريات";
            this.mnuPurchaseReturns.Click += new EventHandler(this.mnuPurchaseReturns_Click);

            // 
            // mnuAccounting
            // 
            this.mnuAccounting.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuExpenses,
                this.mnuRevenues,
                new ToolStripSeparator(),
                this.mnuAccounts
            });
            this.mnuAccounting.Name = "mnuAccounting";
            this.mnuAccounting.Size = new Size(60, 20);
            this.mnuAccounting.Text = "المحاسبة";

            //
            // mnuExpenses
            //
            this.mnuExpenses.Name = "mnuExpenses";
            this.mnuExpenses.Size = new Size(150, 22);
            this.mnuExpenses.Text = "المصاريف";
            this.mnuExpenses.Click += new EventHandler(this.mnuExpenses_Click);

            //
            // mnuRevenues
            //
            this.mnuRevenues.Name = "mnuRevenues";
            this.mnuRevenues.Size = new Size(150, 22);
            this.mnuRevenues.Text = "الإيرادات";
            this.mnuRevenues.Click += new EventHandler(this.mnuRevenues_Click);

            //
            // mnuAccounts
            //
            this.mnuAccounts.Name = "mnuAccounts";
            this.mnuAccounts.Size = new Size(150, 22);
            this.mnuAccounts.Text = "الحسابات";
            this.mnuAccounts.Click += new EventHandler(this.mnuAccounts_Click);

            // 
            // mnuReports
            // 
            this.mnuReports.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuSalesReports,
                this.mnuPurchaseReports,
                this.mnuInventoryReports,
                this.mnuFinancialReports
            });
            this.mnuReports.Name = "mnuReports";
            this.mnuReports.Size = new Size(60, 20);
            this.mnuReports.Text = "التقارير";

            //
            // mnuSalesReports
            //
            this.mnuSalesReports.Name = "mnuSalesReports";
            this.mnuSalesReports.Size = new Size(150, 22);
            this.mnuSalesReports.Text = "تقارير المبيعات";
            this.mnuSalesReports.Click += new EventHandler(this.mnuSalesReports_Click);

            //
            // mnuPurchaseReports
            //
            this.mnuPurchaseReports.Name = "mnuPurchaseReports";
            this.mnuPurchaseReports.Size = new Size(150, 22);
            this.mnuPurchaseReports.Text = "تقارير المشتريات";
            this.mnuPurchaseReports.Click += new EventHandler(this.mnuPurchaseReports_Click);

            //
            // mnuInventoryReports
            //
            this.mnuInventoryReports.Name = "mnuInventoryReports";
            this.mnuInventoryReports.Size = new Size(150, 22);
            this.mnuInventoryReports.Text = "تقارير المخزون";
            this.mnuInventoryReports.Click += new EventHandler(this.mnuInventoryReports_Click);

            //
            // mnuFinancialReports
            //
            this.mnuFinancialReports.Name = "mnuFinancialReports";
            this.mnuFinancialReports.Size = new Size(150, 22);
            this.mnuFinancialReports.Text = "التقارير المالية";
            this.mnuFinancialReports.Click += new EventHandler(this.mnuFinancialReports_Click);

            // 
            // mnuSettings
            // 
            this.mnuSettings.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuUserManagement,
                this.mnuCompanySettings,
                this.mnuSystemSettings,
                new ToolStripSeparator(),
                this.mnuChangePassword,
                new ToolStripSeparator(),
                this.mnuBackup
            });
            this.mnuSettings.Name = "mnuSettings";
            this.mnuSettings.Size = new Size(60, 20);
            this.mnuSettings.Text = "الإعدادات";

            // 
            // mnuUserManagement
            // 
            this.mnuUserManagement.Name = "mnuUserManagement";
            this.mnuUserManagement.Size = new Size(150, 22);
            this.mnuUserManagement.Text = "إدارة المستخدمين";
            this.mnuUserManagement.Click += new EventHandler(this.mnuUserManagement_Click);

            // 
            // mnuCompanySettings
            // 
            this.mnuCompanySettings.Name = "mnuCompanySettings";
            this.mnuCompanySettings.Size = new Size(150, 22);
            this.mnuCompanySettings.Text = "إعدادات الشركة";
            this.mnuCompanySettings.Click += new EventHandler(this.mnuCompanySettings_Click);

            //
            // mnuSystemSettings
            //
            this.mnuSystemSettings.Name = "mnuSystemSettings";
            this.mnuSystemSettings.Size = new Size(150, 22);
            this.mnuSystemSettings.Text = "إعدادات النظام";
            this.mnuSystemSettings.Click += new EventHandler(this.mnuSystemSettings_Click);

            // 
            // mnuChangePassword
            // 
            this.mnuChangePassword.Name = "mnuChangePassword";
            this.mnuChangePassword.Size = new Size(150, 22);
            this.mnuChangePassword.Text = "تغيير كلمة المرور";
            this.mnuChangePassword.Click += new EventHandler(this.mnuChangePassword_Click);

            // 
            // mnuBackup
            // 
            this.mnuBackup.Name = "mnuBackup";
            this.mnuBackup.Size = new Size(150, 22);
            this.mnuBackup.Text = "النسخ الاحتياطي";
            this.mnuBackup.Click += new EventHandler(this.mnuBackup_Click);

            // 
            // mnuHelp
            // 
            this.mnuHelp.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuAbout
            });
            this.mnuHelp.Name = "mnuHelp";
            this.mnuHelp.Size = new Size(50, 20);
            this.mnuHelp.Text = "مساعدة";

            // 
            // mnuAbout
            // 
            this.mnuAbout.Name = "mnuAbout";
            this.mnuAbout.Size = new Size(150, 22);
            this.mnuAbout.Text = "حول البرنامج";
            this.mnuAbout.Click += new EventHandler(this.mnuAbout_Click);

            // 
            // statusStrip
            // 
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.toolStripStatusUser,
                this.toolStripStatusRole,
                this.toolStripStatusCompany,
                this.toolStripStatusTime
            });
            this.statusStrip.Location = new Point(0, 739);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new Size(1200, 22);
            this.statusStrip.TabIndex = 1;
            this.statusStrip.Text = "statusStrip";
            this.statusStrip.RightToLeft = RightToLeft.Yes;

            // 
            // toolStripStatusUser
            // 
            this.toolStripStatusUser.Name = "toolStripStatusUser";
            this.toolStripStatusUser.Size = new Size(100, 17);
            this.toolStripStatusUser.Text = "المستخدم: ";

            // 
            // toolStripStatusRole
            // 
            this.toolStripStatusRole.Name = "toolStripStatusRole";
            this.toolStripStatusRole.Size = new Size(100, 17);
            this.toolStripStatusRole.Text = "الدور: ";

            // 
            // toolStripStatusCompany
            // 
            this.toolStripStatusCompany.Name = "toolStripStatusCompany";
            this.toolStripStatusCompany.Size = new Size(100, 17);
            this.toolStripStatusCompany.Text = "الشركة: ";

            // 
            // toolStripStatusTime
            // 
            this.toolStripStatusTime.Name = "toolStripStatusTime";
            this.toolStripStatusTime.Size = new Size(100, 17);
            this.toolStripStatusTime.Text = "الوقت: ";
            this.toolStripStatusTime.Spring = true;
            this.toolStripStatusTime.TextAlign = ContentAlignment.MiddleLeft;

            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 761);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.toolStrip);
            this.Controls.Add(this.menuStrip);
            this.IsMdiContainer = true;
            this.MainMenuStrip = this.menuStrip;
            this.Name = "MainForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "نظام جود للمحاسبة المالية";
            this.WindowState = FormWindowState.Maximized;
            this.FormClosing += new FormClosingEventHandler(this.MainForm_FormClosing);
            this.Load += new EventHandler(this.MainForm_Load);
            this.menuStrip.ResumeLayout(false);
            this.menuStrip.PerformLayout();
            this.toolStrip.ResumeLayout(false);
            this.toolStrip.PerformLayout();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private MenuStrip menuStrip;
        private ToolStripMenuItem mnuFile;
        private ToolStripMenuItem mnuLogout;
        private ToolStripMenuItem mnuExit;
        private ToolStripMenuItem mnuCustomers;
        private ToolStripMenuItem mnuSuppliers;
        private ToolStripMenuItem mnuInventory;
        private ToolStripMenuItem mnuMainCategories;
        private ToolStripMenuItem mnuSubCategories;
        private ToolStripMenuItem mnuUnits;
        private ToolStripMenuItem mnuWarehouses;
        private ToolStripMenuItem mnuProducts;
        private ToolStripMenuItem mnuSales;
        private ToolStripMenuItem mnuSalesInvoices;
        private ToolStripMenuItem mnuSalesReturns;
        private ToolStripMenuItem mnuPurchases;
        private ToolStripMenuItem mnuPurchaseInvoices;
        private ToolStripMenuItem mnuPurchaseReturns;
        private ToolStripMenuItem mnuAccounting;
        private ToolStripMenuItem mnuExpenses;
        private ToolStripMenuItem mnuRevenues;
        private ToolStripMenuItem mnuAccounts;
        private ToolStripMenuItem mnuReports;
        private ToolStripMenuItem mnuSalesReports;
        private ToolStripMenuItem mnuPurchaseReports;
        private ToolStripMenuItem mnuInventoryReports;
        private ToolStripMenuItem mnuFinancialReports;
        private ToolStripMenuItem mnuSettings;
        private ToolStripMenuItem mnuUserManagement;
        private ToolStripMenuItem mnuCompanySettings;
        private ToolStripMenuItem mnuSystemSettings;
        private ToolStripMenuItem mnuChangePassword;
        private ToolStripMenuItem mnuBackup;
        private ToolStripMenuItem mnuHelp;
        private ToolStripMenuItem mnuAbout;
        private ToolStrip toolStrip;
        private ToolStripButton toolStripBtnCustomers;
        private ToolStripButton toolStripBtnSuppliers;
        private ToolStripButton toolStripBtnProducts;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripButton toolStripBtnSalesInvoices;
        private ToolStripButton toolStripBtnPurchaseInvoices;
        private ToolStripSeparator toolStripSeparator2;
        private ToolStripButton toolStripBtnReports;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel toolStripStatusUser;
        private ToolStripStatusLabel toolStripStatusRole;
        private ToolStripStatusLabel toolStripStatusCompany;
        private ToolStripStatusLabel toolStripStatusTime;
    }
}
