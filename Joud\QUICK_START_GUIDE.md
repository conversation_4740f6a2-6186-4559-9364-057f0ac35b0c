# دليل التشغيل السريع
## Quick Start Guide - نظام جود للمحاسبة المالية

---

## 🚀 **البدء السريع**

### **المتطلبات الأساسية:**
- Windows 10/11
- .NET Framework 4.8+
- SQL Server 2019+ أو SQL Server Express
- Visual Studio 2022 (للتطوير)

---

## 📥 **التثبيت والإعداد**

### **1. تحميل المشروع**
```bash
git clone [repository-url]
cd Joud
```

### **2. فتح المشروع**
- افتح ملف `Joud.sln` في Visual Studio
- تأكد من استعادة جميع الحزم (NuGet Packages)

### **3. إعداد قاعدة البيانات**
- تأكد من تشغيل SQL Server
- عند التشغيل الأول، ستظهر شاشة الإعداد الأولي
- أدخل معلومات الاتصال بقاعدة البيانات

### **4. تشغيل النظام**
- اضغط F5 أو "Start Debugging"
- اتبع خطوات الإعداد الأولي

---

## 🏢 **الإعداد الأولي**

### **خطوات الإعداد:**

#### **1. معلومات قاعدة البيانات**
- **اسم الخادم**: `.\SQLEXPRESS` (للـ SQL Server Express)
- **اسم قاعدة البيانات**: `JoudAccountingDB` (أو أي اسم تختاره)
- **نوع المصادقة**: Windows Authentication (موصى به)

#### **2. معلومات الشركة**
- اسم الشركة
- العنوان
- الهاتف والفاكس
- البريد الإلكتروني
- العملة الافتراضية

#### **3. المستخدم الرئيسي**
- اسم المستخدم
- كلمة المرور (يجب أن تكون قوية)
- الاسم الكامل
- البريد الإلكتروني

---

## 🎯 **الاستخدام الأساسي**

### **1. تسجيل الدخول**
- استخدم بيانات المستخدم الرئيسي المُنشأ في الإعداد
- النظام يدعم عدة مستخدمين بصلاحيات مختلفة

### **2. إعداد البيانات الأساسية**

#### **أ. إنشاء دليل الحسابات**
1. اذهب إلى **المحاسبة** → **إدارة الحسابات**
2. اضغط **"إنشاء دليل افتراضي"**
3. سيتم إنشاء دليل الحسابات الأساسي تلقائياً

#### **ب. إضافة الفئات والوحدات**
1. **الفئات الرئيسية**: اذهب إلى **الأصناف** → **الفئات الرئيسية**
2. **الفئات الفرعية**: اذهب إلى **الأصناف** → **الفئات الفرعية**
3. **الوحدات**: اذهب إلى **الأصناف** → **الوحدات**

#### **ج. إضافة المخازن**
1. اذهب إلى **المخازن** → **إدارة المخازن**
2. أضف مخزن رئيسي واحد على الأقل

#### **د. إضافة العملاء والموردين**
1. **العملاء**: اذهب إلى **العملاء** → **إدارة العملاء**
2. **الموردين**: اذهب إلى **الموردين** → **إدارة الموردين**

#### **هـ. إضافة الأصناف**
1. اذهب إلى **الأصناف** → **إدارة الأصناف**
2. أضف الأصناف مع أسعار الشراء والبيع

### **3. العمليات اليومية**

#### **أ. فواتير المشتريات**
1. اذهب إلى **الفواتير** → **فواتير المشتريات**
2. اضغط **"جديد"**
3. اختر المورد والمخزن
4. أضف بنود الفاتورة
5. احفظ الفاتورة

#### **ب. فواتير المبيعات**
1. اذهب إلى **الفواتير** → **فواتير المبيعات**
2. اضغط **"جديد"**
3. اختر العميل والمخزن
4. أضف بنود الفاتورة
5. احفظ الفاتورة

### **4. التقارير**
1. اذهب إلى **التقارير**
2. اختر نوع التقرير المطلوب:
   - تقرير المبيعات
   - تقرير المشتريات
   - أرصدة العملاء
   - أرصدة الموردين
   - تقرير المخزون
   - حركة المخزون

### **5. الطباعة**
- يمكن طباعة جميع الفواتير والتقارير
- استخدم زر **"طباعة"** في كل شاشة
- يتوفر معاينة قبل الطباعة

---

## 💡 **نصائح مهمة**

### **الأمان:**
- استخدم كلمات مرور قوية
- قم بعمل نسخ احتياطية دورية
- لا تشارك بيانات تسجيل الدخول

### **الأداء:**
- أغلق الشاشات غير المستخدمة
- قم بتحديث البيانات دورياً
- استخدم البحث للعثور على البيانات بسرعة

### **إدارة البيانات:**
- تأكد من صحة البيانات قبل الحفظ
- استخدم الأكواد المنطقية للأصناف والعملاء
- راجع التقارير دورياً للتأكد من دقة البيانات

---

## 🔧 **حل المشاكل الشائعة**

### **مشكلة الاتصال بقاعدة البيانات:**
- تأكد من تشغيل SQL Server
- تحقق من صحة اسم الخادم
- تأكد من صلاحيات الوصول

### **مشكلة في تسجيل الدخول:**
- تأكد من صحة اسم المستخدم وكلمة المرور
- تحقق من حالة الأحرف (Case Sensitive)

### **بطء في الأداء:**
- أغلق الشاشات غير المستخدمة
- قم بإعادة تشغيل التطبيق
- تحقق من مساحة القرص الصلب

### **مشاكل في الطباعة:**
- تأكد من تثبيت الطابعة بشكل صحيح
- تحقق من إعدادات الطابعة
- جرب طابعة أخرى

---

## 📞 **الدعم الفني**

### **للحصول على المساعدة:**
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **الموقع**: www.joudsystems.com

### **الوثائق الإضافية:**
- `README.md` - دليل المشروع الكامل
- `PROJECT_COMPLETION_REPORT.md` - تقرير إكمال المشروع
- `PHASE1_PROGRESS.md` - تقرير المرحلة الأولى
- `PHASE2_PROGRESS.md` - تقرير المرحلة الثانية
- `PHASE3_PROGRESS.md` - تقرير المرحلة الثالثة

---

## ✅ **قائمة التحقق السريع**

### **قبل البدء:**
- [ ] تثبيت SQL Server
- [ ] تثبيت .NET Framework
- [ ] تحميل المشروع
- [ ] فتح المشروع في Visual Studio

### **الإعداد الأولي:**
- [ ] إعداد قاعدة البيانات
- [ ] إدخال معلومات الشركة
- [ ] إنشاء المستخدم الرئيسي
- [ ] إنشاء دليل الحسابات

### **البيانات الأساسية:**
- [ ] إضافة الفئات والوحدات
- [ ] إضافة المخازن
- [ ] إضافة العملاء والموردين
- [ ] إضافة الأصناف

### **اختبار النظام:**
- [ ] إنشاء فاتورة مشتريات
- [ ] إنشاء فاتورة مبيعات
- [ ] طباعة فاتورة
- [ ] إنشاء تقرير

---

**🎉 مبروك! النظام جاهز للاستخدام!**

**تاريخ آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0  
**الحالة**: مكتمل ✅
