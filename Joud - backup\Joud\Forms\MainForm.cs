using Joud.Models;
using System.Configuration;

namespace Joud.Forms
{
    /// <summary>
    /// الشاشة الرئيسية للتطبيق
    /// Main Application Form
    /// </summary>
    public partial class MainForm : Form
    {
        private User _currentUser;
        private Company _currentCompany;

        public MainForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            
            InitializeFormSettings();
            SetupUserInterface();
            UpdateStatusBar();
        }

        private void InitializeFormSettings()
        {
            // إعدادات النموذج الأساسية
            this.Text = $"نظام جود للمحاسبة المالية - {_currentCompany.CompanyName}";
            this.WindowState = FormWindowState.Maximized;
            this.IsMdiContainer = true;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // تطبيق الألوان والتصميم
            this.BackColor = Color.FromArgb(236, 240, 241);
        }

        private void SetupUserInterface()
        {
            // إعداد الواجهة حسب صلاحيات المستخدم
            SetMenuPermissions();
            
            // إعداد شريط الأدوات
            SetupToolbar();
        }

        private void SetMenuPermissions()
        {
            // إعداد صلاحيات القوائم حسب دور المستخدم
            string userRole = _currentUser.UserRole.RoleName;

            switch (userRole)
            {
                case "مدير النظام":
                    // صلاحيات كاملة
                    break;
                    
                case "مدير مالي":
                    // صلاحيات مالية ومحاسبية
                    mnuSystemSettings.Enabled = false;
                    mnuUserManagement.Enabled = false;
                    break;
                    
                case "محاسب":
                    // صلاحيات محاسبية فقط
                    mnuSystemSettings.Enabled = false;
                    mnuUserManagement.Enabled = false;
                    mnuReports.Enabled = true;
                    break;
                    
                case "موظف مبيعات":
                    // صلاحيات المبيعات والعملاء فقط
                    mnuPurchases.Enabled = false;
                    mnuSuppliers.Enabled = false;
                    mnuSystemSettings.Enabled = false;
                    mnuUserManagement.Enabled = false;
                    mnuAccounting.Enabled = false;
                    break;
                    
                case "موظف مشتريات":
                    // صلاحيات المشتريات والموردين فقط
                    mnuSales.Enabled = false;
                    mnuCustomers.Enabled = false;
                    mnuSystemSettings.Enabled = false;
                    mnuUserManagement.Enabled = false;
                    mnuAccounting.Enabled = false;
                    break;
                    
                case "موظف مخازن":
                    // صلاحيات المخازن والأصناف فقط
                    mnuSales.Enabled = false;
                    mnuPurchases.Enabled = false;
                    mnuCustomers.Enabled = false;
                    mnuSuppliers.Enabled = false;
                    mnuSystemSettings.Enabled = false;
                    mnuUserManagement.Enabled = false;
                    mnuAccounting.Enabled = false;
                    break;
                    
                default:
                    // صلاحيات محدودة للأدوار غير المعرفة
                    mnuSystemSettings.Enabled = false;
                    mnuUserManagement.Enabled = false;
                    mnuAccounting.Enabled = false;
                    break;
            }
        }

        private void SetupToolbar()
        {
            // إعداد شريط الأدوات السريع
            toolStripStatusUser.Text = $"المستخدم: {_currentUser.FullName}";
            toolStripStatusRole.Text = $"الدور: {_currentUser.UserRole.RoleName}";
            toolStripStatusCompany.Text = $"الشركة: {_currentCompany.CompanyName}";
            toolStripStatusTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
        }

        private void UpdateStatusBar()
        {
            // تحديث شريط الحالة
            System.Windows.Forms.Timer statusTimer = new System.Windows.Forms.Timer();
            statusTimer.Interval = 1000; // تحديث كل ثانية
            statusTimer.Tick += (s, e) => {
                toolStripStatusTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            };
            statusTimer.Start();
        }

        // معالجات أحداث القوائم
        private void mnuCustomers_Click(object sender, EventArgs e)
        {
            OpenChildForm(new CustomersForm(_currentUser, _currentCompany));
        }

        private void mnuSuppliers_Click(object sender, EventArgs e)
        {
            OpenChildForm(new SuppliersForm(_currentUser, _currentCompany));
        }

        private void mnuProducts_Click(object sender, EventArgs e)
        {
            OpenChildForm(new ProductsForm(_currentUser, _currentCompany));
        }

        private void mnuMainCategories_Click(object sender, EventArgs e)
        {
            OpenChildForm(new MainCategoriesForm(_currentUser, _currentCompany));
        }

        private void mnuSubCategories_Click(object sender, EventArgs e)
        {
            OpenChildForm(new SubCategoriesForm(_currentUser, _currentCompany));
        }

        private void mnuUnits_Click(object sender, EventArgs e)
        {
            OpenChildForm(new UnitsForm(_currentUser, _currentCompany));
        }

        private void mnuWarehouses_Click(object sender, EventArgs e)
        {
            OpenChildForm(new WarehousesForm(_currentUser, _currentCompany));
        }



        private void mnuSalesInvoices_Click(object sender, EventArgs e)
        {
            OpenChildForm(new SalesInvoicesForm(_currentUser, _currentCompany));
        }

        private void mnuPurchaseInvoices_Click(object sender, EventArgs e)
        {
            OpenChildForm(new PurchaseInvoicesForm(_currentUser, _currentCompany));
        }

        private void mnuReports_Click(object sender, EventArgs e)
        {
            OpenChildForm(new ReportsForm(_currentUser, _currentCompany));
        }

        private void mnuAccounts_Click(object sender, EventArgs e)
        {
            OpenChildForm(new AccountsForm(_currentUser, _currentCompany));
        }

        private void mnuUserManagement_Click(object sender, EventArgs e)
        {
            // مؤقتاً حتى يتم إنشاء النموذج
            MessageBox.Show("شاشة إدارة المستخدمين قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void mnuCompanySettings_Click(object sender, EventArgs e)
        {
            // مؤقتاً حتى يتم إنشاء النموذج
            MessageBox.Show("شاشة إعدادات الشركة قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void mnuBackup_Click(object sender, EventArgs e)
        {
            // مؤقتاً حتى يتم إنشاء النموذج
            MessageBox.Show("شاشة النسخ الاحتياطي قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void mnuChangePassword_Click(object sender, EventArgs e)
        {
            // مؤقتاً حتى يتم إنشاء النموذج
            MessageBox.Show("شاشة تغيير كلمة المرور قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void mnuLogout_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(
                "هل تريد تسجيل الخروج من النظام؟",
                "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // إغلاق جميع النوافذ الفرعية
                foreach (Form childForm in this.MdiChildren)
                {
                    childForm.Close();
                }

                // إخفاء النموذج الحالي
                this.Hide();

                // إظهار شاشة تسجيل الدخول
                using (var loginForm = new LoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        // تحديث بيانات المستخدم الجديد
                        _currentUser = loginForm.LoggedInUser!;
                        _currentCompany = loginForm.UserCompany!;
                        
                        // إعادة إعداد الواجهة
                        SetupUserInterface();
                        UpdateStatusBar();
                        
                        // إظهار النموذج مرة أخرى
                        this.Show();
                    }
                    else
                    {
                        // إغلاق التطبيق إذا تم إلغاء تسجيل الدخول
                        Application.Exit();
                    }
                }
            }
        }

        private void mnuExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(
                "هل تريد إغلاق التطبيق؟",
                "تأكيد الإغلاق",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void mnuAbout_Click(object sender, EventArgs e)
        {
            MessageBox.Show(
                $"نظام جود للمحاسبة المالية\nالإصدار 1.0.0\n\n" +
                $"© 2024 Joud Systems\nجميع الحقوق محفوظة\n\n" +
                $"تم التطوير باستخدام:\n" +
                $"- C# .NET 8.0\n" +
                $"- Windows Forms\n" +
                $"- SQL Server\n" +
                $"- Entity Framework Core",
                "حول البرنامج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }

        private void OpenChildForm(Form childForm)
        {
            // التحقق من وجود النموذج مفتوح مسبقاً
            foreach (Form openForm in this.MdiChildren)
            {
                if (openForm.GetType() == childForm.GetType())
                {
                    openForm.Activate();
                    childForm.Dispose();
                    return;
                }
            }

            // فتح النموذج الجديد
            childForm.MdiParent = this;
            childForm.Show();
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                mnuExit_Click(sender, e);
            }
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // رسالة ترحيب
            MessageBox.Show(
                $"مرحباً {_currentUser.FullName}\nتم تسجيل الدخول بنجاح إلى نظام جود للمحاسبة المالية",
                "مرحباً",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }
    }
}
