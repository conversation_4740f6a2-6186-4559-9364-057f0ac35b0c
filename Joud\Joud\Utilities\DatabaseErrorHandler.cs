using System;
using System.Collections.Generic;
using System.Linq;

namespace Joud.Utilities
{
    /// <summary>
    /// معالج أخطاء قاعدة البيانات
    /// Database Error Handler
    /// </summary>
    public static class DatabaseErrorHandler
    {
        /// <summary>
        /// التحقق من وجود خطأ عمود مفقود
        /// Check if error is related to missing column
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <returns>true إذا كان الخطأ متعلق بعمود مفقود</returns>
        public static bool IsMissingColumnError(Exception exception)
        {
            if (exception?.InnerException?.Message == null)
                return false;

            return exception.InnerException.Message.Contains("Invalid column name", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// الحصول على اسم العمود المفقود
        /// Get missing column name
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <returns>اسم العمود المفقود</returns>
        public static string GetMissingColumnName(Exception exception)
        {
            if (!IsMissingColumnError(exception))
                return string.Empty;

            var message = exception.InnerException!.Message;
            
            // استخراج اسم العمود من رسالة الخطأ
            var startIndex = message.IndexOf("'", StringComparison.OrdinalIgnoreCase);
            if (startIndex == -1) return string.Empty;

            var endIndex = message.IndexOf("'", startIndex + 1, StringComparison.OrdinalIgnoreCase);
            if (endIndex == -1) return string.Empty;

            return message.Substring(startIndex + 1, endIndex - startIndex - 1);
        }

        /// <summary>
        /// إنشاء رسالة خطأ مفصلة للأعمدة المفقودة
        /// Create detailed error message for missing columns
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <param name="operationName">اسم العملية</param>
        /// <returns>رسالة خطأ مفصلة</returns>
        public static string CreateDetailedErrorMessage(Exception exception, string operationName)
        {
            var errorMessage = $"خطأ في {operationName}: {exception.Message}";

            if (exception.InnerException != null)
            {
                errorMessage += $"\n\nتفاصيل إضافية: {exception.InnerException.Message}";

                if (IsMissingColumnError(exception))
                {
                    var columnName = GetMissingColumnName(exception);
                    var tableName = GetTableNameFromError(exception);

                    errorMessage += "\n\n🔧 مشكلة في قاعدة البيانات:";
                    errorMessage += $"\nالعمود المفقود: {columnName}";
                    
                    if (!string.IsNullOrEmpty(tableName))
                        errorMessage += $"\nالجدول: {tableName}";

                    errorMessage += "\n\n💡 الحل المقترح:";
                    errorMessage += "\n1. تأكد من تنفيذ ملف AddMissingColumns.sql";
                    errorMessage += "\n2. أعد تشغيل الإعداد الأولي للنظام";
                    errorMessage += "\n3. تحقق من أن قاعدة البيانات محدثة";

                    errorMessage += GetColumnSuggestion(columnName);
                }
            }

            return errorMessage;
        }

        /// <summary>
        /// الحصول على اسم الجدول من رسالة الخطأ
        /// Get table name from error message
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <returns>اسم الجدول</returns>
        private static string GetTableNameFromError(Exception exception)
        {
            var message = exception.InnerException?.Message ?? string.Empty;
            
            // قائمة الجداول المحتملة
            var tables = new[] { "Companies", "Users", "Customers", "Suppliers", "Products", 
                               "MainCategories", "SubCategories", "Units", "Warehouses",
                               "SalesInvoices", "PurchaseInvoices" };

            return tables.FirstOrDefault(table => 
                message.Contains(table, StringComparison.OrdinalIgnoreCase)) ?? string.Empty;
        }

        /// <summary>
        /// الحصول على اقتراح للعمود المفقود
        /// Get suggestion for missing column
        /// </summary>
        /// <param name="columnName">اسم العمود</param>
        /// <returns>اقتراح للحل</returns>
        private static string GetColumnSuggestion(string columnName)
        {
            var suggestions = new Dictionary<string, string>
            {
                { "Description", "\n\n📝 عمود Description: يستخدم لحفظ وصف تفصيلي للعنصر" },
                { "SalePrice", "\n\n💰 عمود SalePrice: يستخدم لحفظ سعر البيع للمنتج" },
                { "Location", "\n\n📍 عمود Location: يستخدم لحفظ موقع المخزن" },
                { "Email", "\n\n📧 عمود Email: يستخدم لحفظ البريد الإلكتروني" },
                { "Mobile", "\n\n📱 عمود Mobile: يستخدم لحفظ رقم الجوال" },
                { "Fax", "\n\n📠 عمود Fax: يستخدم لحفظ رقم الفاكس" },
                { "Website", "\n\n🌐 عمود Website: يستخدم لحفظ الموقع الإلكتروني" },
                { "City", "\n\n🏙️ عمود City: يستخدم لحفظ اسم المدينة" },
                { "Country", "\n\n🌍 عمود Country: يستخدم لحفظ اسم البلد" },
                { "LastLoginDate", "\n\n🕐 عمود LastLoginDate: يستخدم لحفظ تاريخ آخر دخول للمستخدم" },
                { "RoleName", "\n\n👤 عمود RoleName: يستخدم لحفظ اسم دور المستخدم" }
            };

            return suggestions.TryGetValue(columnName, out var suggestion) ? suggestion : string.Empty;
        }

        /// <summary>
        /// التحقق من إمكانية تجاهل الخطأ والمتابعة
        /// Check if error can be ignored and continue
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <returns>true إذا كان يمكن تجاهل الخطأ</returns>
        public static bool CanIgnoreError(Exception exception)
        {
            if (!IsMissingColumnError(exception))
                return false;

            var columnName = GetMissingColumnName(exception);
            
            // الأعمدة التي يمكن تجاهلها (غير أساسية)
            var ignorableColumns = new[] { "Description", "Email", "Mobile", "Fax", 
                                         "Website", "City", "Country", "Location", 
                                         "LastLoginDate", "RoleName" };

            return ignorableColumns.Contains(columnName, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// إنشاء رسالة تحذير للأعمدة المفقودة
        /// Create warning message for missing columns
        /// </summary>
        /// <param name="columnName">اسم العمود</param>
        /// <param name="tableName">اسم الجدول</param>
        /// <returns>رسالة تحذير</returns>
        public static string CreateWarningMessage(string columnName, string tableName)
        {
            return $"⚠️ تحذير: العمود '{columnName}' مفقود في جدول '{tableName}'\n" +
                   "سيتم تجاهل هذا العمود مؤقتاً، لكن يُنصح بتحديث قاعدة البيانات.";
        }

        /// <summary>
        /// الحصول على قائمة بجميع الأعمدة المطلوبة لجدول معين
        /// Get list of all required columns for a specific table
        /// </summary>
        /// <param name="tableName">اسم الجدول</param>
        /// <returns>قائمة الأعمدة المطلوبة</returns>
        public static List<string> GetRequiredColumns(string tableName)
        {
            var requiredColumns = new Dictionary<string, List<string>>
            {
                { "Companies", new List<string> { "Fax", "CommercialRecord", "ContactPerson", "EstablishmentDate", "Notes" } },
                { "Users", new List<string> { "LastLoginDate", "RoleName" } },
                { "Customers", new List<string> { "Email", "Mobile", "Fax", "Website", "City", "Country" } },
                { "Suppliers", new List<string> { "Email", "Mobile", "Fax", "Website", "City", "Country" } },
                { "Products", new List<string> { "Description", "SalePrice" } },
                { "MainCategories", new List<string> { "Description" } },
                { "SubCategories", new List<string> { "Description" } },
                { "Units", new List<string> { "Description" } },
                { "Warehouses", new List<string> { "Location", "Description", "Email", "Capacity" } }
            };

            return requiredColumns.TryGetValue(tableName, out var columns) ? columns : new List<string>();
        }
    }
}
