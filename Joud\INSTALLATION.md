# دليل التثبيت والتشغيل
## Installation and Setup Guide

---

## 📋 المتطلبات الأساسية

### 1. متطلبات النظام
- **نظام التشغيل**: Windows 10/11 أو Windows Server 2019/2022
- **المعالج**: Intel Core i3 أو أعلى
- **الذاكرة**: 4 GB RAM كحد أدنى (8 GB مستحسن)
- **مساحة القرص**: 2 GB مساحة فارغة
- **الشاشة**: دقة 1024x768 كحد أدنى (1920x1080 مستحسن)

### 2. البرمجيات المطلوبة
- **.NET 8.0 Runtime** أو أحدث
- **SQL Server 2019** أو أحدث (أو SQL Server Express المجاني)
- **Visual Studio 2022** (للتطوير فقط)

---

## 🔧 خطوات التثبيت

### الخطوة 1: تثبيت .NET 8.0
1. اذهب إلى [موقع Microsoft الرسمي](https://dotnet.microsoft.com/download/dotnet/8.0)
2. حمل **.NET 8.0 Runtime** (Desktop Apps)
3. شغل ملف التثبيت واتبع التعليمات

### الخطوة 2: تثبيت SQL Server
#### الخيار الأول: SQL Server Express (مجاني)
1. اذهب إلى [موقع SQL Server Express](https://www.microsoft.com/sql-server/sql-server-downloads)
2. حمل **SQL Server Express**
3. شغل ملف التثبيت واختر:
   - **Basic Installation**
   - اقبل الترخيص
   - اختر مجلد التثبيت
   - انتظر انتهاء التثبيت

#### الخيار الثاني: SQL Server Developer (مجاني للتطوير)
1. اذهب إلى [موقع SQL Server](https://www.microsoft.com/sql-server/sql-server-downloads)
2. حمل **SQL Server Developer**
3. اتبع نفس خطوات التثبيت

### الخطوة 3: تثبيت SQL Server Management Studio (اختياري)
1. حمل [SSMS](https://docs.microsoft.com/sql/ssms/download-sql-server-management-studio-ssms)
2. شغل ملف التثبيت
3. هذا البرنامج مفيد لإدارة قاعدة البيانات

---

## 📁 تحضير ملفات التطبيق

### للمطورين (من الكود المصدري)
```bash
# استنساخ المشروع
git clone [repository-url]
cd Joud-POS

# فتح المشروع في Visual Studio
# افتح ملف Joud.sln
```

### للمستخدمين النهائيين
1. حمل ملف التطبيق المضغوط
2. فك الضغط في مجلد مناسب (مثل: `C:\JoudPOS`)
3. تأكد من وجود جميع الملفات:
   - `Joud.exe`
   - `Database/` (مجلد ملفات قاعدة البيانات)
   - جميع ملفات DLL المطلوبة

---

## 🚀 التشغيل الأول

### 1. تشغيل التطبيق
- شغل ملف `Joud.exe`
- ستظهر شاشة الإعداد الأولي تلقائياً

### 2. إعداد قاعدة البيانات
في شاشة الإعداد الأولي:

#### أ. إعدادات قاعدة البيانات
- **اسم الخادم**: أدخل اسم خادم SQL Server
  - للتثبيت المحلي: `.\SQLEXPRESS` أو `localhost\SQLEXPRESS`
  - للخادم المحلي: `localhost` أو `.`
  - للخادم البعيد: `server-name` أو `IP-address`

#### ب. بيانات الشركة
- **اسم الشركة**: أدخل اسم شركتك (مطلوب)
- **اسم الشركة بالإنجليزية**: اختياري
- **العنوان**: عنوان الشركة
- **الهاتف**: رقم هاتف الشركة
- **البريد الإلكتروني**: بريد الشركة الإلكتروني
- **البلد**: اختر البلد (افتراضي: المملكة العربية السعودية)
- **العملة**: اختر العملة (افتراضي: SAR)

#### ج. بيانات المستخدم الرئيسي
- **اسم المستخدم**: اختر اسم مستخدم للمدير (مطلوب)
- **كلمة المرور**: أدخل كلمة مرور قوية (مطلوب)
- **تأكيد كلمة المرور**: أعد إدخال كلمة المرور
- **الاسم الكامل**: اسمك الكامل (مطلوب)

### 3. إتمام الإعداد
- اضغط **"إعداد النظام"**
- انتظر حتى انتهاء العملية (قد تستغرق دقائق قليلة)
- ستظهر رسالة نجاح الإعداد
- سيتم إعادة تشغيل التطبيق تلقائياً

---

## 🔑 تسجيل الدخول

بعد الإعداد الأولي:
1. ستظهر شاشة تسجيل الدخول
2. أدخل اسم المستخدم وكلمة المرور التي أنشأتها
3. اضغط **"دخول"**
4. ستظهر الشاشة الرئيسية للنظام

---

## ⚠️ حل المشاكل الشائعة

### مشكلة: "لا يمكن الاتصال بقاعدة البيانات"
**الحلول:**
1. تأكد من تشغيل خدمة SQL Server:
   - افتح **Services** من لوحة التحكم
   - ابحث عن **SQL Server (SQLEXPRESS)**
   - تأكد أنها تعمل (Running)

2. تحقق من اسم الخادم:
   - جرب `.\SQLEXPRESS`
   - جرب `localhost\SQLEXPRESS`
   - جرب `(local)\SQLEXPRESS`

3. تفعيل TCP/IP:
   - افتح **SQL Server Configuration Manager**
   - اذهب إلى **SQL Server Network Configuration**
   - فعل **TCP/IP**

### مشكلة: "خطأ في صلاحيات قاعدة البيانات"
**الحلول:**
1. تشغيل التطبيق كمدير:
   - انقر بالزر الأيمن على `Joud.exe`
   - اختر **"Run as administrator"**

2. إعطاء صلاحيات للمستخدم:
   - افتح **SQL Server Management Studio**
   - أضف المستخدم الحالي كـ **sysadmin**

### مشكلة: "ملفات DLL مفقودة"
**الحلول:**
1. تأكد من تثبيت .NET 8.0 Runtime
2. حمل **Visual C++ Redistributable** الأحدث
3. تأكد من وجود جميع ملفات التطبيق

### مشكلة: "خطأ في ملفات قاعدة البيانات"
**الحلول:**
1. تأكد من وجود مجلد `Database` مع ملفات SQL
2. تأكد من صلاحيات القراءة للمجلد
3. جرب تشغيل التطبيق من مجلد مختلف

---

## 🔄 النسخ الاحتياطي والاستعادة

### إنشاء نسخة احتياطية
1. من القائمة الرئيسية: **الإعدادات** > **النسخ الاحتياطي**
2. اختر مكان حفظ النسخة
3. اضغط **"إنشاء نسخة احتياطية"**

### استعادة نسخة احتياطية
1. من نفس القائمة اختر **"استعادة نسخة احتياطية"**
2. اختر ملف النسخة الاحتياطية
3. تأكيد الاستعادة

---

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:

### 1. تحقق من ملفات السجل
- ابحث عن مجلد `Logs` في مجلد التطبيق
- افتح أحدث ملف سجل لمعرفة تفاصيل الخطأ

### 2. التواصل مع الدعم الفني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **الموقع**: www.joudsystems.com

### 3. معلومات مفيدة للدعم الفني
عند التواصل، يرجى تقديم:
- إصدار نظام التشغيل
- إصدار SQL Server
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

---

## ✅ التحقق من نجاح التثبيت

للتأكد من نجاح التثبيت:
1. ✅ يفتح التطبيق بدون أخطاء
2. ✅ تتم عملية تسجيل الدخول بنجاح
3. ✅ تظهر الشاشة الرئيسية مع القوائم
4. ✅ يمكن فتح أي من القوائم الفرعية
5. ✅ تظهر بيانات الشركة والمستخدم في شريط الحالة

---

**🎉 تهانينا! تم تثبيت نظام جود للمحاسبة المالية بنجاح**

يمكنك الآن البدء في استخدام النظام لإدارة أعمالك المحاسبية والمالية.
