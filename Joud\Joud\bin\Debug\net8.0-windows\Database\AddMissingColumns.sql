-- ===================================================================
-- إضافة جميع الأعمدة المفقودة - Add All Missing Columns
-- هذا الملف يضيف جميع الأعمدة المفقودة لجميع الجداول
-- ===================================================================

USE [JoudAccountingDB]
GO

PRINT 'بدء إضافة الأعمدة المفقودة...'
GO

-- ===================================================================
-- جدول Companies - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'Fax')
BEGIN
    ALTER TABLE Companies ADD [Fax] NVARCHAR(50) NULL
    PRINT '✓ تم إضافة عمود Fax إلى جدول Companies'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'CommercialRecord')
BEGIN
    ALTER TABLE Companies ADD [CommercialRecord] NVARCHAR(50) NULL
    PRINT '✓ تم إضافة عمود CommercialRecord إلى جدول Companies'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'ContactPerson')
BEGIN
    ALTER TABLE Companies ADD [ContactPerson] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود ContactPerson إلى جدول Companies'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'EstablishmentDate')
BEGIN
    ALTER TABLE Companies ADD [EstablishmentDate] DATETIME2 NULL
    PRINT '✓ تم إضافة عمود EstablishmentDate إلى جدول Companies'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'Notes')
BEGIN
    ALTER TABLE Companies ADD [Notes] NVARCHAR(1000) NULL
    PRINT '✓ تم إضافة عمود Notes إلى جدول Companies'
END
GO

-- ===================================================================
-- جدول Users - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'LastLoginDate')
BEGIN
    ALTER TABLE Users ADD [LastLoginDate] DATETIME2 NULL
    PRINT '✓ تم إضافة عمود LastLoginDate إلى جدول Users'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'RoleName')
BEGIN
    ALTER TABLE Users ADD [RoleName] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود RoleName إلى جدول Users'
END
GO

-- ===================================================================
-- جدول MainCategories - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'MainCategories' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE MainCategories ADD [Description] NVARCHAR(500) NULL
    PRINT '✓ تم إضافة عمود Description إلى جدول MainCategories'
END
GO

-- ===================================================================
-- جدول SubCategories - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SubCategories' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE SubCategories ADD [Description] NVARCHAR(500) NULL
    PRINT '✓ تم إضافة عمود Description إلى جدول SubCategories'
END
GO

-- ===================================================================
-- جدول Units - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Units' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE Units ADD [Description] NVARCHAR(500) NULL
    PRINT '✓ تم إضافة عمود Description إلى جدول Units'
END
GO

-- ===================================================================
-- جدول Warehouses - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Warehouses' AND COLUMN_NAME = 'Location')
BEGIN
    ALTER TABLE Warehouses ADD [Location] NVARCHAR(500) NULL
    PRINT '✓ تم إضافة عمود Location إلى جدول Warehouses'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Warehouses' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE Warehouses ADD [Description] NVARCHAR(1000) NULL
    PRINT '✓ تم إضافة عمود Description إلى جدول Warehouses'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Warehouses' AND COLUMN_NAME = 'Email')
BEGIN
    ALTER TABLE Warehouses ADD [Email] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود Email إلى جدول Warehouses'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Warehouses' AND COLUMN_NAME = 'Capacity')
BEGIN
    ALTER TABLE Warehouses ADD [Capacity] DECIMAL(18,2) NULL
    PRINT '✓ تم إضافة عمود Capacity إلى جدول Warehouses'
END
GO

-- ===================================================================
-- جدول Products - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE Products ADD [Description] NVARCHAR(1000) NULL
    PRINT '✓ تم إضافة عمود Description إلى جدول Products'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'SalePrice')
BEGIN
    ALTER TABLE Products ADD [SalePrice] DECIMAL(18,2) NOT NULL DEFAULT 0
    PRINT '✓ تم إضافة عمود SalePrice إلى جدول Products'
END
GO

-- ===================================================================
-- جدول Customers - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Email')
BEGIN
    ALTER TABLE Customers ADD [Email] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود Email إلى جدول Customers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Mobile')
BEGIN
    ALTER TABLE Customers ADD [Mobile] NVARCHAR(50) NULL
    PRINT '✓ تم إضافة عمود Mobile إلى جدول Customers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Fax')
BEGIN
    ALTER TABLE Customers ADD [Fax] NVARCHAR(50) NULL
    PRINT '✓ تم إضافة عمود Fax إلى جدول Customers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Website')
BEGIN
    ALTER TABLE Customers ADD [Website] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود Website إلى جدول Customers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'City')
BEGIN
    ALTER TABLE Customers ADD [City] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود City إلى جدول Customers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Country')
BEGIN
    ALTER TABLE Customers ADD [Country] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود Country إلى جدول Customers'
END
GO

-- ===================================================================
-- جدول Suppliers - إضافة الأعمدة المفقودة
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Email')
BEGIN
    ALTER TABLE Suppliers ADD [Email] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود Email إلى جدول Suppliers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Mobile')
BEGIN
    ALTER TABLE Suppliers ADD [Mobile] NVARCHAR(50) NULL
    PRINT '✓ تم إضافة عمود Mobile إلى جدول Suppliers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Fax')
BEGIN
    ALTER TABLE Suppliers ADD [Fax] NVARCHAR(50) NULL
    PRINT '✓ تم إضافة عمود Fax إلى جدول Suppliers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Website')
BEGIN
    ALTER TABLE Suppliers ADD [Website] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود Website إلى جدول Suppliers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'City')
BEGIN
    ALTER TABLE Suppliers ADD [City] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود City إلى جدول Suppliers'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Country')
BEGIN
    ALTER TABLE Suppliers ADD [Country] NVARCHAR(100) NULL
    PRINT '✓ تم إضافة عمود Country إلى جدول Suppliers'
END
GO

PRINT '🎉 تم الانتهاء من إضافة جميع الأعمدة المفقودة بنجاح!'
PRINT 'يمكن الآن استخدام جميع الشاشات بدون أخطاء'
GO
