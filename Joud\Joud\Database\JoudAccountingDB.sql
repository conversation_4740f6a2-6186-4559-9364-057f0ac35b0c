-- ===================================================================
-- نظام جود للمحاسبة المالية - قاعدة البيانات
-- Joud Accounting System Database
-- تاريخ الإنشاء: 2024
-- ===================================================================

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'JoudAccountingDB')
BEGIN
    CREATE DATABASE [JoudAccountingDB]
    COLLATE Arabic_CI_AS
END
GO

USE [JoudAccountingDB]
GO

-- ===================================================================
-- جدول الشركات - Companies
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Companies' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Companies] (
        [CompanyId] INT IDENTITY(1,1) PRIMARY KEY,
        [CompanyName] NVARCHAR(200) NOT NULL,
        [CompanyNameEn] NVARCHAR(200) NULL,
        [Logo] VARBINARY(MAX) NULL,
        [Address] NVARCHAR(500) NULL,
        [Phone] NVARCHAR(50) NULL,
        [Fax] NVARCHAR(50) NULL,
        [Email] NVARCHAR(100) NULL,
        [Website] NVARCHAR(100) NULL,
        [Country] NVARCHAR(100) NULL,
        [Currency] NVARCHAR(10) NOT NULL DEFAULT 'SAR',
        [TaxNumber] NVARCHAR(50) NULL,
        [CommercialRegister] NVARCHAR(50) NULL,
        [CommercialRecord] NVARCHAR(50) NULL,
        [ContactPerson] NVARCHAR(100) NULL,
        [EstablishmentDate] DATETIME2 NULL,
        [Notes] NVARCHAR(1000) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL
    )
END
GO

-- إضافة الأعمدة المفقودة إلى جدول Companies إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'Fax')
BEGIN
    ALTER TABLE Companies ADD [Fax] NVARCHAR(50) NULL
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'CommercialRecord')
BEGIN
    ALTER TABLE Companies ADD [CommercialRecord] NVARCHAR(50) NULL
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'ContactPerson')
BEGIN
    ALTER TABLE Companies ADD [ContactPerson] NVARCHAR(100) NULL
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'EstablishmentDate')
BEGIN
    ALTER TABLE Companies ADD [EstablishmentDate] DATETIME2 NULL
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'Notes')
BEGIN
    ALTER TABLE Companies ADD [Notes] NVARCHAR(1000) NULL
END
GO

-- ===================================================================
-- جدول أدوار المستخدمين - User Roles
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserRoles' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[UserRoles] (
        [RoleId] INT IDENTITY(1,1) PRIMARY KEY,
        [RoleName] NVARCHAR(100) NOT NULL,
        [RoleNameEn] NVARCHAR(100) NULL,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE()
    )
END
GO

-- ===================================================================
-- جدول المستخدمين - Users
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Users] (
        [UserId] INT IDENTITY(1,1) PRIMARY KEY,
        [Username] NVARCHAR(50) NOT NULL UNIQUE,
        [PasswordHash] NVARCHAR(255) NOT NULL,
        [PasswordSalt] NVARCHAR(255) NOT NULL,
        [FullName] NVARCHAR(200) NOT NULL,
        [Email] NVARCHAR(100) NULL,
        [Phone] NVARCHAR(50) NULL,
        [RoleId] INT NOT NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [LastLogin] DATETIME2 NULL,
        [LastLoginDate] DATETIME2 NULL,
        [RoleName] NVARCHAR(100) NULL,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Users_UserRoles FOREIGN KEY (RoleId) REFERENCES UserRoles(RoleId),
        CONSTRAINT FK_Users_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId)
    )
END
GO

-- إضافة الأعمدة المفقودة إلى جدول Users إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'LastLoginDate')
BEGIN
    ALTER TABLE Users ADD [LastLoginDate] DATETIME2 NULL
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'RoleName')
BEGIN
    ALTER TABLE Users ADD [RoleName] NVARCHAR(100) NULL
END
GO

-- ===================================================================
-- جدول العملاء - Customers
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Customers] (
        [CustomerId] INT IDENTITY(1,1) PRIMARY KEY,
        [CustomerCode] NVARCHAR(20) NOT NULL UNIQUE,
        [CustomerName] NVARCHAR(200) NOT NULL,
        [CustomerNameEn] NVARCHAR(200) NULL,
        [Phone] NVARCHAR(50) NULL,
        [Mobile] NVARCHAR(50) NULL,
        [Email] NVARCHAR(100) NULL,
        [Address] NVARCHAR(500) NULL,
        [City] NVARCHAR(100) NULL,
        [Country] NVARCHAR(100) NULL,
        [TaxNumber] NVARCHAR(50) NULL,
        [CreditLimit] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CurrentBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Customers_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_Customers_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول الموردين - Suppliers
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Suppliers] (
        [SupplierId] INT IDENTITY(1,1) PRIMARY KEY,
        [SupplierCode] NVARCHAR(20) NOT NULL UNIQUE,
        [SupplierName] NVARCHAR(200) NOT NULL,
        [SupplierNameEn] NVARCHAR(200) NULL,
        [Phone] NVARCHAR(50) NULL,
        [Mobile] NVARCHAR(50) NULL,
        [Email] NVARCHAR(100) NULL,
        [Address] NVARCHAR(500) NULL,
        [City] NVARCHAR(100) NULL,
        [Country] NVARCHAR(100) NULL,
        [TaxNumber] NVARCHAR(50) NULL,
        [CreditLimit] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CurrentBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Suppliers_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_Suppliers_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول الفئات الرئيسية - Main Categories
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MainCategories' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MainCategories] (
        [MainCategoryId] INT IDENTITY(1,1) PRIMARY KEY,
        [CategoryCode] NVARCHAR(20) NOT NULL UNIQUE,
        [CategoryName] NVARCHAR(200) NOT NULL,
        [CategoryNameEn] NVARCHAR(200) NULL,
        [Description] NVARCHAR(500) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_MainCategories_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_MainCategories_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول الفئات الفرعية - Sub Categories
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SubCategories' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[SubCategories] (
        [SubCategoryId] INT IDENTITY(1,1) PRIMARY KEY,
        [CategoryCode] NVARCHAR(20) NOT NULL UNIQUE,
        [CategoryName] NVARCHAR(200) NOT NULL,
        [CategoryNameEn] NVARCHAR(200) NULL,
        [MainCategoryId] INT NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_SubCategories_MainCategories FOREIGN KEY (MainCategoryId) REFERENCES MainCategories(MainCategoryId),
        CONSTRAINT FK_SubCategories_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_SubCategories_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول الوحدات - Units
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Units' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Units] (
        [UnitId] INT IDENTITY(1,1) PRIMARY KEY,
        [UnitCode] NVARCHAR(20) NOT NULL UNIQUE,
        [UnitName] NVARCHAR(100) NOT NULL,
        [UnitNameEn] NVARCHAR(100) NULL,
        [Description] NVARCHAR(500) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Units_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_Units_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول المخازن - Warehouses
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Warehouses' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Warehouses] (
        [WarehouseId] INT IDENTITY(1,1) PRIMARY KEY,
        [WarehouseCode] NVARCHAR(20) NOT NULL UNIQUE,
        [WarehouseName] NVARCHAR(200) NOT NULL,
        [WarehouseNameEn] NVARCHAR(200) NULL,
        [Address] NVARCHAR(500) NULL,
        [Phone] NVARCHAR(50) NULL,
        [ManagerName] NVARCHAR(200) NULL,
        [Location] NVARCHAR(500) NULL,
        [Description] NVARCHAR(1000) NULL,
        [Email] NVARCHAR(100) NULL,
        [Capacity] DECIMAL(18,2) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Warehouses_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_Warehouses_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول الأصناف - Products
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Products' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Products] (
        [ProductId] INT IDENTITY(1,1) PRIMARY KEY,
        [ProductCode] NVARCHAR(20) NOT NULL UNIQUE,
        [Barcode] NVARCHAR(50) NULL,
        [ProductName] NVARCHAR(200) NOT NULL,
        [ProductNameEn] NVARCHAR(200) NULL,
        [MainCategoryId] INT NOT NULL,
        [SubCategoryId] INT NULL,
        [UnitId] INT NOT NULL,
        [WarehouseId] INT NOT NULL,
        [ProductImage] VARBINARY(MAX) NULL,
        [Description] NVARCHAR(1000) NULL,
        [PurchasePrice] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [WholesalePrice] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [HalfWholesalePrice] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [RetailPrice] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [SalePrice] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [MinimumPrice] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [IsActiveForPurchase] BIT NOT NULL DEFAULT 1,
        [IsActiveForSale] BIT NOT NULL DEFAULT 1,
        [OpeningBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CurrentStock] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [MinimumStock] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [DiscountPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TaxPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Products_MainCategories FOREIGN KEY (MainCategoryId) REFERENCES MainCategories(MainCategoryId),
        CONSTRAINT FK_Products_SubCategories FOREIGN KEY (SubCategoryId) REFERENCES SubCategories(SubCategoryId),
        CONSTRAINT FK_Products_Units FOREIGN KEY (UnitId) REFERENCES Units(UnitId),
        CONSTRAINT FK_Products_Warehouses FOREIGN KEY (WarehouseId) REFERENCES Warehouses(WarehouseId),
        CONSTRAINT FK_Products_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_Products_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول فواتير المشتريات - Purchase Invoices
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PurchaseInvoices' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[PurchaseInvoices] (
        [PurchaseInvoiceId] INT IDENTITY(1,1) PRIMARY KEY,
        [InvoiceNumber] NVARCHAR(50) NOT NULL UNIQUE,
        [InvoiceDate] DATETIME2 NOT NULL,
        [SupplierId] INT NOT NULL,
        [WarehouseId] INT NOT NULL,
        [SubTotal] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [DiscountAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [DiscountPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TaxAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TaxPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TotalAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [PaidAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [RemainingAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [PaymentStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending', -- Pending, Partial, Paid
        [Notes] NVARCHAR(1000) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_PurchaseInvoices_Suppliers FOREIGN KEY (SupplierId) REFERENCES Suppliers(SupplierId),
        CONSTRAINT FK_PurchaseInvoices_Warehouses FOREIGN KEY (WarehouseId) REFERENCES Warehouses(WarehouseId),
        CONSTRAINT FK_PurchaseInvoices_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_PurchaseInvoices_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول تفاصيل فواتير المشتريات - Purchase Invoice Details
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PurchaseInvoiceDetails' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[PurchaseInvoiceDetails] (
        [PurchaseInvoiceDetailId] INT IDENTITY(1,1) PRIMARY KEY,
        [PurchaseInvoiceId] INT NOT NULL,
        [ProductId] INT NOT NULL,
        [Quantity] DECIMAL(18,2) NOT NULL,
        [UnitPrice] DECIMAL(18,2) NOT NULL,
        [DiscountAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [DiscountPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TaxAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TaxPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TotalAmount] DECIMAL(18,2) NOT NULL,
        [ExpiryDate] DATETIME2 NULL,
        [BatchNumber] NVARCHAR(50) NULL,
        [Notes] NVARCHAR(500) NULL,
        CONSTRAINT FK_PurchaseInvoiceDetails_PurchaseInvoices FOREIGN KEY (PurchaseInvoiceId) REFERENCES PurchaseInvoices(PurchaseInvoiceId) ON DELETE CASCADE,
        CONSTRAINT FK_PurchaseInvoiceDetails_Products FOREIGN KEY (ProductId) REFERENCES Products(ProductId)
    )
END
GO
