using Microsoft.EntityFrameworkCore;
using Joud.Models;

namespace Joud.DAL
{
    /// <summary>
    /// سياق قاعدة البيانات لنظام جود المحاسبي
    /// Joud Accounting System Database Context
    /// </summary>
    public class JoudDbContext : DbContext
    {
        public JoudDbContext(DbContextOptions<JoudDbContext> options) : base(options)
        {
        }

        // DbSets - مجموعات البيانات
        public DbSet<Company> Companies { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<MainCategory> MainCategories { get; set; }
        public DbSet<SubCategory> SubCategories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<SalesInvoiceDetail> SalesInvoiceDetails { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; }
        public DbSet<SalesInvoiceItem> SalesInvoiceItems { get; set; }
        public DbSet<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryDetail> JournalEntryDetails { get; set; }
        public DbSet<Account> Accounts { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات والقيود
            ConfigureRelationships(modelBuilder);

            // تكوين الفهارس
            ConfigureIndexes(modelBuilder);

            // تكوين القيم الافتراضية
            ConfigureDefaults(modelBuilder);
        }

        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // تكوين علاقة المستخدمين مع الأدوار
            modelBuilder.Entity<User>()
                .HasOne(u => u.UserRole)
                .WithMany(r => r.Users)
                .HasForeignKey(u => u.RoleId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة المستخدمين مع الشركات
            modelBuilder.Entity<User>()
                .HasOne(u => u.Company)
                .WithMany(c => c.Users)
                .HasForeignKey(u => u.CompanyId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة العملاء مع الشركات
            modelBuilder.Entity<Customer>()
                .HasOne(c => c.Company)
                .WithMany(co => co.Customers)
                .HasForeignKey(c => c.CompanyId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة الموردين مع الشركات
            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.Company)
                .WithMany(co => co.Suppliers)
                .HasForeignKey(s => s.CompanyId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة الفئات الفرعية مع الرئيسية
            modelBuilder.Entity<SubCategory>()
                .HasOne(sc => sc.MainCategory)
                .WithMany(mc => mc.SubCategories)
                .HasForeignKey(sc => sc.MainCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة الأصناف مع الفئات
            modelBuilder.Entity<Product>()
                .HasOne(p => p.MainCategory)
                .WithMany(mc => mc.Products)
                .HasForeignKey(p => p.MainCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.SubCategory)
                .WithMany(sc => sc.Products)
                .HasForeignKey(p => p.SubCategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            // تكوين علاقة الأصناف مع الوحدات والمخازن
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Unit)
                .WithMany(u => u.Products)
                .HasForeignKey(p => p.UnitId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Warehouse)
                .WithMany(w => w.Products)
                .HasForeignKey(p => p.WarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة فواتير المبيعات
            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.Customer)
                .WithMany(c => c.SalesInvoices)
                .HasForeignKey(si => si.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.Warehouse)
                .WithMany(w => w.SalesInvoices)
                .HasForeignKey(si => si.WarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة تفاصيل فواتير المبيعات
            modelBuilder.Entity<SalesInvoiceDetail>()
                .HasOne(sid => sid.SalesInvoice)
                .WithMany(si => si.SalesInvoiceDetails)
                .HasForeignKey(sid => sid.SalesInvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<SalesInvoiceDetail>()
                .HasOne(sid => sid.Product)
                .WithMany(p => p.SalesInvoiceDetails)
                .HasForeignKey(sid => sid.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة فواتير المشتريات
            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.Supplier)
                .WithMany(s => s.PurchaseInvoices)
                .HasForeignKey(pi => pi.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.Warehouse)
                .WithMany(w => w.PurchaseInvoices)
                .HasForeignKey(pi => pi.WarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة تفاصيل فواتير المشتريات
            modelBuilder.Entity<PurchaseInvoiceDetail>()
                .HasOne(pid => pid.PurchaseInvoice)
                .WithMany(pi => pi.PurchaseInvoiceDetails)
                .HasForeignKey(pid => pid.PurchaseInvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PurchaseInvoiceDetail>()
                .HasOne(pid => pid.Product)
                .WithMany(p => p.PurchaseInvoiceDetails)
                .HasForeignKey(pid => pid.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة قيود اليومية
            modelBuilder.Entity<JournalEntry>()
                .HasOne(je => je.User)
                .WithMany()
                .HasForeignKey(je => je.UserId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<JournalEntry>()
                .HasOne(je => je.Company)
                .WithMany()
                .HasForeignKey(je => je.CompanyId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة تفاصيل قيود اليومية
            modelBuilder.Entity<JournalEntryDetail>()
                .HasOne(jed => jed.JournalEntry)
                .WithMany(je => je.JournalEntryDetails)
                .HasForeignKey(jed => jed.JournalEntryId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<JournalEntryDetail>()
                .HasOne(jed => jed.Account)
                .WithMany()
                .HasForeignKey(jed => jed.AccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقات CreatedBy و ModifiedBy للنماذج
            modelBuilder.Entity<Customer>()
                .HasOne(c => c.CreatedByUser)
                .WithMany()
                .HasForeignKey(c => c.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Supplier>()
                .HasOne(s => s.CreatedByUser)
                .WithMany()
                .HasForeignKey(s => s.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.CreatedByUser)
                .WithMany()
                .HasForeignKey(p => p.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.ModifiedByUser)
                .WithMany()
                .HasForeignKey(p => p.ModifiedBy)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<MainCategory>()
                .HasOne(mc => mc.CreatedByUser)
                .WithMany()
                .HasForeignKey(mc => mc.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SubCategory>()
                .HasOne(sc => sc.CreatedByUser)
                .WithMany()
                .HasForeignKey(sc => sc.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Unit>()
                .HasOne(u => u.CreatedByUser)
                .WithMany()
                .HasForeignKey(u => u.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Unit>()
                .HasOne(u => u.ModifiedByUser)
                .WithMany()
                .HasForeignKey(u => u.ModifiedBy)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Warehouse>()
                .HasOne(w => w.CreatedByUser)
                .WithMany()
                .HasForeignKey(w => w.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Warehouse>()
                .HasOne(w => w.ModifiedByUser)
                .WithMany()
                .HasForeignKey(w => w.ModifiedBy)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.CreatedByUser)
                .WithMany()
                .HasForeignKey(si => si.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.CreatedByUser)
                .WithMany()
                .HasForeignKey(pi => pi.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقات User مع نفسه
            modelBuilder.Entity<User>()
                .HasOne(u => u.CreatedByUser)
                .WithMany()
                .HasForeignKey(u => u.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<User>()
                .HasOne(u => u.ModifiedByUser)
                .WithMany()
                .HasForeignKey(u => u.ModifiedBy)
                .OnDelete(DeleteBehavior.SetNull);

            // تكوين علاقة User مع UserRole
            modelBuilder.Entity<User>()
                .HasOne(u => u.UserRole)
                .WithMany(ur => ur.Users)
                .HasForeignKey(u => u.RoleId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين علاقة User مع Company
            modelBuilder.Entity<User>()
                .HasOne(u => u.Company)
                .WithMany(c => c.Users)
                .HasForeignKey(u => u.CompanyId)
                .OnDelete(DeleteBehavior.Restrict);

            // تجاهل الخصائص المحسوبة أو غير المرغوب فيها
            modelBuilder.Entity<User>()
                .Ignore(u => u.CreatedCustomers)
                .Ignore(u => u.CreatedSuppliers)
                .Ignore(u => u.CreatedProducts)
                .Ignore(u => u.CreatedSalesInvoices)
                .Ignore(u => u.CreatedPurchaseInvoices);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // فهارس للأداء
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.CustomerCode)
                .IsUnique();

            modelBuilder.Entity<Supplier>()
                .HasIndex(s => s.SupplierCode)
                .IsUnique();

            modelBuilder.Entity<Product>()
                .HasIndex(p => p.ProductCode)
                .IsUnique();

            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Barcode);

            modelBuilder.Entity<SalesInvoice>()
                .HasIndex(si => si.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<PurchaseInvoice>()
                .HasIndex(pi => pi.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<JournalEntry>()
                .HasIndex(je => je.EntryNumber)
                .IsUnique();

            modelBuilder.Entity<MainCategory>()
                .HasIndex(mc => mc.CategoryCode)
                .IsUnique();

            modelBuilder.Entity<SubCategory>()
                .HasIndex(sc => sc.CategoryCode)
                .IsUnique();

            modelBuilder.Entity<Unit>()
                .HasIndex(u => u.UnitCode)
                .IsUnique();

            modelBuilder.Entity<Warehouse>()
                .HasIndex(w => w.WarehouseCode)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<UserRole>()
                .HasIndex(ur => ur.RoleName)
                .IsUnique();
        }

        private void ConfigureDefaults(ModelBuilder modelBuilder)
        {
            // تكوين القيم الافتراضية
            modelBuilder.Entity<Company>()
                .Property(c => c.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<User>()
                .Property(u => u.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Customer>()
                .Property(c => c.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Supplier>()
                .Property(s => s.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Product>()
                .Property(p => p.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<SalesInvoice>()
                .Property(si => si.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<PurchaseInvoice>()
                .Property(pi => pi.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<JournalEntry>()
                .Property(je => je.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<MainCategory>()
                .Property(mc => mc.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<SubCategory>()
                .Property(sc => sc.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Unit>()
                .Property(u => u.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            modelBuilder.Entity<Warehouse>()
                .Property(w => w.CreatedDate)
                .HasDefaultValueSql("GETDATE()");

            // تكوين أسماء الأعمدة صراحة لتجنب أخطاء Entity Framework
            modelBuilder.Entity<User>()
                .Property(u => u.RoleId)
                .HasColumnName("RoleId");

            modelBuilder.Entity<User>()
                .Property(u => u.CompanyId)
                .HasColumnName("CompanyId");

            modelBuilder.Entity<UserRole>()
                .Property(ur => ur.RoleId)
                .HasColumnName("RoleId");
        }
    }
}
