namespace Joud.Forms
{
    partial class SalesInvoicesForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            splitContainer1 = new SplitContainer();
            groupBox1 = new GroupBox();
            dgvInvoices = new DataGridView();
            panel1 = new Panel();
            dtpDateTo = new DateTimePicker();
            dtpDateFrom = new DateTimePicker();
            label2 = new Label();
            label1 = new Label();
            txtSearch = new TextBox();
            lblSearch = new Label();
            splitContainer2 = new SplitContainer();
            groupBox2 = new GroupBox();
            lblTotalAmount = new Label();
            lblDiscountAmount = new Label();
            lblTaxAmount = new Label();
            lblSubtotal = new Label();
            txtNotes = new TextBox();
            label9 = new Label();
            numDiscountRate = new NumericUpDown();
            label8 = new Label();
            numTaxRate = new NumericUpDown();
            label7 = new Label();
            cmbWarehouse = new ComboBox();
            label6 = new Label();
            cmbCustomer = new ComboBox();
            label5 = new Label();
            dtpInvoiceDate = new DateTimePicker();
            label4 = new Label();
            txtInvoiceNumber = new TextBox();
            label3 = new Label();
            groupBox3 = new GroupBox();
            dgvInvoiceItems = new DataGridView();
            panel2 = new Panel();
            btnPrint = new Button();
            btnRefresh = new Button();
            btnCancel = new Button();
            btnSave = new Button();
            btnDelete = new Button();
            btnEdit = new Button();
            btnNew = new Button();
            statusStrip1 = new StatusStrip();
            lblStatus = new ToolStripStatusLabel();
            panel3 = new Panel();
            lblTotalInvoices = new Label();
            lblTotalSales = new Label();
            lblTodaySales = new Label();
            lblMonthSales = new Label();
            ((System.ComponentModel.ISupportInitialize)splitContainer1).BeginInit();
            splitContainer1.Panel1.SuspendLayout();
            splitContainer1.Panel2.SuspendLayout();
            splitContainer1.SuspendLayout();
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvInvoices).BeginInit();
            panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)splitContainer2).BeginInit();
            splitContainer2.Panel1.SuspendLayout();
            splitContainer2.Panel2.SuspendLayout();
            splitContainer2.SuspendLayout();
            groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numDiscountRate).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numTaxRate).BeginInit();
            groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvInvoiceItems).BeginInit();
            panel2.SuspendLayout();
            statusStrip1.SuspendLayout();
            panel3.SuspendLayout();
            SuspendLayout();
            // 
            // splitContainer1
            // 
            splitContainer1.Dock = DockStyle.Fill;
            splitContainer1.Location = new Point(0, 60);
            splitContainer1.Name = "splitContainer1";
            splitContainer1.Orientation = Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            splitContainer1.Panel1.Controls.Add(groupBox1);
            splitContainer1.Panel1.RightToLeft = RightToLeft.Yes;
            // 
            // splitContainer1.Panel2
            // 
            splitContainer1.Panel2.Controls.Add(splitContainer2);
            splitContainer1.Panel2.RightToLeft = RightToLeft.Yes;
            splitContainer1.Size = new Size(1284, 741);
            splitContainer1.SplitterDistance = 350;
            splitContainer1.TabIndex = 0;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(dgvInvoices);
            groupBox1.Controls.Add(panel1);
            groupBox1.Dock = DockStyle.Fill;
            groupBox1.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupBox1.Location = new Point(0, 0);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(1284, 350);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "قائمة فواتير المبيعات";
            // 
            // dgvInvoices
            // 
            dgvInvoices.AllowUserToAddRows = false;
            dgvInvoices.AllowUserToDeleteRows = false;
            dgvInvoices.BackgroundColor = Color.White;
            dgvInvoices.BorderStyle = BorderStyle.None;
            dgvInvoices.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvInvoices.Dock = DockStyle.Fill;
            dgvInvoices.Location = new Point(3, 71);
            dgvInvoices.MultiSelect = false;
            dgvInvoices.Name = "dgvInvoices";
            dgvInvoices.ReadOnly = true;
            dgvInvoices.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvInvoices.Size = new Size(1278, 276);
            dgvInvoices.TabIndex = 1;
            // 
            // panel1
            // 
            panel1.Controls.Add(dtpDateTo);
            panel1.Controls.Add(dtpDateFrom);
            panel1.Controls.Add(label2);
            panel1.Controls.Add(label1);
            panel1.Controls.Add(txtSearch);
            panel1.Controls.Add(lblSearch);
            panel1.Dock = DockStyle.Top;
            panel1.Location = new Point(3, 21);
            panel1.Name = "panel1";
            panel1.Size = new Size(1278, 50);
            panel1.TabIndex = 0;
            // 
            // dtpDateTo
            // 
            dtpDateTo.Font = new Font("Segoe UI", 9F);
            dtpDateTo.Location = new Point(12, 15);
            dtpDateTo.Name = "dtpDateTo";
            dtpDateTo.Size = new Size(120, 23);
            dtpDateTo.TabIndex = 5;
            dtpDateTo.ValueChanged += dtpDateTo_ValueChanged;
            // 
            // dtpDateFrom
            // 
            dtpDateFrom.Font = new Font("Segoe UI", 9F);
            dtpDateFrom.Location = new Point(180, 15);
            dtpDateFrom.Name = "dtpDateFrom";
            dtpDateFrom.Size = new Size(120, 23);
            dtpDateFrom.TabIndex = 4;
            dtpDateFrom.ValueChanged += dtpDateFrom_ValueChanged;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Segoe UI", 9F);
            label2.Location = new Point(138, 19);
            label2.Name = "label2";
            label2.Size = new Size(27, 15);
            label2.TabIndex = 3;
            label2.Text = "إلى:";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Segoe UI", 9F);
            label1.Location = new Point(306, 19);
            label1.Name = "label1";
            label1.Size = new Size(25, 15);
            label1.TabIndex = 2;
            label1.Text = "من:";
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Segoe UI", 9F);
            txtSearch.Location = new Point(450, 15);
            txtSearch.Name = "txtSearch";
            txtSearch.Size = new Size(200, 23);
            txtSearch.TabIndex = 1;
            txtSearch.TextChanged += txtSearch_TextChanged;
            // 
            // lblSearch
            // 
            lblSearch.AutoSize = true;
            lblSearch.Font = new Font("Segoe UI", 9F);
            lblSearch.Location = new Point(656, 19);
            lblSearch.Name = "lblSearch";
            lblSearch.Size = new Size(32, 15);
            lblSearch.TabIndex = 0;
            lblSearch.Text = "بحث:";
            // 
            // splitContainer2
            // 
            splitContainer2.Dock = DockStyle.Fill;
            splitContainer2.Location = new Point(0, 0);
            splitContainer2.Name = "splitContainer2";
            // 
            // splitContainer2.Panel1
            // 
            splitContainer2.Panel1.Controls.Add(groupBox2);
            splitContainer2.Panel1.RightToLeft = RightToLeft.Yes;
            // 
            // splitContainer2.Panel2
            // 
            splitContainer2.Panel2.Controls.Add(groupBox3);
            splitContainer2.Panel2.RightToLeft = RightToLeft.Yes;
            splitContainer2.Size = new Size(1284, 387);
            splitContainer2.SplitterDistance = 486;
            splitContainer2.TabIndex = 0;
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(lblTotalAmount);
            groupBox2.Controls.Add(lblDiscountAmount);
            groupBox2.Controls.Add(lblTaxAmount);
            groupBox2.Controls.Add(lblSubtotal);
            groupBox2.Controls.Add(txtNotes);
            groupBox2.Controls.Add(label9);
            groupBox2.Controls.Add(numDiscountRate);
            groupBox2.Controls.Add(label8);
            groupBox2.Controls.Add(numTaxRate);
            groupBox2.Controls.Add(label7);
            groupBox2.Controls.Add(cmbWarehouse);
            groupBox2.Controls.Add(label6);
            groupBox2.Controls.Add(cmbCustomer);
            groupBox2.Controls.Add(label5);
            groupBox2.Controls.Add(dtpInvoiceDate);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(txtInvoiceNumber);
            groupBox2.Controls.Add(label3);
            groupBox2.Dock = DockStyle.Fill;
            groupBox2.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupBox2.Location = new Point(0, 0);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(486, 387);
            groupBox2.TabIndex = 0;
            groupBox2.TabStop = false;
            groupBox2.Text = "تفاصيل الفاتورة";
            // 
            // lblTotalAmount
            // 
            lblTotalAmount.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblTotalAmount.AutoSize = true;
            lblTotalAmount.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblTotalAmount.ForeColor = Color.FromArgb(39, 174, 96);
            lblTotalAmount.Location = new Point(20, 330);
            lblTotalAmount.Name = "lblTotalAmount";
            lblTotalAmount.Size = new Size(102, 21);
            lblTotalAmount.TabIndex = 17;
            lblTotalAmount.Text = "الإجمالي: 0.00";
            // 
            // lblDiscountAmount
            // 
            lblDiscountAmount.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblDiscountAmount.AutoSize = true;
            lblDiscountAmount.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblDiscountAmount.ForeColor = Color.FromArgb(155, 89, 182);
            lblDiscountAmount.Location = new Point(20, 290);
            lblDiscountAmount.Name = "lblDiscountAmount";
            lblDiscountAmount.Size = new Size(84, 19);
            lblDiscountAmount.TabIndex = 16;
            lblDiscountAmount.Text = "الخصم: 0.00";
            // 
            // lblTaxAmount
            // 
            lblTaxAmount.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblTaxAmount.AutoSize = true;
            lblTaxAmount.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblTaxAmount.ForeColor = Color.FromArgb(230, 126, 34);
            lblTaxAmount.Location = new Point(20, 260);
            lblTaxAmount.Name = "lblTaxAmount";
            lblTaxAmount.Size = new Size(91, 19);
            lblTaxAmount.TabIndex = 15;
            lblTaxAmount.Text = "الضريبة: 0.00";
            // 
            // lblSubtotal
            // 
            lblSubtotal.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblSubtotal.AutoSize = true;
            lblSubtotal.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblSubtotal.ForeColor = Color.FromArgb(52, 73, 94);
            lblSubtotal.Location = new Point(20, 230);
            lblSubtotal.Name = "lblSubtotal";
            lblSubtotal.Size = new Size(138, 19);
            lblSubtotal.TabIndex = 14;
            lblSubtotal.Text = "المجموع الفرعي: 0.00";
            // 
            // txtNotes
            // 
            txtNotes.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtNotes.Font = new Font("Segoe UI", 9F);
            txtNotes.Location = new Point(20, 150);
            txtNotes.Multiline = true;
            txtNotes.Name = "txtNotes";
            txtNotes.Size = new Size(500, 60);
            txtNotes.TabIndex = 13;
            // 
            // label9
            // 
            label9.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label9.AutoSize = true;
            label9.Font = new Font("Segoe UI", 9F);
            label9.Location = new Point(530, 153);
            label9.Name = "label9";
            label9.Size = new Size(55, 15);
            label9.TabIndex = 12;
            label9.Text = "ملاحظات:";
            // 
            // numDiscountRate
            // 
            numDiscountRate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            numDiscountRate.DecimalPlaces = 2;
            numDiscountRate.Font = new Font("Segoe UI", 9F);
            numDiscountRate.Location = new Point(320, 110);
            numDiscountRate.Name = "numDiscountRate";
            numDiscountRate.Size = new Size(200, 23);
            numDiscountRate.TabIndex = 11;
            numDiscountRate.ValueChanged += numDiscountRate_ValueChanged;
            // 
            // label8
            // 
            label8.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label8.AutoSize = true;
            label8.Font = new Font("Segoe UI", 9F);
            label8.Location = new Point(530, 113);
            label8.Name = "label8";
            label8.Size = new Size(70, 15);
            label8.TabIndex = 10;
            label8.Text = "نسبة الخصم:";
            // 
            // numTaxRate
            // 
            numTaxRate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            numTaxRate.DecimalPlaces = 2;
            numTaxRate.Font = new Font("Segoe UI", 9F);
            numTaxRate.Location = new Point(20, 110);
            numTaxRate.Name = "numTaxRate";
            numTaxRate.Size = new Size(200, 23);
            numTaxRate.TabIndex = 9;
            numTaxRate.ValueChanged += numTaxRate_ValueChanged;
            // 
            // label7
            // 
            label7.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label7.AutoSize = true;
            label7.Font = new Font("Segoe UI", 9F);
            label7.Location = new Point(230, 113);
            label7.Name = "label7";
            label7.Size = new Size(75, 15);
            label7.TabIndex = 8;
            label7.Text = "نسبة الضريبة:";
            // 
            // cmbWarehouse
            // 
            cmbWarehouse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbWarehouse.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbWarehouse.Font = new Font("Segoe UI", 9F);
            cmbWarehouse.FormattingEnabled = true;
            cmbWarehouse.Location = new Point(320, 70);
            cmbWarehouse.Name = "cmbWarehouse";
            cmbWarehouse.Size = new Size(200, 23);
            cmbWarehouse.TabIndex = 7;
            // 
            // label6
            // 
            label6.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label6.AutoSize = true;
            label6.Font = new Font("Segoe UI", 9F);
            label6.Location = new Point(530, 73);
            label6.Name = "label6";
            label6.Size = new Size(44, 15);
            label6.TabIndex = 6;
            label6.Text = "المخزن:";
            // 
            // cmbCustomer
            // 
            cmbCustomer.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCustomer.Font = new Font("Segoe UI", 9F);
            cmbCustomer.FormattingEnabled = true;
            cmbCustomer.Location = new Point(20, 70);
            cmbCustomer.Name = "cmbCustomer";
            cmbCustomer.Size = new Size(200, 23);
            cmbCustomer.TabIndex = 5;
            // 
            // label5
            // 
            label5.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label5.AutoSize = true;
            label5.Font = new Font("Segoe UI", 9F);
            label5.Location = new Point(230, 73);
            label5.Name = "label5";
            label5.Size = new Size(43, 15);
            label5.TabIndex = 4;
            label5.Text = "العميل:";
            // 
            // dtpInvoiceDate
            // 
            dtpInvoiceDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtpInvoiceDate.Font = new Font("Segoe UI", 9F);
            dtpInvoiceDate.Location = new Point(320, 30);
            dtpInvoiceDate.Name = "dtpInvoiceDate";
            dtpInvoiceDate.Size = new Size(200, 23);
            dtpInvoiceDate.TabIndex = 3;
            // 
            // label4
            // 
            label4.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label4.AutoSize = true;
            label4.Font = new Font("Segoe UI", 9F);
            label4.Location = new Point(530, 33);
            label4.Name = "label4";
            label4.Size = new Size(41, 15);
            label4.TabIndex = 2;
            label4.Text = "التاريخ:";
            // 
            // txtInvoiceNumber
            // 
            txtInvoiceNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtInvoiceNumber.Font = new Font("Segoe UI", 9F);
            txtInvoiceNumber.Location = new Point(20, 30);
            txtInvoiceNumber.Name = "txtInvoiceNumber";
            txtInvoiceNumber.Size = new Size(200, 23);
            txtInvoiceNumber.TabIndex = 1;
            // 
            // label3
            // 
            label3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label3.AutoSize = true;
            label3.Font = new Font("Segoe UI", 9F);
            label3.Location = new Point(230, 33);
            label3.Name = "label3";
            label3.Size = new Size(66, 15);
            label3.TabIndex = 0;
            label3.Text = "رقم الفاتورة:";
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(dgvInvoiceItems);
            groupBox3.Dock = DockStyle.Fill;
            groupBox3.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            groupBox3.Location = new Point(0, 0);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new Size(794, 387);
            groupBox3.TabIndex = 0;
            groupBox3.TabStop = false;
            groupBox3.Text = "بنود الفاتورة";
            // 
            // dgvInvoiceItems
            // 
            dgvInvoiceItems.BackgroundColor = Color.White;
            dgvInvoiceItems.BorderStyle = BorderStyle.None;
            dgvInvoiceItems.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvInvoiceItems.Dock = DockStyle.Fill;
            dgvInvoiceItems.Location = new Point(3, 21);
            dgvInvoiceItems.Name = "dgvInvoiceItems";
            dgvInvoiceItems.Size = new Size(788, 363);
            dgvInvoiceItems.TabIndex = 0;
            // 
            // panel2
            // 
            panel2.Controls.Add(btnPrint);
            panel2.Controls.Add(btnRefresh);
            panel2.Controls.Add(btnCancel);
            panel2.Controls.Add(btnSave);
            panel2.Controls.Add(btnDelete);
            panel2.Controls.Add(btnEdit);
            panel2.Controls.Add(btnNew);
            panel2.Dock = DockStyle.Bottom;
            panel2.Location = new Point(0, 801);
            panel2.Name = "panel2";
            panel2.Size = new Size(1284, 60);
            panel2.TabIndex = 1;
            // 
            // btnPrint
            // 
            btnPrint.BackColor = Color.FromArgb(155, 89, 182);
            btnPrint.FlatStyle = FlatStyle.Flat;
            btnPrint.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnPrint.ForeColor = Color.White;
            btnPrint.Location = new Point(950, 15);
            btnPrint.Name = "btnPrint";
            btnPrint.Size = new Size(80, 30);
            btnPrint.TabIndex = 6;
            btnPrint.Text = "طباعة";
            btnPrint.UseVisualStyleBackColor = false;
            btnPrint.Click += btnPrint_Click;
            // 
            // btnRefresh
            // 
            btnRefresh.BackColor = Color.FromArgb(52, 73, 94);
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Location = new Point(1040, 15);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(80, 30);
            btnRefresh.TabIndex = 5;
            btnRefresh.Text = "تحديث";
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // btnCancel
            // 
            btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnCancel.ForeColor = Color.White;
            btnCancel.Location = new Point(1130, 15);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(80, 30);
            btnCancel.TabIndex = 4;
            btnCancel.Text = "إلغاء";
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.Click += btnCancel_Click;
            // 
            // btnSave
            // 
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSave.ForeColor = Color.White;
            btnSave.Location = new Point(1220, 15);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(80, 30);
            btnSave.TabIndex = 3;
            btnSave.Text = "حفظ";
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += btnSave_Click;
            // 
            // btnDelete
            // 
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.FlatStyle = FlatStyle.Flat;
            btnDelete.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnDelete.ForeColor = Color.White;
            btnDelete.Location = new Point(1310, 15);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(80, 30);
            btnDelete.TabIndex = 2;
            btnDelete.Text = "حذف";
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnEdit
            // 
            btnEdit.BackColor = Color.FromArgb(230, 126, 34);
            btnEdit.FlatStyle = FlatStyle.Flat;
            btnEdit.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnEdit.ForeColor = Color.White;
            btnEdit.Location = new Point(1400, 15);
            btnEdit.Name = "btnEdit";
            btnEdit.Size = new Size(80, 30);
            btnEdit.TabIndex = 1;
            btnEdit.Text = "تعديل";
            btnEdit.UseVisualStyleBackColor = false;
            btnEdit.Click += btnEdit_Click;
            // 
            // btnNew
            // 
            btnNew.BackColor = Color.FromArgb(52, 152, 219);
            btnNew.FlatStyle = FlatStyle.Flat;
            btnNew.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnNew.ForeColor = Color.White;
            btnNew.Location = new Point(1490, 15);
            btnNew.Name = "btnNew";
            btnNew.Size = new Size(80, 30);
            btnNew.TabIndex = 0;
            btnNew.Text = "جديد";
            btnNew.UseVisualStyleBackColor = false;
            btnNew.Click += btnNew_Click;
            // 
            // statusStrip1
            // 
            statusStrip1.Items.AddRange(new ToolStripItem[] { lblStatus });
            statusStrip1.Location = new Point(0, 861);
            statusStrip1.Name = "statusStrip1";
            statusStrip1.Size = new Size(1284, 22);
            statusStrip1.TabIndex = 2;
            statusStrip1.Text = "statusStrip1";
            // 
            // lblStatus
            // 
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(30, 17);
            lblStatus.Text = "جاهز";
            // 
            // panel3
            // 
            panel3.BackColor = Color.FromArgb(52, 73, 94);
            panel3.Controls.Add(lblTotalInvoices);
            panel3.Controls.Add(lblTotalSales);
            panel3.Controls.Add(lblTodaySales);
            panel3.Controls.Add(lblMonthSales);
            panel3.Dock = DockStyle.Top;
            panel3.Location = new Point(0, 0);
            panel3.Name = "panel3";
            panel3.Size = new Size(1284, 60);
            panel3.TabIndex = 3;
            // 
            // lblTotalInvoices
            // 
            lblTotalInvoices.AutoSize = true;
            lblTotalInvoices.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblTotalInvoices.ForeColor = Color.White;
            lblTotalInvoices.Location = new Point(1200, 20);
            lblTotalInvoices.Name = "lblTotalInvoices";
            lblTotalInvoices.Size = new Size(113, 19);
            lblTotalInvoices.TabIndex = 0;
            lblTotalInvoices.Text = "إجمالي الفواتير: 0";
            // 
            // lblTotalSales
            // 
            lblTotalSales.AutoSize = true;
            lblTotalSales.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblTotalSales.ForeColor = Color.FromArgb(46, 204, 113);
            lblTotalSales.Location = new Point(800, 20);
            lblTotalSales.Name = "lblTotalSales";
            lblTotalSales.Size = new Size(122, 19);
            lblTotalSales.TabIndex = 1;
            lblTotalSales.Text = "إجمالي المبيعات: 0";
            // 
            // lblTodaySales
            // 
            lblTodaySales.AutoSize = true;
            lblTodaySales.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblTodaySales.ForeColor = Color.FromArgb(52, 152, 219);
            lblTodaySales.Location = new Point(400, 20);
            lblTodaySales.Name = "lblTodaySales";
            lblTodaySales.Size = new Size(101, 19);
            lblTodaySales.TabIndex = 2;
            lblTodaySales.Text = "مبيعات اليوم: 0";
            // 
            // lblMonthSales
            // 
            lblMonthSales.AutoSize = true;
            lblMonthSales.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblMonthSales.ForeColor = Color.FromArgb(230, 126, 34);
            lblMonthSales.Location = new Point(12, 20);
            lblMonthSales.Name = "lblMonthSales";
            lblMonthSales.Size = new Size(106, 19);
            lblMonthSales.TabIndex = 3;
            lblMonthSales.Text = "مبيعات الشهر: 0";
            // 
            // SalesInvoicesForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(240, 244, 247);
            ClientSize = new Size(1284, 883);
            Controls.Add(splitContainer1);
            Controls.Add(panel2);
            Controls.Add(statusStrip1);
            Controls.Add(panel3);
            Font = new Font("Segoe UI", 9F);
            Name = "SalesInvoicesForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "إدارة فواتير المبيعات";
            WindowState = FormWindowState.Maximized;
            Load += SalesInvoicesForm_Load;
            splitContainer1.Panel1.ResumeLayout(false);
            splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer1).EndInit();
            splitContainer1.ResumeLayout(false);
            groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvInvoices).EndInit();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            splitContainer2.Panel1.ResumeLayout(false);
            splitContainer2.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer2).EndInit();
            splitContainer2.ResumeLayout(false);
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numDiscountRate).EndInit();
            ((System.ComponentModel.ISupportInitialize)numTaxRate).EndInit();
            groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvInvoiceItems).EndInit();
            panel2.ResumeLayout(false);
            statusStrip1.ResumeLayout(false);
            statusStrip1.PerformLayout();
            panel3.ResumeLayout(false);
            panel3.PerformLayout();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.DataGridView dgvInvoices;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.DateTimePicker dtpDateTo;
        private System.Windows.Forms.DateTimePicker dtpDateFrom;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Label lblSearch;
        private System.Windows.Forms.SplitContainer splitContainer2;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label lblTotalAmount;
        private System.Windows.Forms.Label lblDiscountAmount;
        private System.Windows.Forms.Label lblTaxAmount;
        private System.Windows.Forms.Label lblSubtotal;
        private System.Windows.Forms.TextBox txtNotes;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numDiscountRate;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numTaxRate;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.ComboBox cmbWarehouse;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.ComboBox cmbCustomer;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.DateTimePicker dtpInvoiceDate;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox txtInvoiceNumber;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.DataGridView dgvInvoiceItems;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button btnPrint;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnEdit;
        private System.Windows.Forms.Button btnNew;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel lblStatus;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Label lblMonthSales;
        private System.Windows.Forms.Label lblTodaySales;
        private System.Windows.Forms.Label lblTotalSales;
        private System.Windows.Forms.Label lblTotalInvoices;
    }
}