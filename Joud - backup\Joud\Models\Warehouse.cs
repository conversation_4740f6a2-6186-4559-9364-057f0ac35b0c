using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات المخازن
    /// Warehouse Data Model
    /// </summary>
    [Table("Warehouses")]
    public class Warehouse
    {
        [Key]
        public int WarehouseId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "كود المخزن")]
        public string WarehouseCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم المخزن")]
        public string WarehouseName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "اسم المخزن بالإنجليزية")]
        public string? WarehouseNameEn { get; set; }

        [StringLength(500)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(50)]
        [Display(Name = "الهاتف")]
        public string? Phone { get; set; }

        [StringLength(200)]
        [Display(Name = "اسم المدير")]
        public string? ManagerName { get; set; }

        [StringLength(500)]
        [Display(Name = "الموقع")]
        public string? Location { get; set; }

        [StringLength(1000)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "الشركة")]
        public int CompanyId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "أنشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "عدل بواسطة")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        [ForeignKey("CompanyId")]
        public virtual Company Company { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User CreatedByUser { get; set; } = null!;

        [ForeignKey("ModifiedBy")]
        public virtual User? ModifiedByUser { get; set; }

        // Collections
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; } = new List<SalesInvoice>();
        public virtual ICollection<PurchaseInvoice> PurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
    }
}
