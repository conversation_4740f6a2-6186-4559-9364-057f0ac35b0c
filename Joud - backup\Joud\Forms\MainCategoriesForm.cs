using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة الفئات الرئيسية
    /// Main Categories Management Form
    /// </summary>
    public partial class MainCategoriesForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly MainCategoryService _mainCategoryService;
        private List<MainCategory> _mainCategories;
        private MainCategory? _selectedMainCategory;
        private bool _isEditing = false;

        public MainCategoriesForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _mainCategoryService = new MainCategoryService();
            _mainCategories = new List<MainCategory>();
            
            InitializeFormSettings();
            SetupDataGridView();
            _ = LoadMainCategoriesAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة الفئات الرئيسية - نظام جود للمحاسبة المالية";
            this.Size = new Size(1000, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private void SetupDataGridView()
        {
            // إعداد DataGridView
            dgvMainCategories.AutoGenerateColumns = false;
            dgvMainCategories.AllowUserToAddRows = false;
            dgvMainCategories.AllowUserToDeleteRows = false;
            dgvMainCategories.ReadOnly = true;
            dgvMainCategories.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvMainCategories.MultiSelect = false;
            dgvMainCategories.BackgroundColor = Color.White;
            dgvMainCategories.BorderStyle = BorderStyle.None;
            dgvMainCategories.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvMainCategories.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvMainCategories.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvMainCategories.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvMainCategories.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvMainCategories.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvMainCategories.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvMainCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategoryCode",
                HeaderText = "كود الفئة",
                DataPropertyName = "CategoryCode",
                Width = 100,
                ReadOnly = true
            });

            dgvMainCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategoryName",
                HeaderText = "اسم الفئة",
                DataPropertyName = "CategoryName",
                Width = 200,
                ReadOnly = true
            });

            dgvMainCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategoryNameEn",
                HeaderText = "الاسم بالإنجليزية",
                DataPropertyName = "CategoryNameEn",
                Width = 180,
                ReadOnly = true
            });

            dgvMainCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 250,
                ReadOnly = true
            });

            dgvMainCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SubCategoriesCount",
                HeaderText = "عدد الفئات الفرعية",
                Width = 120,
                ReadOnly = true
            });

            dgvMainCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            // أحداث DataGridView
            dgvMainCategories.SelectionChanged += DgvMainCategories_SelectionChanged;
            dgvMainCategories.CellDoubleClick += DgvMainCategories_CellDoubleClick;
            dgvMainCategories.DataBindingComplete += DgvMainCategories_DataBindingComplete;
        }

        private async Task LoadMainCategoriesAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل بيانات الفئات الرئيسية...";
                lblStatus.ForeColor = Color.Blue;

                _mainCategories = await _mainCategoryService.GetAllMainCategoriesAsync(_currentCompany.CompanyId);
                dgvMainCategories.DataSource = _mainCategories;

                // تحديث الإحصائيات
                await UpdateStatisticsAsync();

                lblStatus.Text = $"تم تحميل {_mainCategories.Count} فئة رئيسية";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات الفئات الرئيسية: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void DgvMainCategories_DataBindingComplete(object? sender, DataGridViewBindingCompleteEventArgs e)
        {
            // تحديث عدد الفئات الفرعية لكل فئة رئيسية
            foreach (DataGridViewRow row in dgvMainCategories.Rows)
            {
                if (row.DataBoundItem is MainCategory mainCategory)
                {
                    row.Cells["SubCategoriesCount"].Value = mainCategory.SubCategories.Count;
                }
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _mainCategoryService.GetMainCategoryStatisticsAsync(_currentCompany.CompanyId);
                lblTotalMainCategories.Text = $"إجمالي الفئات الرئيسية: {stats.TotalMainCategories}";
                lblTotalSubCategories.Text = $"إجمالي الفئات الفرعية: {stats.TotalSubCategories}";
                lblTotalProducts.Text = $"إجمالي الأصناف: {stats.TotalProducts}";
                lblCategoriesWithProducts.Text = $"فئات تحتوي على أصناف: {stats.CategoriesWithProducts}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        private void DgvMainCategories_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvMainCategories.SelectedRows.Count > 0)
            {
                _selectedMainCategory = dgvMainCategories.SelectedRows[0].DataBoundItem as MainCategory;
                LoadMainCategoryDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
            }
            else
            {
                _selectedMainCategory = null;
                ClearMainCategoryDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void DgvMainCategories_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadMainCategoryDetails()
        {
            if (_selectedMainCategory == null) return;

            txtCategoryCode.Text = _selectedMainCategory.CategoryCode;
            txtCategoryName.Text = _selectedMainCategory.CategoryName;
            txtCategoryNameEn.Text = _selectedMainCategory.CategoryNameEn;
            txtDescription.Text = _selectedMainCategory.Description;
            lblSubCategoriesCount.Text = $"عدد الفئات الفرعية: {_selectedMainCategory.SubCategories.Count}";
        }

        private void ClearMainCategoryDetails()
        {
            txtCategoryCode.Clear();
            txtCategoryName.Clear();
            txtCategoryNameEn.Clear();
            txtDescription.Clear();
            lblSubCategoriesCount.Text = "عدد الفئات الفرعية: 0";
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                _isEditing = false;
                _selectedMainCategory = null;
                ClearMainCategoryDetails();
                
                // إنشاء كود تلقائي
                string newCode = await _mainCategoryService.GenerateMainCategoryCodeAsync(_currentCompany.CompanyId);
                txtCategoryCode.Text = newCode;
                
                SetEditMode(true);
                txtCategoryName.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء فئة رئيسية جديدة: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedMainCategory == null)
            {
                ShowError("يرجى اختيار فئة رئيسية للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            txtCategoryName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ بيانات الفئة الرئيسية...";
                lblStatus.ForeColor = Color.Blue;

                var mainCategory = CreateMainCategoryFromInput();

                if (_isEditing && _selectedMainCategory != null)
                {
                    mainCategory.MainCategoryId = _selectedMainCategory.MainCategoryId;
                    mainCategory.CreatedDate = _selectedMainCategory.CreatedDate;
                    mainCategory.CreatedBy = _selectedMainCategory.CreatedBy;
                    mainCategory.ModifiedBy = _currentUser.UserId;

                    await _mainCategoryService.UpdateMainCategoryAsync(mainCategory);
                    lblStatus.Text = "تم تحديث بيانات الفئة الرئيسية بنجاح";
                }
                else
                {
                    mainCategory.CreatedBy = _currentUser.UserId;
                    mainCategory.CompanyId = _currentCompany.CompanyId;

                    await _mainCategoryService.AddMainCategoryAsync(mainCategory);
                    lblStatus.Text = "تم إضافة الفئة الرئيسية بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadMainCategoriesAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ بيانات الفئة الرئيسية: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedMainCategory != null)
            {
                LoadMainCategoryDetails();
            }
            else
            {
                ClearMainCategoryDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedMainCategory == null)
            {
                ShowError("يرجى اختيار فئة رئيسية للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف الفئة الرئيسية '{_selectedMainCategory.CategoryName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف الفئة الرئيسية...";
                    lblStatus.ForeColor = Color.Blue;

                    await _mainCategoryService.DeleteMainCategoryAsync(_selectedMainCategory.MainCategoryId, _currentUser.UserId);
                    
                    lblStatus.Text = "تم حذف الفئة الرئيسية بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadMainCategoriesAsync();
                    ClearMainCategoryDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف الفئة الرئيسية: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvMainCategories.DataSource = _mainCategories;
                }
                else
                {
                    var searchResults = await _mainCategoryService.SearchMainCategoriesAsync(txtSearch.Text, _currentCompany.CompanyId);
                    dgvMainCategories.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadMainCategoriesAsync();
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtCategoryName.Text))
                errors.Add("اسم الفئة مطلوب");

            if (string.IsNullOrWhiteSpace(txtCategoryCode.Text))
                errors.Add("كود الفئة مطلوب");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private MainCategory CreateMainCategoryFromInput()
        {
            return new MainCategory
            {
                CategoryCode = txtCategoryCode.Text.Trim(),
                CategoryName = txtCategoryName.Text.Trim(),
                CategoryNameEn = txtCategoryNameEn.Text.Trim(),
                Description = txtDescription.Text.Trim()
            };
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال
            txtCategoryCode.ReadOnly = isEditing && _isEditing; // الكود للقراءة فقط عند التعديل
            txtCategoryName.ReadOnly = !isEditing;
            txtCategoryNameEn.ReadOnly = !isEditing;
            txtDescription.ReadOnly = !isEditing;

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedMainCategory != null;
            btnDelete.Enabled = !isEditing && _selectedMainCategory != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            dgvMainCategories.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is DataGridView)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void MainCategoriesForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
        }
    }
}
