using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة فواتير المشتريات
    /// Purchase Invoices Management Form
    /// </summary>
    public partial class PurchaseInvoicesForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly PurchaseInvoiceService _purchaseInvoiceService;
        private readonly SupplierService _supplierService;
        private readonly WarehouseService _warehouseService;
        private readonly ProductService _productService;

        private List<PurchaseInvoice> _purchaseInvoices;
        private List<Supplier> _suppliers;
        private List<Warehouse> _warehouses;
        private List<Product> _products;
        private PurchaseInvoice? _selectedInvoice;
        private bool _isEditing = false;

        public PurchaseInvoicesForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _purchaseInvoiceService = new PurchaseInvoiceService();
            _supplierService = new SupplierService();
            _warehouseService = new WarehouseService();
            _productService = new ProductService();

            _purchaseInvoices = new List<PurchaseInvoice>();
            _suppliers = new List<Supplier>();
            _warehouses = new List<Warehouse>();
            _products = new List<Product>();

            InitializeFormSettings();
            SetupDataGridViews();
            LoadDataAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة فواتير المشتريات - نظام جود للمحاسبة المالية";
            this.Size = new Size(1600, 900);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.WindowState = FormWindowState.Maximized;
        }

        private void SetupDataGridViews()
        {
            // إعداد DataGridView للفواتير
            SetupInvoicesDataGridView();

            // إعداد DataGridView لبنود الفاتورة
            SetupInvoiceItemsDataGridView();
        }

        private void SetupInvoicesDataGridView()
        {
            dgvInvoices.AutoGenerateColumns = false;
            dgvInvoices.AllowUserToAddRows = false;
            dgvInvoices.AllowUserToDeleteRows = false;
            dgvInvoices.ReadOnly = true;
            dgvInvoices.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvInvoices.MultiSelect = false;
            dgvInvoices.BackgroundColor = Color.White;
            dgvInvoices.BorderStyle = BorderStyle.None;
            dgvInvoices.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvInvoices.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvInvoices.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvInvoices.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvInvoices.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvInvoices.EnableHeadersVisualStyles = false;

            // إضافة أعمدة الفواتير
            dgvInvoices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceNumber",
                HeaderText = "رقم الفاتورة",
                DataPropertyName = "InvoiceNumber",
                Width = 120
            });

            dgvInvoices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceDate",
                HeaderText = "التاريخ",
                DataPropertyName = "InvoiceDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            dgvInvoices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SupplierName",
                HeaderText = "المورد",
                Width = 150
            });

            dgvInvoices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WarehouseName",
                HeaderText = "المخزن",
                Width = 120
            });

            dgvInvoices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalAmount",
                HeaderText = "الإجمالي",
                DataPropertyName = "TotalAmount",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvInvoices.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedByUserName",
                HeaderText = "المستخدم",
                Width = 120
            });

            // أحداث DataGridView للفواتير
            dgvInvoices.SelectionChanged += DgvInvoices_SelectionChanged;
            dgvInvoices.CellDoubleClick += DgvInvoices_CellDoubleClick;
            dgvInvoices.DataBindingComplete += DgvInvoices_DataBindingComplete;
        }

        private void SetupInvoiceItemsDataGridView()
        {
            dgvInvoiceItems.AutoGenerateColumns = false;
            dgvInvoiceItems.AllowUserToAddRows = true;
            dgvInvoiceItems.AllowUserToDeleteRows = true;
            dgvInvoiceItems.BackgroundColor = Color.White;
            dgvInvoiceItems.BorderStyle = BorderStyle.None;
            dgvInvoiceItems.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvInvoiceItems.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvInvoiceItems.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvInvoiceItems.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvInvoiceItems.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvInvoiceItems.EnableHeadersVisualStyles = false;

            // إضافة أعمدة بنود الفاتورة
            var productColumn = new DataGridViewComboBoxColumn
            {
                Name = "ProductId",
                HeaderText = "الصنف",
                DataPropertyName = "ProductId",
                Width = 200,
                DisplayMember = "ProductName",
                ValueMember = "ProductId"
            };
            dgvInvoiceItems.Columns.Add(productColumn);

            dgvInvoiceItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "الكمية",
                DataPropertyName = "Quantity",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvInvoiceItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitPrice",
                HeaderText = "سعر الوحدة",
                DataPropertyName = "UnitPrice",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvInvoiceItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Total",
                HeaderText = "الإجمالي",
                DataPropertyName = "Total",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            // أحداث DataGridView لبنود الفاتورة
            dgvInvoiceItems.CellValueChanged += DgvInvoiceItems_CellValueChanged;
            dgvInvoiceItems.EditingControlShowing += DgvInvoiceItems_EditingControlShowing;
        }

        private async void LoadDataAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل البيانات...";
                lblStatus.ForeColor = Color.Blue;

                // تحميل البيانات المرجعية
                _suppliers = await _supplierService.GetAllSuppliersAsync(_currentCompany.CompanyId);
                _warehouses = await _warehouseService.GetAllWarehousesAsync(_currentCompany.CompanyId);
                _products = await _productService.GetAllProductsAsync(_currentCompany.CompanyId);

                // إعداد ComboBoxes
                SetupComboBoxes();

                // تحميل الفواتير
                await LoadInvoicesAsync();

                lblStatus.Text = $"تم تحميل {_purchaseInvoices.Count} فاتورة";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void SetupComboBoxes()
        {
            // إعداد ComboBox للموردين
            cmbSupplier.DataSource = _suppliers;
            cmbSupplier.DisplayMember = "SupplierName";
            cmbSupplier.ValueMember = "SupplierId";
            cmbSupplier.SelectedIndex = -1;

            // إعداد ComboBox للمخازن
            cmbWarehouse.DataSource = _warehouses;
            cmbWarehouse.DisplayMember = "WarehouseName";
            cmbWarehouse.ValueMember = "WarehouseId";
            cmbWarehouse.SelectedIndex = -1;

            // إعداد ComboBox للأصناف في DataGridView
            var productColumn = (DataGridViewComboBoxColumn)dgvInvoiceItems.Columns["ProductId"];
            productColumn.DataSource = _products;
            productColumn.DisplayMember = "ProductName";
            productColumn.ValueMember = "ProductId";
        }

        private async Task LoadInvoicesAsync()
        {
            _purchaseInvoices = await _purchaseInvoiceService.GetAllPurchaseInvoicesAsync(_currentCompany.CompanyId);
            dgvInvoices.DataSource = _purchaseInvoices;

            // تحديث الإحصائيات
            await UpdateStatisticsAsync();
        }

        private void DgvInvoices_DataBindingComplete(object? sender, DataGridViewBindingCompleteEventArgs e)
        {
            // تحديث البيانات المحسوبة
            foreach (DataGridViewRow row in dgvInvoices.Rows)
            {
                if (row.DataBoundItem is PurchaseInvoice invoice)
                {
                    row.Cells["SupplierName"].Value = invoice.Supplier?.SupplierName ?? "";
                    row.Cells["WarehouseName"].Value = invoice.Warehouse?.WarehouseName ?? "";
                    row.Cells["CreatedByUserName"].Value = invoice.CreatedByUser?.Username ?? "";
                }
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _purchaseInvoiceService.GetPurchaseInvoiceStatisticsAsync(_currentCompany.CompanyId);
                lblTotalInvoices.Text = $"إجمالي الفواتير: {stats.TotalInvoices}";
                lblTotalPurchases.Text = $"إجمالي المشتريات: {stats.TotalPurchases:N2} {_currentCompany.Currency}";
                lblTodayPurchases.Text = $"مشتريات اليوم: {stats.TodayPurchases:N2} {_currentCompany.Currency}";
                lblMonthPurchases.Text = $"مشتريات الشهر: {stats.MonthPurchases:N2} {_currentCompany.Currency}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        private void DgvInvoices_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvInvoices.SelectedRows.Count > 0)
            {
                _selectedInvoice = dgvInvoices.SelectedRows[0].DataBoundItem as PurchaseInvoice;
                LoadInvoiceDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
                btnPrint.Enabled = true;
            }
            else
            {
                _selectedInvoice = null;
                ClearInvoiceDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
                btnPrint.Enabled = false;
            }
        }

        private void DgvInvoices_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadInvoiceDetails()
        {
            if (_selectedInvoice == null) return;

            txtInvoiceNumber.Text = _selectedInvoice.InvoiceNumber;
            dtpInvoiceDate.Value = _selectedInvoice.InvoiceDate;
            cmbSupplier.SelectedValue = _selectedInvoice.SupplierId;
            cmbWarehouse.SelectedValue = _selectedInvoice.WarehouseId;
            numTaxRate.Value = _selectedInvoice.TaxRate;
            numDiscountRate.Value = _selectedInvoice.DiscountRate;
            txtNotes.Text = _selectedInvoice.Notes;

            // تحميل بنود الفاتورة
            dgvInvoiceItems.DataSource = _selectedInvoice.PurchaseInvoiceItems?.ToList() ?? new List<PurchaseInvoiceItem>();

            // حساب الإجماليات
            CalculateTotals();
        }

        private void ClearInvoiceDetails()
        {
            txtInvoiceNumber.Clear();
            dtpInvoiceDate.Value = DateTime.Now;
            cmbSupplier.SelectedIndex = -1;
            cmbWarehouse.SelectedIndex = -1;
            numTaxRate.Value = 0;
            numDiscountRate.Value = 0;
            txtNotes.Clear();
            dgvInvoiceItems.DataSource = new List<PurchaseInvoiceItem>();

            lblSubtotal.Text = "المجموع الفرعي: 0.00";
            lblTaxAmount.Text = "الضريبة: 0.00";
            lblDiscountAmount.Text = "الخصم: 0.00";
            lblTotalAmount.Text = "الإجمالي: 0.00";
        }

        private void DgvInvoiceItems_CellValueChanged(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && (e.ColumnIndex == dgvInvoiceItems.Columns["Quantity"].Index ||
                                   e.ColumnIndex == dgvInvoiceItems.Columns["UnitPrice"].Index ||
                                   e.ColumnIndex == dgvInvoiceItems.Columns["ProductId"].Index))
            {
                CalculateRowTotal(e.RowIndex);
                CalculateTotals();
            }
        }

        private void DgvInvoiceItems_EditingControlShowing(object? sender, DataGridViewEditingControlShowingEventArgs e)
        {
            if (dgvInvoiceItems.CurrentCell.ColumnIndex == dgvInvoiceItems.Columns["ProductId"].Index)
            {
                var comboBox = e.Control as ComboBox;
                if (comboBox != null)
                {
                    comboBox.SelectedIndexChanged -= ComboBox_SelectedIndexChanged;
                    comboBox.SelectedIndexChanged += ComboBox_SelectedIndexChanged;
                }
            }
        }

        private void ComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox?.SelectedValue != null && int.TryParse(comboBox.SelectedValue.ToString(), out int productId))
            {
                var product = _products.FirstOrDefault(p => p.ProductId == productId);
                if (product != null && dgvInvoiceItems.CurrentRow != null)
                {
                    // تعيين سعر الشراء تلقائياً
                    dgvInvoiceItems.CurrentRow.Cells["UnitPrice"].Value = product.PurchasePrice;
                    CalculateRowTotal(dgvInvoiceItems.CurrentRow.Index);
                    CalculateTotals();
                }
            }
        }

        private void CalculateRowTotal(int rowIndex)
        {
            if (rowIndex >= 0 && rowIndex < dgvInvoiceItems.Rows.Count)
            {
                var row = dgvInvoiceItems.Rows[rowIndex];

                if (decimal.TryParse(row.Cells["Quantity"].Value?.ToString(), out decimal quantity) &&
                    decimal.TryParse(row.Cells["UnitPrice"].Value?.ToString(), out decimal unitPrice))
                {
                    row.Cells["Total"].Value = quantity * unitPrice;
                }
            }
        }

        private void CalculateTotals()
        {
            decimal subtotal = 0;

            foreach (DataGridViewRow row in dgvInvoiceItems.Rows)
            {
                if (row.Cells["Total"].Value != null &&
                    decimal.TryParse(row.Cells["Total"].Value.ToString(), out decimal total))
                {
                    subtotal += total;
                }
            }

            decimal taxAmount = subtotal * (numTaxRate.Value / 100);
            decimal discountAmount = subtotal * (numDiscountRate.Value / 100);
            decimal totalAmount = subtotal + taxAmount - discountAmount;

            lblSubtotal.Text = $"المجموع الفرعي: {subtotal:N2}";
            lblTaxAmount.Text = $"الضريبة: {taxAmount:N2}";
            lblDiscountAmount.Text = $"الخصم: {discountAmount:N2}";
            lblTotalAmount.Text = $"الإجمالي: {totalAmount:N2}";
        }

        private void numTaxRate_ValueChanged(object sender, EventArgs e)
        {
            CalculateTotals();
        }

        private void numDiscountRate_ValueChanged(object sender, EventArgs e)
        {
            CalculateTotals();
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                if (_suppliers.Count == 0)
                {
                    ShowError("يجب إنشاء مورد واحد على الأقل أولاً");
                    return;
                }

                if (_warehouses.Count == 0)
                {
                    ShowError("يجب إنشاء مخزن واحد على الأقل أولاً");
                    return;
                }

                if (_products.Count == 0)
                {
                    ShowError("يجب إنشاء صنف واحد على الأقل أولاً");
                    return;
                }

                _isEditing = false;
                _selectedInvoice = null;
                ClearInvoiceDetails();

                // إنشاء رقم فاتورة تلقائي
                string newNumber = await _purchaseInvoiceService.GeneratePurchaseInvoiceNumberAsync(_currentCompany.CompanyId);
                txtInvoiceNumber.Text = newNumber;

                SetEditMode(true);
                cmbSupplier.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء فاتورة جديدة: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedInvoice == null)
            {
                ShowError("يرجى اختيار فاتورة للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            cmbSupplier.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ الفاتورة...";
                lblStatus.ForeColor = Color.Blue;

                var invoice = CreateInvoiceFromInput();

                if (_isEditing && _selectedInvoice != null)
                {
                    invoice.PurchaseInvoiceId = _selectedInvoice.PurchaseInvoiceId;
                    invoice.CreatedDate = _selectedInvoice.CreatedDate;
                    invoice.CreatedBy = _selectedInvoice.CreatedBy;
                    invoice.ModifiedBy = _currentUser.UserId;

                    await _purchaseInvoiceService.UpdatePurchaseInvoiceAsync(invoice);
                    lblStatus.Text = "تم تحديث الفاتورة بنجاح";
                }
                else
                {
                    invoice.CreatedBy = _currentUser.UserId;
                    invoice.CompanyId = _currentCompany.CompanyId;

                    await _purchaseInvoiceService.AddPurchaseInvoiceAsync(invoice);
                    lblStatus.Text = "تم إضافة الفاتورة بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadInvoicesAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ الفاتورة: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedInvoice != null)
            {
                LoadInvoiceDetails();
            }
            else
            {
                ClearInvoiceDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedInvoice == null)
            {
                ShowError("يرجى اختيار فاتورة للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف الفاتورة '{_selectedInvoice.InvoiceNumber}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف الفاتورة...";
                    lblStatus.ForeColor = Color.Blue;

                    await _purchaseInvoiceService.DeletePurchaseInvoiceAsync(_selectedInvoice.PurchaseInvoiceId, _currentUser.UserId);

                    lblStatus.Text = "تم حذف الفاتورة بنجاح";
                    lblStatus.ForeColor = Color.Green;

                    await LoadInvoicesAsync();
                    ClearInvoiceDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف الفاتورة: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvInvoices.DataSource = _purchaseInvoices;
                }
                else
                {
                    var searchResults = await _purchaseInvoiceService.SearchPurchaseInvoicesAsync(
                        txtSearch.Text, _currentCompany.CompanyId, dtpDateFrom.Value, dtpDateTo.Value);
                    dgvInvoices.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataAsync();
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            if (_selectedInvoice == null)
            {
                ShowError("يرجى اختيار فاتورة للطباعة");
                return;
            }

            try
            {
                var printService = new PrintService();
                printService.PrintPurchaseInvoice(_selectedInvoice, _currentCompany);
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في طباعة الفاتورة: {ex.Message}");
            }
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtInvoiceNumber.Text))
                errors.Add("رقم الفاتورة مطلوب");

            if (cmbSupplier.SelectedValue == null)
                errors.Add("يجب اختيار المورد");

            if (cmbWarehouse.SelectedValue == null)
                errors.Add("يجب اختيار المخزن");

            // التحقق من وجود بنود في الفاتورة
            bool hasValidItems = false;
            foreach (DataGridViewRow row in dgvInvoiceItems.Rows)
            {
                if (!row.IsNewRow && row.Cells["ProductId"].Value != null)
                {
                    hasValidItems = true;
                    break;
                }
            }

            if (!hasValidItems)
                errors.Add("يجب إضافة بند واحد على الأقل للفاتورة");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private PurchaseInvoice CreateInvoiceFromInput()
        {
            var invoice = new PurchaseInvoice
            {
                InvoiceNumber = txtInvoiceNumber.Text.Trim(),
                InvoiceDate = dtpInvoiceDate.Value,
                SupplierId = cmbSupplier.SelectedValue != null ? (int)cmbSupplier.SelectedValue : 0,
                WarehouseId = cmbWarehouse.SelectedValue != null ? (int)cmbWarehouse.SelectedValue : 0,
                TaxRate = numTaxRate.Value,
                DiscountRate = numDiscountRate.Value,
                Notes = txtNotes.Text.Trim(),
                PurchaseInvoiceItems = new List<PurchaseInvoiceItem>()
            };

            // إضافة البنود
            foreach (DataGridViewRow row in dgvInvoiceItems.Rows)
            {
                if (!row.IsNewRow && row.Cells["ProductId"].Value != null)
                {
                    var item = new PurchaseInvoiceItem
                    {
                        ProductId = (int)row.Cells["ProductId"].Value,
                        Quantity = Convert.ToDecimal(row.Cells["Quantity"].Value ?? 0),
                        UnitPrice = Convert.ToDecimal(row.Cells["UnitPrice"].Value ?? 0)
                    };

                    if (item.Quantity > 0 && item.UnitPrice >= 0)
                    {
                        invoice.PurchaseInvoiceItems.Add(item);
                    }
                }
            }

            return invoice;
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال
            txtInvoiceNumber.ReadOnly = isEditing && _isEditing; // الرقم للقراءة فقط عند التعديل
            dtpInvoiceDate.Enabled = isEditing;
            cmbSupplier.Enabled = isEditing;
            cmbWarehouse.Enabled = isEditing;
            numTaxRate.ReadOnly = !isEditing;
            numDiscountRate.ReadOnly = !isEditing;
            txtNotes.ReadOnly = !isEditing;
            dgvInvoiceItems.ReadOnly = !isEditing;

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedInvoice != null;
            btnDelete.Enabled = !isEditing && _selectedInvoice != null;
            btnPrint.Enabled = !isEditing && _selectedInvoice != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            dgvInvoices.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;
            dtpDateFrom.Enabled = !isEditing;
            dtpDateTo.Enabled = !isEditing;
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is ComboBox ||
                    control is NumericUpDown || control is DataGridView || control is DateTimePicker)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void PurchaseInvoicesForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
            dtpDateFrom.Value = DateTime.Now.AddMonths(-1);
            dtpDateTo.Value = DateTime.Now;
        }

        private void dtpDateFrom_ValueChanged(object sender, EventArgs e)
        {
            txtSearch_TextChanged(sender, e);
        }

        private void dtpDateTo_ValueChanged(object sender, EventArgs e)
        {
            txtSearch_TextChanged(sender, e);
        }
    }
}