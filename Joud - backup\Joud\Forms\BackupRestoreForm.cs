using System;
using System.ComponentModel;
using System.Data;
using Microsoft.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using Joud.Services;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة النسخ الاحتياطي والاستعادة - Backup & Restore Form
    /// تسمح بإنشاء واستعادة النسخ الاحتياطية لقاعدة البيانات
    /// </summary>
    public partial class BackupRestoreForm : Form
    {
        #region Private Fields

        private readonly DatabaseService _databaseService;
        private BackgroundWorker _backgroundWorker;
        private bool _isOperationInProgress = false;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ شاشة النسخ الاحتياطي والاستعادة
        /// </summary>
        public BackupRestoreForm()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            InitializeBackgroundWorker();
            InitializeForm();
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل الشاشة
        /// </summary>
        private void BackupRestoreForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoadBackupHistory();
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Button Events

        /// <summary>
        /// حدث النقر على زر استعراض مسار النسخة الاحتياطية
        /// </summary>
        private void btnBrowseBackup_Click(object sender, EventArgs e)
        {
            try
            {
                saveFileDialog1.Filter = "ملفات النسخ الاحتياطي (*.bak)|*.bak|جميع الملفات (*.*)|*.*";
                saveFileDialog1.DefaultExt = "bak";
                saveFileDialog1.FileName = $"JoudBackup_{DateTime.Now:yyyyMMdd_HHmmss}.bak";
                
                if (saveFileDialog1.ShowDialog() == DialogResult.OK)
                {
                    txtBackupPath.Text = saveFileDialog1.FileName;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار مسار النسخة الاحتياطية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر استعراض ملف الاستعادة
        /// </summary>
        private void btnBrowseRestore_Click(object sender, EventArgs e)
        {
            try
            {
                openFileDialog1.Filter = "ملفات النسخ الاحتياطي (*.bak)|*.bak|جميع الملفات (*.*)|*.*";
                openFileDialog1.DefaultExt = "bak";
                
                if (openFileDialog1.ShowDialog() == DialogResult.OK)
                {
                    txtRestorePath.Text = openFileDialog1.FileName;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار ملف الاستعادة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر إنشاء نسخة احتياطية
        /// </summary>
        private void btnCreateBackup_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateBackupSettings())
                {
                    CreateBackup();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر استعادة النسخة الاحتياطية
        /// </summary>
        private void btnRestoreBackup_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateRestoreSettings())
                {
                    var result = MessageBox.Show(
                        "تحذير: ستؤدي عملية الاستعادة إلى استبدال جميع البيانات الحالية.\n" +
                        "هل تريد المتابعة؟", 
                        "تأكيد الاستعادة", 
                        MessageBoxButtons.YesNo, 
                        MessageBoxIcon.Warning);
                    
                    if (result == DialogResult.Yes)
                    {
                        RestoreBackup();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تحديث السجل
        /// </summary>
        private void btnRefreshHistory_Click(object sender, EventArgs e)
        {
            try
            {
                LoadBackupHistory();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث سجل النسخ الاحتياطية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر حذف النسخة الاحتياطية
        /// </summary>
        private void btnDeleteBackup_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvBackupHistory.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvBackupHistory.SelectedRows[0];
                    string backupPath = selectedRow.Cells["BackupPath"].Value?.ToString();
                    
                    if (!string.IsNullOrEmpty(backupPath))
                    {
                        var result = MessageBox.Show(
                            $"هل تريد حذف النسخة الاحتياطية؟\n{backupPath}", 
                            "تأكيد الحذف", 
                            MessageBoxButtons.YesNo, 
                            MessageBoxIcon.Question);
                        
                        if (result == DialogResult.Yes)
                        {
                            DeleteBackupFile(backupPath);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار نسخة احتياطية للحذف.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف النسخة الاحتياطية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تهيئة الشاشة
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // تعيين القيم الافتراضية
                chkIncludeSchema.Checked = true;
                chkIncludeData.Checked = true;
                chkCompressBackup.Checked = true;
                chkOverwriteExisting.Checked = false;
                
                // إعداد شريط التقدم
                progressBar1.Visible = false;
                lblCurrentOperation.Text = "";
                
                // إعداد جدول سجل النسخ الاحتياطية
                SetupBackupHistoryGrid();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تهيئة BackgroundWorker
        /// </summary>
        private void InitializeBackgroundWorker()
        {
            try
            {
                _backgroundWorker = new BackgroundWorker
                {
                    WorkerReportsProgress = true,
                    WorkerSupportsCancellation = true
                };
                
                _backgroundWorker.DoWork += BackgroundWorker_DoWork;
                _backgroundWorker.ProgressChanged += BackgroundWorker_ProgressChanged;
                _backgroundWorker.RunWorkerCompleted += BackgroundWorker_RunWorkerCompleted;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة BackgroundWorker: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد جدول سجل النسخ الاحتياطية
        /// </summary>
        private void SetupBackupHistoryGrid()
        {
            try
            {
                dgvBackupHistory.AutoGenerateColumns = false;
                dgvBackupHistory.Columns.Clear();
                
                // إضافة الأعمدة
                dgvBackupHistory.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "BackupDate",
                    HeaderText = "تاريخ النسخة",
                    DataPropertyName = "BackupDate",
                    Width = 150
                });
                
                dgvBackupHistory.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "BackupPath",
                    HeaderText = "مسار الملف",
                    DataPropertyName = "BackupPath",
                    Width = 300
                });
                
                dgvBackupHistory.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "FileSize",
                    HeaderText = "حجم الملف",
                    DataPropertyName = "FileSize",
                    Width = 100
                });
                
                dgvBackupHistory.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Status",
                    HeaderText = "الحالة",
                    DataPropertyName = "Status",
                    Width = 100
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد جدول سجل النسخ الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة إعدادات النسخة الاحتياطية
        /// </summary>
        private bool ValidateBackupSettings()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtBackupPath.Text))
                {
                    MessageBox.Show("يرجى اختيار مسار لحفظ النسخة الاحتياطية.", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabControl1.SelectedTab = tabBackup;
                    btnBrowseBackup.Focus();
                    return false;
                }
                
                if (!chkIncludeSchema.Checked && !chkIncludeData.Checked)
                {
                    MessageBox.Show("يجب اختيار تضمين الهيكل أو البيانات على الأقل.", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabControl1.SelectedTab = tabBackup;
                    return false;
                }
                
                // التحقق من وجود المجلد
                string directory = Path.GetDirectoryName(txtBackupPath.Text);
                if (!Directory.Exists(directory))
                {
                    MessageBox.Show("المجلد المحدد غير موجود.", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من إعدادات النسخة الاحتياطية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة إعدادات الاستعادة
        /// </summary>
        private bool ValidateRestoreSettings()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtRestorePath.Text))
                {
                    MessageBox.Show("يرجى اختيار ملف النسخة الاحتياطية للاستعادة.", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabControl1.SelectedTab = tabRestore;
                    btnBrowseRestore.Focus();
                    return false;
                }
                
                if (!File.Exists(txtRestorePath.Text))
                {
                    MessageBox.Show("ملف النسخة الاحتياطية غير موجود.", "خطأ في التحقق", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من إعدادات الاستعادة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        private void CreateBackup()
        {
            try
            {
                if (_isOperationInProgress)
                {
                    MessageBox.Show("هناك عملية قيد التنفيذ بالفعل.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                
                var backupOptions = new BackupOptions
                {
                    BackupPath = txtBackupPath.Text,
                    IncludeSchema = chkIncludeSchema.Checked,
                    IncludeData = chkIncludeData.Checked,
                    CompressBackup = chkCompressBackup.Checked
                };
                
                _isOperationInProgress = true;
                UpdateUI();
                
                _backgroundWorker.RunWorkerAsync(new { Operation = "Backup", Options = backupOptions });
            }
            catch (Exception ex)
            {
                _isOperationInProgress = false;
                UpdateUI();
                throw;
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        private void RestoreBackup()
        {
            try
            {
                if (_isOperationInProgress)
                {
                    MessageBox.Show("هناك عملية قيد التنفيذ بالفعل.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                
                var restoreOptions = new RestoreOptions
                {
                    BackupPath = txtRestorePath.Text,
                    OverwriteExisting = chkOverwriteExisting.Checked
                };
                
                _isOperationInProgress = true;
                UpdateUI();
                
                _backgroundWorker.RunWorkerAsync(new { Operation = "Restore", Options = restoreOptions });
            }
            catch (Exception ex)
            {
                _isOperationInProgress = false;
                UpdateUI();
                throw;
            }
        }

        /// <summary>
        /// تحميل سجل النسخ الاحتياطية
        /// </summary>
        private void LoadBackupHistory()
        {
            try
            {
                // هنا يمكن تحميل سجل النسخ الاحتياطية من قاعدة البيانات أو ملف
                // للتبسيط، سنعرض قائمة فارغة
                dgvBackupHistory.DataSource = null;
                
                lblStatus.Text = "تم تحديث سجل النسخ الاحتياطية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل سجل النسخ الاحتياطية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف ملف النسخة الاحتياطية
        /// </summary>
        private void DeleteBackupFile(string backupPath)
        {
            try
            {
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                    MessageBox.Show("تم حذف النسخة الاحتياطية بنجاح.", "نجح الحذف", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadBackupHistory();
                }
                else
                {
                    MessageBox.Show("ملف النسخة الاحتياطية غير موجود.", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف النسخة الاحتياطية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث واجهة المستخدم
        /// </summary>
        private void UpdateUI()
        {
            try
            {
                // تحديث حالة الأزرار
                btnCreateBackup.Enabled = !_isOperationInProgress;
                btnRestoreBackup.Enabled = !_isOperationInProgress;
                btnBrowseBackup.Enabled = !_isOperationInProgress;
                btnBrowseRestore.Enabled = !_isOperationInProgress;
                
                // تحديث شريط التقدم
                progressBar1.Visible = _isOperationInProgress;
                
                if (!_isOperationInProgress)
                {
                    lblCurrentOperation.Text = "";
                    lblStatus.Text = "جاهز";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة المستخدم: {ex.Message}");
            }
        }

        #endregion

        #region BackgroundWorker Events

        /// <summary>
        /// حدث تنفيذ العمل في الخلفية
        /// </summary>
        private void BackgroundWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                var worker = sender as BackgroundWorker;
                var args = e.Argument as dynamic;
                
                if (args.Operation == "Backup")
                {
                    var options = args.Options as BackupOptions;
                    worker.ReportProgress(0, "بدء إنشاء النسخة الاحتياطية...");
                    
                    // تنفيذ عملية النسخ الاحتياطي
                    // هنا يجب استخدام DatabaseService لتنفيذ العملية الفعلية
                    worker.ReportProgress(50, "جاري إنشاء النسخة الاحتياطية...");
                    
                    // محاكاة العملية
                    System.Threading.Thread.Sleep(3000);
                    
                    worker.ReportProgress(100, "تم إنشاء النسخة الاحتياطية بنجاح");
                    e.Result = "تم إنشاء النسخة الاحتياطية بنجاح";
                }
                else if (args.Operation == "Restore")
                {
                    var options = args.Options as RestoreOptions;
                    worker.ReportProgress(0, "بدء استعادة النسخة الاحتياطية...");
                    
                    // تنفيذ عملية الاستعادة
                    worker.ReportProgress(50, "جاري استعادة النسخة الاحتياطية...");
                    
                    // محاكاة العملية
                    System.Threading.Thread.Sleep(3000);
                    
                    worker.ReportProgress(100, "تم استعادة النسخة الاحتياطية بنجاح");
                    e.Result = "تم استعادة النسخة الاحتياطية بنجاح";
                }
            }
            catch (Exception ex)
            {
                e.Result = $"خطأ: {ex.Message}";
            }
        }

        /// <summary>
        /// حدث تقرير التقدم
        /// </summary>
        private void BackgroundWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            try
            {
                progressBar1.Value = e.ProgressPercentage;
                lblCurrentOperation.Text = e.UserState?.ToString() ?? "";
                lblStatus.Text = e.UserState?.ToString() ?? "";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث التقدم: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث اكتمال العمل
        /// </summary>
        private void BackgroundWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            try
            {
                _isOperationInProgress = false;
                UpdateUI();
                
                if (e.Error != null)
                {
                    MessageBox.Show($"خطأ في العملية: {e.Error.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    string result = e.Result?.ToString() ?? "تمت العملية";
                    
                    if (result.StartsWith("خطأ:"))
                    {
                        MessageBox.Show(result, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    else
                    {
                        MessageBox.Show(result, "نجحت العملية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadBackupHistory();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة نتيجة العملية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Helper Classes

        /// <summary>
        /// خيارات النسخة الاحتياطية
        /// </summary>
        public class BackupOptions
        {
            public string BackupPath { get; set; }
            public bool IncludeSchema { get; set; }
            public bool IncludeData { get; set; }
            public bool CompressBackup { get; set; }
        }

        /// <summary>
        /// خيارات الاستعادة
        /// </summary>
        public class RestoreOptions
        {
            public string BackupPath { get; set; }
            public bool OverwriteExisting { get; set; }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// عرض شاشة النسخ الاحتياطي والاستعادة
        /// </summary>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>نتيجة الحوار</returns>
        public static DialogResult ShowBackupRestore(IWin32Window parent = null)
        {
            try
            {
                using (var backupForm = new BackupRestoreForm())
                {
                    return backupForm.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض شاشة النسخ الاحتياطي: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        #endregion

        #region Dispose

        /// <summary>
        /// تحرير الموارد
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _backgroundWorker?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
