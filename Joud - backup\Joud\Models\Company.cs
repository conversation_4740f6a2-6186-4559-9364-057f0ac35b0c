using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات الشركة
    /// Company Data Model
    /// </summary>
    [Table("Companies")]
    public class Company
    {
        [Key]
        public int CompanyId { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الشركة")]
        public string CompanyName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "اسم الشركة بالإنجليزية")]
        public string? CompanyNameEn { get; set; }

        [Display(Name = "الشعار")]
        public byte[]? Logo { get; set; }

        [StringLength(500)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(50)]
        [Display(Name = "الهاتف")]
        public string? Phone { get; set; }

        [StringLength(50)]
        [Display(Name = "الفاكس")]
        public string? Fax { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(100)]
        [Display(Name = "الموقع الإلكتروني")]
        public string? Website { get; set; }

        [StringLength(100)]
        [Display(Name = "البلد")]
        public string? Country { get; set; }

        [Required]
        [StringLength(10)]
        [Display(Name = "العملة")]
        public string Currency { get; set; } = "SAR";

        [StringLength(50)]
        [Display(Name = "الرقم الضريبي")]
        public string? TaxNumber { get; set; }

        [StringLength(50)]
        [Display(Name = "السجل التجاري")]
        public string? CommercialRegister { get; set; }

        [StringLength(50)]
        [Display(Name = "السجل التجاري")]
        public string? CommercialRecord { get; set; }

        [StringLength(100)]
        [Display(Name = "الشخص المسؤول")]
        public string? ContactPerson { get; set; }

        [Display(Name = "تاريخ التأسيس")]
        public DateTime? EstablishmentDate { get; set; }

        [StringLength(1000)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "أنشئ بواسطة")]
        public int? CreatedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "عدل بواسطة")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; } = new List<SalesInvoice>();
        public virtual ICollection<PurchaseInvoice> PurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
    }
}
