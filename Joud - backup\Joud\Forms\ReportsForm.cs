using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة التقارير
    /// Reports Form
    /// </summary>
    public partial class ReportsForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly ReportService _reportService;

        public ReportsForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _reportService = new ReportService();
            
            InitializeFormSettings();
            SetupReportButtons();
        }

        private void InitializeFormSettings()
        {
            this.Text = "التقارير - نظام جود للمحاسبة المالية";
            this.Size = new Size(1200, 800);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.WindowState = FormWindowState.Maximized;
        }

        private void SetupReportButtons()
        {
            // إعداد أزرار التقارير
            var buttonStyle = new
            {
                Size = new Size(200, 80),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // تقارير المبيعات
            var btnSalesReport = new Button
            {
                Text = "تقرير المبيعات",
                Size = buttonStyle.Size,
                Font = buttonStyle.Font,
                BackColor = buttonStyle.BackColor,
                ForeColor = buttonStyle.ForeColor,
                FlatStyle = buttonStyle.FlatStyle,
                Location = new Point(50, 50)
            };
            btnSalesReport.Click += BtnSalesReport_Click;
            this.Controls.Add(btnSalesReport);

            // تقارير المشتريات
            var btnPurchaseReport = new Button
            {
                Text = "تقرير المشتريات",
                Size = buttonStyle.Size,
                Font = buttonStyle.Font,
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = buttonStyle.ForeColor,
                FlatStyle = buttonStyle.FlatStyle,
                Location = new Point(270, 50)
            };
            btnPurchaseReport.Click += BtnPurchaseReport_Click;
            this.Controls.Add(btnPurchaseReport);

            // تقرير أرصدة العملاء
            var btnCustomerBalanceReport = new Button
            {
                Text = "أرصدة العملاء",
                Size = buttonStyle.Size,
                Font = buttonStyle.Font,
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = buttonStyle.ForeColor,
                FlatStyle = buttonStyle.FlatStyle,
                Location = new Point(490, 50)
            };
            btnCustomerBalanceReport.Click += BtnCustomerBalanceReport_Click;
            this.Controls.Add(btnCustomerBalanceReport);

            // تقرير أرصدة الموردين
            var btnSupplierBalanceReport = new Button
            {
                Text = "أرصدة الموردين",
                Size = buttonStyle.Size,
                Font = buttonStyle.Font,
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = buttonStyle.ForeColor,
                FlatStyle = buttonStyle.FlatStyle,
                Location = new Point(710, 50)
            };
            btnSupplierBalanceReport.Click += BtnSupplierBalanceReport_Click;
            this.Controls.Add(btnSupplierBalanceReport);

            // تقرير المخزون
            var btnInventoryReport = new Button
            {
                Text = "تقرير المخزون",
                Size = buttonStyle.Size,
                Font = buttonStyle.Font,
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = buttonStyle.ForeColor,
                FlatStyle = buttonStyle.FlatStyle,
                Location = new Point(50, 150)
            };
            btnInventoryReport.Click += BtnInventoryReport_Click;
            this.Controls.Add(btnInventoryReport);

            // تقرير حركة المخزون
            var btnInventoryMovementReport = new Button
            {
                Text = "حركة المخزون",
                Size = buttonStyle.Size,
                Font = buttonStyle.Font,
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = buttonStyle.ForeColor,
                FlatStyle = buttonStyle.FlatStyle,
                Location = new Point(270, 150)
            };
            btnInventoryMovementReport.Click += BtnInventoryMovementReport_Click;
            this.Controls.Add(btnInventoryMovementReport);
        }

        private async void BtnSalesReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var dateForm = new DateRangeForm();
                if (dateForm.ShowDialog() == DialogResult.OK)
                {
                    var report = await _reportService.GetSalesReportAsync(
                        _currentCompany.CompanyId, 
                        dateForm.DateFrom, 
                        dateForm.DateTo);

                    ShowSalesReport(report);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المبيعات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnPurchaseReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var dateForm = new DateRangeForm();
                if (dateForm.ShowDialog() == DialogResult.OK)
                {
                    var report = await _reportService.GetPurchaseReportAsync(
                        _currentCompany.CompanyId, 
                        dateForm.DateFrom, 
                        dateForm.DateTo);

                    ShowPurchaseReport(report);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المشتريات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnCustomerBalanceReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var report = await _reportService.GetCustomerBalanceReportAsync(_currentCompany.CompanyId);
                ShowCustomerBalanceReport(report);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير أرصدة العملاء: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnSupplierBalanceReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var report = await _reportService.GetSupplierBalanceReportAsync(_currentCompany.CompanyId);
                ShowSupplierBalanceReport(report);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير أرصدة الموردين: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnInventoryReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var report = await _reportService.GetInventoryReportAsync(_currentCompany.CompanyId);
                ShowInventoryReport(report);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المخزون: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnInventoryMovementReport_Click(object? sender, EventArgs e)
        {
            try
            {
                var dateForm = new DateRangeForm();
                if (dateForm.ShowDialog() == DialogResult.OK)
                {
                    var report = await _reportService.GetInventoryMovementReportAsync(
                        _currentCompany.CompanyId, 
                        null, // جميع الأصناف
                        dateForm.DateFrom, 
                        dateForm.DateTo);

                    ShowInventoryMovementReport(report);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير حركة المخزون: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowSalesReport(SalesReport report)
        {
            var reportForm = new ReportViewerForm($"تقرير المبيعات من {report.DateFrom:yyyy/MM/dd} إلى {report.DateTo:yyyy/MM/dd}");
            
            var content = $@"
=== تقرير المبيعات ===
الفترة: من {report.DateFrom:yyyy/MM/dd} إلى {report.DateTo:yyyy/MM/dd}

الملخص العام:
- إجمالي الفواتير: {report.TotalInvoices}
- إجمالي المبيعات: {report.TotalSales:N2} {_currentCompany.Currency}
- إجمالي الضريبة: {report.TotalTax:N2} {_currentCompany.Currency}
- إجمالي الخصم: {report.TotalDiscount:N2} {_currentCompany.Currency}
- إجمالي الكمية: {report.TotalQuantity:N2}

أفضل العملاء:
{string.Join("\n", report.TopCustomers.Take(5).Select(c => $"- {c.CustomerName}: {c.TotalSales:N2} {_currentCompany.Currency} ({c.InvoiceCount} فاتورة)"))}

أفضل الأصناف:
{string.Join("\n", report.TopProducts.Take(5).Select(p => $"- {p.ProductName}: {p.TotalSales:N2} {_currentCompany.Currency} ({p.TotalQuantity:N2} وحدة)"))}
";

            reportForm.SetContent(content);
            reportForm.Show();
        }

        private void ShowPurchaseReport(PurchaseReport report)
        {
            var reportForm = new ReportViewerForm($"تقرير المشتريات من {report.DateFrom:yyyy/MM/dd} إلى {report.DateTo:yyyy/MM/dd}");
            
            var content = $@"
=== تقرير المشتريات ===
الفترة: من {report.DateFrom:yyyy/MM/dd} إلى {report.DateTo:yyyy/MM/dd}

الملخص العام:
- إجمالي الفواتير: {report.TotalInvoices}
- إجمالي المشتريات: {report.TotalPurchases:N2} {_currentCompany.Currency}
- إجمالي الضريبة: {report.TotalTax:N2} {_currentCompany.Currency}
- إجمالي الخصم: {report.TotalDiscount:N2} {_currentCompany.Currency}
- إجمالي الكمية: {report.TotalQuantity:N2}

أفضل الموردين:
{string.Join("\n", report.TopSuppliers.Take(5).Select(s => $"- {s.SupplierName}: {s.TotalPurchases:N2} {_currentCompany.Currency} ({s.InvoiceCount} فاتورة)"))}

أكثر الأصناف شراءً:
{string.Join("\n", report.TopProducts.Take(5).Select(p => $"- {p.ProductName}: {p.TotalPurchases:N2} {_currentCompany.Currency} ({p.TotalQuantity:N2} وحدة)"))}
";

            reportForm.SetContent(content);
            reportForm.Show();
        }

        private void ShowCustomerBalanceReport(CustomerBalanceReport report)
        {
            var reportForm = new ReportViewerForm("تقرير أرصدة العملاء");
            
            var content = $@"
=== تقرير أرصدة العملاء ===
تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}

الملخص العام:
- إجمالي الأرصدة: {report.TotalBalance:N2} {_currentCompany.Currency}
- عدد العملاء ذوي الأرصدة: {report.CustomersWithBalance}
- عدد العملاء المتجاوزين للحد الائتماني: {report.CustomersOverLimit}

تفاصيل الأرصدة:
{string.Join("\n", report.Customers.Where(c => c.CurrentBalance > 0).Take(20).Select(c => $"- {c.CustomerName} ({c.CustomerCode}): {c.CurrentBalance:N2} {_currentCompany.Currency}"))}
";

            reportForm.SetContent(content);
            reportForm.Show();
        }

        private void ShowSupplierBalanceReport(SupplierBalanceReport report)
        {
            var reportForm = new ReportViewerForm("تقرير أرصدة الموردين");
            
            var content = $@"
=== تقرير أرصدة الموردين ===
تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}

الملخص العام:
- إجمالي الأرصدة: {report.TotalBalance:N2} {_currentCompany.Currency}
- عدد الموردين ذوي الأرصدة: {report.SuppliersWithBalance}
- عدد الموردين المتجاوزين للحد الائتماني: {report.SuppliersOverLimit}

تفاصيل الأرصدة:
{string.Join("\n", report.Suppliers.Where(s => s.CurrentBalance > 0).Take(20).Select(s => $"- {s.SupplierName} ({s.SupplierCode}): {s.CurrentBalance:N2} {_currentCompany.Currency}"))}
";

            reportForm.SetContent(content);
            reportForm.Show();
        }

        private void ShowInventoryReport(InventoryReport report)
        {
            var reportForm = new ReportViewerForm("تقرير المخزون");
            
            var content = $@"
=== تقرير المخزون ===
تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}

الملخص العام:
- إجمالي الأصناف: {report.TotalProducts}
- الأصناف ذات المخزون المنخفض: {report.LowStockProducts}
- الأصناف المنتهية من المخزن: {report.OutOfStockProducts}
- إجمالي قيمة المخزون: {report.TotalInventoryValue:N2} {_currentCompany.Currency}

الأصناف ذات المخزون المنخفض:
{string.Join("\n", report.Products.Where(p => p.CurrentStock <= p.MinimumStock).Take(20).Select(p => $"- {p.ProductName}: {p.CurrentStock:N2} {p.UnitName} (الحد الأدنى: {p.MinimumStock:N2})"))}
";

            reportForm.SetContent(content);
            reportForm.Show();
        }

        private void ShowInventoryMovementReport(InventoryMovementReport report)
        {
            var reportForm = new ReportViewerForm($"تقرير حركة المخزون من {report.DateFrom:yyyy/MM/dd} إلى {report.DateTo:yyyy/MM/dd}");
            
            var content = $@"
=== تقرير حركة المخزون ===
الفترة: من {report.DateFrom:yyyy/MM/dd} إلى {report.DateTo:yyyy/MM/dd}

الملخص العام:
- إجمالي الكمية الداخلة: {report.TotalQuantityIn:N2}
- إجمالي الكمية الخارجة: {report.TotalQuantityOut:N2}
- إجمالي قيمة الداخل: {report.TotalValueIn:N2} {_currentCompany.Currency}
- إجمالي قيمة الخارج: {report.TotalValueOut:N2} {_currentCompany.Currency}

آخر الحركات:
{string.Join("\n", report.Movements.TakeLast(20).Select(m => $"- {m.Date:yyyy/MM/dd} | {m.ProductName} | {m.MovementType} | داخل: {m.QuantityIn:N2} | خارج: {m.QuantityOut:N2}"))}
";

            reportForm.SetContent(content);
            reportForm.Show();
        }

        #region Event Handlers

        private void btnSalesReport_Click(object sender, EventArgs e)
        {
            try
            {
                ShowSalesReportDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تقرير المبيعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnPurchaseReport_Click(object sender, EventArgs e)
        {
            try
            {
                ShowPurchaseReportDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تقرير المشتريات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnInventoryReport_Click(object sender, EventArgs e)
        {
            try
            {
                ShowInventoryReportDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تقرير المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnFinancialReport_Click(object sender, EventArgs e)
        {
            try
            {
                ShowFinancialReportDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التقرير المالي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض حوار تقرير المبيعات
        /// </summary>
        private async void ShowSalesReportDialog()
        {
            try
            {
                var dateForm = new DateRangeForm();
                if (dateForm.ShowDialog() == DialogResult.OK)
                {
                    var report = await _reportService.GetSalesReportAsync(
                        _currentCompany.CompanyId,
                        dateForm.DateFrom,
                        dateForm.DateTo);

                    ShowSalesReport(report);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المبيعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض حوار تقرير المشتريات
        /// </summary>
        private async void ShowPurchaseReportDialog()
        {
            try
            {
                var dateForm = new DateRangeForm();
                if (dateForm.ShowDialog() == DialogResult.OK)
                {
                    var report = await _reportService.GetPurchaseReportAsync(
                        _currentCompany.CompanyId,
                        dateForm.DateFrom,
                        dateForm.DateTo);

                    ShowPurchaseReport(report);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المشتريات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض حوار تقرير المخزون
        /// </summary>
        private async void ShowInventoryReportDialog()
        {
            try
            {
                var report = await _reportService.GetInventoryReportAsync(_currentCompany.CompanyId);
                ShowInventoryReport(report);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض حوار التقرير المالي
        /// </summary>
        private async void ShowFinancialReportDialog()
        {
            try
            {
                var dateForm = new DateRangeForm();
                if (dateForm.ShowDialog() == DialogResult.OK)
                {
                    var report = await _reportService.GetFinancialReportAsync(
                        _currentCompany.CompanyId,
                        dateForm.DateFrom,
                        dateForm.DateTo);

                    ShowFinancialReport(report);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير المالي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض التقرير المالي
        /// </summary>
        private void ShowFinancialReport(FinancialReport report)
        {
            var reportForm = new ReportViewerForm($"التقرير المالي من {report.DateFrom:yyyy/MM/dd} إلى {report.DateTo:yyyy/MM/dd}");

            var content = $@"
=== التقرير المالي ===
الفترة: من {report.DateFrom:yyyy/MM/dd} إلى {report.DateTo:yyyy/MM/dd}

الملخص المالي:
- إجمالي الإيرادات: {report.TotalRevenue:N2} {_currentCompany.Currency}
- إجمالي المصروفات: {report.TotalExpenses:N2} {_currentCompany.Currency}
- صافي الربح: {report.NetProfit:N2} {_currentCompany.Currency}
- هامش الربح: {report.ProfitMargin:P2}

تفاصيل الإيرادات:
- مبيعات نقدية: {report.CashSales:N2} {_currentCompany.Currency}
- مبيعات آجلة: {report.CreditSales:N2} {_currentCompany.Currency}
- إيرادات أخرى: {report.OtherRevenue:N2} {_currentCompany.Currency}

تفاصيل المصروفات:
- تكلفة البضاعة المباعة: {report.CostOfGoodsSold:N2} {_currentCompany.Currency}
- مصروفات تشغيلية: {report.OperatingExpenses:N2} {_currentCompany.Currency}
- مصروفات أخرى: {report.OtherExpenses:N2} {_currentCompany.Currency}
";

            reportForm.SetContent(content);
            reportForm.Show();
        }

        #endregion
    }

}
