using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة القيود المحاسبية
    /// Journal Entry Management Service
    /// </summary>
    public class JournalEntryService
    {
        private readonly string _connectionString;
        private readonly AccountService _accountService;

        public JournalEntryService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
            _accountService = new AccountService();
        }

        /// <summary>
        /// الحصول على جميع القيود للشركة
        /// Get all journal entries for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة القيود</returns>
        public async Task<List<JournalEntry>> GetAllJournalEntriesAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.JournalEntries
                .Where(je => je.CompanyId == companyId && je.IsActive)
                .Include(je => je.User)
                .Include(je => je.JournalEntryDetails)
                    .ThenInclude(jed => jed.Account)
                .OrderByDescending(je => je.EntryDate)
                .ThenByDescending(je => je.EntryNumber)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على قيد بالمعرف
        /// Get journal entry by ID
        /// </summary>
        /// <param name="entryId">معرف القيد</param>
        /// <returns>بيانات القيد</returns>
        public async Task<JournalEntry?> GetJournalEntryByIdAsync(int entryId)
        {
            using var context = CreateDbContext();
            return await context.JournalEntries
                .Include(je => je.User)
                .Include(je => je.Company)
                .Include(je => je.JournalEntryDetails)
                    .ThenInclude(jed => jed.Account)
                .FirstOrDefaultAsync(je => je.JournalEntryId == entryId && je.IsActive);
        }

        /// <summary>
        /// البحث في القيود
        /// Search journal entries
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <param name="dateFrom">من تاريخ</param>
        /// <param name="dateTo">إلى تاريخ</param>
        /// <returns>قائمة القيود المطابقة</returns>
        public async Task<List<JournalEntry>> SearchJournalEntriesAsync(string searchTerm, int companyId, 
            DateTime? dateFrom = null, DateTime? dateTo = null)
        {
            using var context = CreateDbContext();
            
            var query = context.JournalEntries
                .Where(je => je.CompanyId == companyId && je.IsActive)
                .Include(je => je.User)
                .Include(je => je.JournalEntryDetails)
                    .ThenInclude(jed => jed.Account)
                .AsQueryable();

            // تطبيق فلتر التاريخ
            if (dateFrom.HasValue)
                query = query.Where(je => je.EntryDate >= dateFrom.Value);
            
            if (dateTo.HasValue)
                query = query.Where(je => je.EntryDate <= dateTo.Value);

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.Trim().ToLower();
                query = query.Where(je => 
                    je.EntryNumber.ToLower().Contains(searchTerm) ||
                    (je.Description != null && je.Description.ToLower().Contains(searchTerm)) ||
                    (je.Reference != null && je.Reference.ToLower().Contains(searchTerm)));
            }

            return await query
                .OrderByDescending(je => je.EntryDate)
                .ThenByDescending(je => je.EntryNumber)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة قيد محاسبي جديد
        /// Add new journal entry
        /// </summary>
        /// <param name="journalEntry">بيانات القيد</param>
        /// <returns>معرف القيد الجديد</returns>
        public async Task<int> AddJournalEntryAsync(JournalEntry journalEntry)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // التحقق من توازن القيد
                var totalDebit = journalEntry.JournalEntryDetails.Sum(jed => jed.DebitAmount);
                var totalCredit = journalEntry.JournalEntryDetails.Sum(jed => jed.CreditAmount);

                if (totalDebit != totalCredit)
                    throw new InvalidOperationException($"القيد غير متوازن. إجمالي المدين: {totalDebit:N2}، إجمالي الدائن: {totalCredit:N2}");

                // إنشاء رقم قيد تلقائي إذا لم يتم تحديده
                if (string.IsNullOrEmpty(journalEntry.EntryNumber))
                {
                    journalEntry.EntryNumber = await GenerateEntryNumberAsync(journalEntry.CompanyId);
                }

                // التحقق من عدم تكرار رقم القيد
                bool numberExists = await context.JournalEntries
                    .AnyAsync(je => je.EntryNumber == journalEntry.EntryNumber && 
                                   je.CompanyId == journalEntry.CompanyId);
                
                if (numberExists)
                    throw new InvalidOperationException($"رقم القيد '{journalEntry.EntryNumber}' موجود مسبقاً");

                // التحقق من صحة الحسابات
                foreach (var detail in journalEntry.JournalEntryDetails)
                {
                    var account = await context.Accounts
                        .FirstOrDefaultAsync(a => a.AccountId == detail.AccountId && 
                                                 a.CompanyId == journalEntry.CompanyId && a.IsActive);
                    
                    if (account == null)
                        throw new InvalidOperationException($"الحساب غير موجود أو غير نشط");

                    if (!account.IsFinalAccount)
                        throw new InvalidOperationException($"لا يمكن إجراء قيود على الحساب '{account.AccountName}' لأنه ليس حساب نهائي");
                }

                journalEntry.CreatedDate = DateTime.Now;
                journalEntry.IsActive = true;
                journalEntry.TotalAmount = totalDebit; // أو totalCredit (نفس القيمة)

                // إضافة القيد
                context.JournalEntries.Add(journalEntry);
                await context.SaveChangesAsync();

                // تحديث أرصدة الحسابات
                await UpdateAccountBalancesAsync(context, journalEntry.JournalEntryDetails);

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return journalEntry.JournalEntryId;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// تحديث قيد محاسبي
        /// Update journal entry
        /// </summary>
        /// <param name="journalEntry">بيانات القيد المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateJournalEntryAsync(JournalEntry journalEntry)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var existingEntry = await context.JournalEntries
                    .Include(je => je.JournalEntryDetails)
                    .FirstOrDefaultAsync(je => je.JournalEntryId == journalEntry.JournalEntryId);

                if (existingEntry == null)
                    throw new InvalidOperationException("القيد غير موجود");

                // التحقق من توازن القيد الجديد
                var totalDebit = journalEntry.JournalEntryDetails.Sum(jed => jed.DebitAmount);
                var totalCredit = journalEntry.JournalEntryDetails.Sum(jed => jed.CreditAmount);

                if (totalDebit != totalCredit)
                    throw new InvalidOperationException($"القيد غير متوازن. إجمالي المدين: {totalDebit:N2}، إجمالي الدائن: {totalCredit:N2}");

                // عكس تأثير القيد القديم على أرصدة الحسابات
                await ReverseAccountBalancesAsync(context, existingEntry.JournalEntryDetails);

                // حذف التفاصيل القديمة
                context.JournalEntryDetails.RemoveRange(existingEntry.JournalEntryDetails);

                // تحديث بيانات القيد
                existingEntry.EntryNumber = journalEntry.EntryNumber;
                existingEntry.EntryDate = journalEntry.EntryDate;
                existingEntry.Description = journalEntry.Description;
                existingEntry.Reference = journalEntry.Reference;
                existingEntry.TotalAmount = totalDebit;
                existingEntry.ModifiedDate = DateTime.Now;
                existingEntry.ModifiedBy = journalEntry.ModifiedBy;

                // إضافة التفاصيل الجديدة
                foreach (var detail in journalEntry.JournalEntryDetails)
                {
                    detail.JournalEntryId = existingEntry.JournalEntryId;
                    existingEntry.JournalEntryDetails.Add(detail);
                }

                // تطبيق تأثير القيد الجديد على أرصدة الحسابات
                await UpdateAccountBalancesAsync(context, existingEntry.JournalEntryDetails);

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// حذف قيد محاسبي (حذف منطقي)
        /// Delete journal entry (soft delete)
        /// </summary>
        /// <param name="entryId">معرف القيد</param>
        /// <param name="deletedBy">اسم المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteJournalEntryAsync(int entryId, string deletedBy)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var entry = await context.JournalEntries
                    .Include(je => je.JournalEntryDetails)
                    .FirstOrDefaultAsync(je => je.JournalEntryId == entryId);

                if (entry == null)
                    throw new InvalidOperationException("القيد غير موجود");

                // عكس تأثير القيد على أرصدة الحسابات
                await ReverseAccountBalancesAsync(context, entry.JournalEntryDetails);

                // حذف منطقي
                entry.IsActive = false;
                entry.ModifiedDate = DateTime.Now;
                entry.ModifiedBy = deletedBy;

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// إنشاء قيد تلقائي لفاتورة مبيعات
        /// Create automatic journal entry for sales invoice
        /// </summary>
        /// <param name="salesInvoice">فاتورة المبيعات</param>
        /// <param name="createdBy">معرف المستخدم</param>
        /// <returns>معرف القيد المنشأ</returns>
        public async Task<int> CreateSalesInvoiceJournalEntryAsync(SalesInvoice salesInvoice, string createdBy)
        {
            using var context = CreateDbContext();

            // الحصول على الحسابات المطلوبة
            var customerAccount = await GetAccountByCodeAsync("113", salesInvoice.CompanyId); // العملاء
            var salesAccount = await GetAccountByCodeAsync("41", salesInvoice.CompanyId); // إيرادات المبيعات
            var taxAccount = await GetAccountByCodeAsync("212", salesInvoice.CompanyId); // الضرائب المستحقة

            var journalEntry = new JournalEntry
            {
                EntryNumber = await GenerateEntryNumberAsync(salesInvoice.CompanyId),
                EntryDate = salesInvoice.InvoiceDate,
                Description = $"قيد فاتورة مبيعات رقم {salesInvoice.InvoiceNumber}",
                Reference = salesInvoice.InvoiceNumber,
                CompanyId = salesInvoice.CompanyId,
                CreatedBy = createdBy,
                JournalEntryDetails = new List<JournalEntryDetail>()
            };

            // مدين: العملاء (إجمالي الفاتورة)
            journalEntry.JournalEntryDetails.Add(new JournalEntryDetail
            {
                AccountId = customerAccount.AccountId,
                Description = $"فاتورة مبيعات رقم {salesInvoice.InvoiceNumber}",
                DebitAmount = salesInvoice.TotalAmount,
                CreditAmount = 0
            });

            // دائن: إيرادات المبيعات (المجموع الفرعي + الخصم)
            journalEntry.JournalEntryDetails.Add(new JournalEntryDetail
            {
                AccountId = salesAccount.AccountId,
                Description = $"إيرادات مبيعات فاتورة رقم {salesInvoice.InvoiceNumber}",
                DebitAmount = 0,
                CreditAmount = salesInvoice.Subtotal - salesInvoice.DiscountAmount
            });

            // دائن: الضرائب المستحقة (إذا وجدت)
            if (salesInvoice.TaxAmount > 0)
            {
                journalEntry.JournalEntryDetails.Add(new JournalEntryDetail
                {
                    AccountId = taxAccount.AccountId,
                    Description = $"ضريبة فاتورة مبيعات رقم {salesInvoice.InvoiceNumber}",
                    DebitAmount = 0,
                    CreditAmount = salesInvoice.TaxAmount
                });
            }

            return await AddJournalEntryAsync(journalEntry);
        }

        /// <summary>
        /// إنشاء قيد تلقائي لفاتورة مشتريات
        /// Create automatic journal entry for purchase invoice
        /// </summary>
        /// <param name="purchaseInvoice">فاتورة المشتريات</param>
        /// <param name="createdBy">معرف المستخدم</param>
        /// <returns>معرف القيد المنشأ</returns>
        public async Task<int> CreatePurchaseInvoiceJournalEntryAsync(PurchaseInvoice purchaseInvoice, string createdBy)
        {
            using var context = CreateDbContext();

            // الحصول على الحسابات المطلوبة
            var supplierAccount = await GetAccountByCodeAsync("211", purchaseInvoice.CompanyId); // الموردون
            var inventoryAccount = await GetAccountByCodeAsync("114", purchaseInvoice.CompanyId); // المخزون
            var taxAccount = await GetAccountByCodeAsync("212", purchaseInvoice.CompanyId); // الضرائب المستحقة

            var journalEntry = new JournalEntry
            {
                EntryNumber = await GenerateEntryNumberAsync(purchaseInvoice.CompanyId),
                EntryDate = purchaseInvoice.InvoiceDate,
                Description = $"قيد فاتورة مشتريات رقم {purchaseInvoice.InvoiceNumber}",
                Reference = purchaseInvoice.InvoiceNumber,
                CompanyId = purchaseInvoice.CompanyId,
                CreatedBy = createdBy,
                JournalEntryDetails = new List<JournalEntryDetail>()
            };

            // مدين: المخزون (المجموع الفرعي - الخصم)
            journalEntry.JournalEntryDetails.Add(new JournalEntryDetail
            {
                AccountId = inventoryAccount.AccountId,
                Description = $"مشتريات فاتورة رقم {purchaseInvoice.InvoiceNumber}",
                DebitAmount = purchaseInvoice.Subtotal - purchaseInvoice.DiscountAmount,
                CreditAmount = 0
            });

            // مدين: الضرائب (إذا وجدت)
            if (purchaseInvoice.TaxAmount > 0)
            {
                journalEntry.JournalEntryDetails.Add(new JournalEntryDetail
                {
                    AccountId = taxAccount.AccountId,
                    Description = $"ضريبة فاتورة مشتريات رقم {purchaseInvoice.InvoiceNumber}",
                    DebitAmount = purchaseInvoice.TaxAmount,
                    CreditAmount = 0
                });
            }

            // دائن: الموردون (إجمالي الفاتورة)
            journalEntry.JournalEntryDetails.Add(new JournalEntryDetail
            {
                AccountId = supplierAccount.AccountId,
                Description = $"فاتورة مشتريات رقم {purchaseInvoice.InvoiceNumber}",
                DebitAmount = 0,
                CreditAmount = purchaseInvoice.TotalAmount
            });

            return await AddJournalEntryAsync(journalEntry);
        }

        /// <summary>
        /// إنشاء رقم قيد تلقائي
        /// Generate automatic entry number
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>رقم القيد الجديد</returns>
        public async Task<string> GenerateEntryNumberAsync(int companyId)
        {
            using var context = CreateDbContext();

            var currentYear = DateTime.Now.Year;
            var lastEntry = await context.JournalEntries
                .Where(je => je.CompanyId == companyId && 
                            je.EntryNumber.StartsWith($"JE{currentYear}") &&
                            je.EntryDate.Year == currentYear)
                .OrderByDescending(je => je.EntryNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastEntry != null)
            {
                string numberPart = lastEntry.EntryNumber.Substring($"JE{currentYear}".Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"JE{currentYear}{nextNumber:000000}";
        }

        private async Task<Account> GetAccountByCodeAsync(string accountCode, int companyId)
        {
            using var context = CreateDbContext();
            var account = await context.Accounts
                .FirstOrDefaultAsync(a => a.AccountCode == accountCode && 
                                         a.CompanyId == companyId && a.IsActive);
            
            if (account == null)
                throw new InvalidOperationException($"الحساب بالكود '{accountCode}' غير موجود");

            return account;
        }

        private async Task UpdateAccountBalancesAsync(JoudDbContext context, ICollection<JournalEntryDetail> details)
        {
            foreach (var detail in details)
            {
                var account = await context.Accounts
                    .FirstOrDefaultAsync(a => a.AccountId == detail.AccountId);
                
                if (account != null)
                {
                    account.CurrentBalance += (detail.DebitAmount - detail.CreditAmount);
                }
            }
        }

        private async Task ReverseAccountBalancesAsync(JoudDbContext context, ICollection<JournalEntryDetail> details)
        {
            foreach (var detail in details)
            {
                var account = await context.Accounts
                    .FirstOrDefaultAsync(a => a.AccountId == detail.AccountId);
                
                if (account != null)
                {
                    account.CurrentBalance -= (detail.DebitAmount - detail.CreditAmount);
                }
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات القيد
        /// Validate journal entry data
        /// </summary>
        /// <param name="journalEntry">بيانات القيد</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateJournalEntry(JournalEntry journalEntry)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(journalEntry.EntryNumber))
                errors.Add("رقم القيد مطلوب");

            if (journalEntry.EntryDate == default)
                errors.Add("تاريخ القيد مطلوب");

            if (journalEntry.JournalEntryDetails == null || !journalEntry.JournalEntryDetails.Any())
                errors.Add("يجب إضافة تفصيل واحد على الأقل للقيد");

            if (journalEntry.JournalEntryDetails != null)
            {
                var totalDebit = journalEntry.JournalEntryDetails.Sum(jed => jed.DebitAmount);
                var totalCredit = journalEntry.JournalEntryDetails.Sum(jed => jed.CreditAmount);

                if (totalDebit != totalCredit)
                    errors.Add($"القيد غير متوازن. إجمالي المدين: {totalDebit:N2}، إجمالي الدائن: {totalCredit:N2}");

                foreach (var detail in journalEntry.JournalEntryDetails)
                {
                    if (detail.AccountId <= 0)
                        errors.Add("يجب اختيار الحساب لجميع التفاصيل");

                    if (detail.DebitAmount == 0 && detail.CreditAmount == 0)
                        errors.Add("يجب إدخال مبلغ مدين أو دائن لكل تفصيل");

                    if (detail.DebitAmount > 0 && detail.CreditAmount > 0)
                        errors.Add("لا يمكن أن يكون التفصيل مدين ودائن في نفس الوقت");
                }
            }

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }
}
