using System.Security.Cryptography;
using System.Text;

namespace Joud.Utilities
{
    /// <summary>
    /// فئة مساعدة لتشفير كلمات المرور
    /// Password Helper Class for Encryption
    /// </summary>
    public static class PasswordHelper
    {
        /// <summary>
        /// إنشاء مفتاح تشفير عشوائي
        /// Generate Random Salt
        /// </summary>
        /// <returns>مفتاح التشفير</returns>
        public static string GenerateSalt()
        {
            byte[] saltBytes = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            return Convert.ToBase64String(saltBytes);
        }

        /// <summary>
        /// تشفير كلمة المرور باستخدام مفتاح التشفير
        /// Hash Password with Salt
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="salt">مفتاح التشفير</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public static string HashPassword(string password, string salt)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("كلمة المرور لا يمكن أن تكون فارغة", nameof(password));

            if (string.IsNullOrEmpty(salt))
                throw new ArgumentException("مفتاح التشفير لا يمكن أن يكون فارغاً", nameof(salt));

            using (var sha256 = SHA256.Create())
            {
                // دمج كلمة المرور مع مفتاح التشفير
                string saltedPassword = password + salt;
                byte[] saltedPasswordBytes = Encoding.UTF8.GetBytes(saltedPassword);
                
                // تشفير كلمة المرور
                byte[] hashBytes = sha256.ComputeHash(saltedPasswordBytes);
                
                return Convert.ToBase64String(hashBytes);
            }
        }

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// Verify Password
        /// </summary>
        /// <param name="password">كلمة المرور المدخلة</param>
        /// <param name="salt">مفتاح التشفير المحفوظ</param>
        /// <param name="hashedPassword">كلمة المرور المشفرة المحفوظة</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة</returns>
        public static bool VerifyPassword(string password, string salt, string hashedPassword)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(salt) || string.IsNullOrEmpty(hashedPassword))
                return false;

            try
            {
                string newHashedPassword = HashPassword(password, salt);
                return newHashedPassword == hashedPassword;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء كلمة مرور وتشفيرها
        /// Create and Hash Password
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة ومفتاح التشفير</returns>
        public static (string hashedPassword, string salt) CreateHashedPassword(string password)
        {
            string salt = GenerateSalt();
            string hashedPassword = HashPassword(password, salt);
            return (hashedPassword, salt);
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// Validate Password Strength
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>رسالة التحقق</returns>
        public static (bool isValid, string message) ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return (false, "كلمة المرور مطلوبة");

            if (password.Length < 6)
                return (false, "كلمة المرور يجب أن تكون 6 أحرف على الأقل");

            if (password.Length > 50)
                return (false, "كلمة المرور يجب أن تكون أقل من 50 حرف");

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);

            if (!hasUpper)
                return (false, "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل");

            if (!hasLower)
                return (false, "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل");

            if (!hasDigit)
                return (false, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل");

            return (true, "كلمة المرور قوية");
        }
    }
}
