using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات تفاصيل فواتير المشتريات
    /// Purchase Invoice Detail Data Model
    /// </summary>
    [Table("PurchaseInvoiceDetails")]
    public class PurchaseInvoiceDetail
    {
        [Key]
        public int PurchaseInvoiceDetailId { get; set; }

        [Required]
        [Display(Name = "فاتورة المشتريات")]
        public int PurchaseInvoiceId { get; set; }

        [Required]
        [Display(Name = "الصنف")]
        public int ProductId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الكمية")]
        public decimal Quantity { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "سعر الوحدة")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ الخصم")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة الخصم")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ الضريبة")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة الضريبة")]
        public decimal TaxPercentage { get; set; } = 0;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ الإجمالي")]
        public decimal TotalAmount { get; set; }

        [Display(Name = "تاريخ الانتهاء")]
        public DateTime? ExpiryDate { get; set; }

        [StringLength(50)]
        [Display(Name = "رقم الدفعة")]
        public string? BatchNumber { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
