using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة الحسابات المحاسبية
    /// Accounting Accounts Management Service
    /// </summary>
    public class AccountService
    {
        private readonly string _connectionString;

        public AccountService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// الحصول على جميع الحسابات للشركة
        /// Get all accounts for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الحسابات</returns>
        public async Task<List<Account>> GetAllAccountsAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.Accounts
                .Where(a => a.CompanyId == companyId && a.IsActive)
                .Include(a => a.ParentAccount)
                .OrderBy(a => a.AccountCode)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// Get account by ID
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>بيانات الحساب</returns>
        public async Task<Account?> GetAccountByIdAsync(int accountId)
        {
            using var context = CreateDbContext();
            return await context.Accounts
                .Include(a => a.ParentAccount)
                .Include(a => a.SubAccounts)
                .FirstOrDefaultAsync(a => a.AccountId == accountId && a.IsActive);
        }

        /// <summary>
        /// الحصول على حساب بالكود
        /// Get account by code
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات الحساب</returns>
        public async Task<Account?> GetAccountByCodeAsync(string accountCode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.Accounts
                .FirstOrDefaultAsync(a => a.AccountCode == accountCode && 
                                         a.CompanyId == companyId && a.IsActive);
        }

        /// <summary>
        /// البحث في الحسابات
        /// Search accounts
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الحسابات المطابقة</returns>
        public async Task<List<Account>> SearchAccountsAsync(string searchTerm, int companyId)
        {
            using var context = CreateDbContext();
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllAccountsAsync(companyId);

            searchTerm = searchTerm.Trim().ToLower();
            
            return await context.Accounts
                .Where(a => a.CompanyId == companyId && a.IsActive &&
                           (a.AccountCode.ToLower().Contains(searchTerm) ||
                            a.AccountName.ToLower().Contains(searchTerm) ||
                            (a.Description != null && a.Description.ToLower().Contains(searchTerm))))
                .Include(a => a.ParentAccount)
                .OrderBy(a => a.AccountCode)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة حساب جديد
        /// Add new account
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>معرف الحساب الجديد</returns>
        public async Task<int> AddAccountAsync(Account account)
        {
            using var context = CreateDbContext();

            // التحقق من عدم تكرار كود الحساب
            bool codeExists = await context.Accounts
                .AnyAsync(a => a.AccountCode == account.AccountCode && 
                              a.CompanyId == account.CompanyId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود الحساب '{account.AccountCode}' موجود مسبقاً");

            // التحقق من صحة الحساب الأب
            if (account.ParentAccountId.HasValue)
            {
                var parentAccount = await context.Accounts
                    .FirstOrDefaultAsync(a => a.AccountId == account.ParentAccountId.Value && 
                                             a.CompanyId == account.CompanyId && a.IsActive);
                
                if (parentAccount == null)
                    throw new InvalidOperationException("الحساب الأب غير موجود أو غير نشط");

                // التحقق من أن الحساب الأب ليس حساب فرعي نهائي
                if (parentAccount.IsFinalAccount)
                    throw new InvalidOperationException("لا يمكن إضافة حسابات فرعية تحت حساب نهائي");
            }

            account.CreatedDate = DateTime.Now;
            account.IsActive = true;
            account.CurrentBalance = 0;

            context.Accounts.Add(account);
            await context.SaveChangesAsync();

            return account.AccountId;
        }

        /// <summary>
        /// تحديث حساب
        /// Update account
        /// </summary>
        /// <param name="account">بيانات الحساب المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateAccountAsync(Account account)
        {
            using var context = CreateDbContext();

            var existingAccount = await context.Accounts
                .FirstOrDefaultAsync(a => a.AccountId == account.AccountId);

            if (existingAccount == null)
                throw new InvalidOperationException("الحساب غير موجود");

            // التحقق من عدم تكرار كود الحساب
            bool codeExists = await context.Accounts
                .AnyAsync(a => a.AccountCode == account.AccountCode && 
                              a.CompanyId == account.CompanyId && 
                              a.AccountId != account.AccountId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود الحساب '{account.AccountCode}' موجود مسبقاً");

            // تحديث البيانات
            existingAccount.AccountCode = account.AccountCode;
            existingAccount.AccountName = account.AccountName;
            existingAccount.AccountType = account.AccountType;
            existingAccount.ParentAccountId = account.ParentAccountId;
            existingAccount.IsFinalAccount = account.IsFinalAccount;
            existingAccount.Description = account.Description;
            existingAccount.ModifiedDate = DateTime.Now;
            existingAccount.ModifiedBy = account.ModifiedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف حساب (حذف منطقي)
        /// Delete account (soft delete)
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="deletedBy">اسم المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteAccountAsync(int accountId, string deletedBy)
        {
            using var context = CreateDbContext();

            var account = await context.Accounts
                .Include(a => a.SubAccounts)
                .FirstOrDefaultAsync(a => a.AccountId == accountId);

            if (account == null)
                throw new InvalidOperationException("الحساب غير موجود");

            // التحقق من عدم وجود حسابات فرعية
            if (account.SubAccounts.Any(sa => sa.IsActive))
                throw new InvalidOperationException("لا يمكن حذف حساب يحتوي على حسابات فرعية");

            // التحقق من عدم وجود قيود على الحساب
            bool hasJournalEntries = await context.JournalEntryDetails
                .AnyAsync(jed => jed.AccountId == accountId);

            if (hasJournalEntries)
                throw new InvalidOperationException("لا يمكن حذف حساب يحتوي على قيود محاسبية");

            // حذف منطقي
            account.IsActive = false;
            account.ModifiedDate = DateTime.Now;
            account.ModifiedBy = deletedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// إنشاء دليل الحسابات الافتراضي
        /// Create default chart of accounts
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <param name="createdBy">معرف المستخدم</param>
        /// <returns>عدد الحسابات المنشأة</returns>
        public async Task<int> CreateDefaultChartOfAccountsAsync(int companyId, string createdBy)
        {
            using var context = CreateDbContext();

            // التحقق من عدم وجود حسابات مسبقاً
            bool hasAccounts = await context.Accounts
                .AnyAsync(a => a.CompanyId == companyId);

            if (hasAccounts)
                throw new InvalidOperationException("دليل الحسابات موجود مسبقاً لهذه الشركة");

            var accounts = new List<Account>
            {
                // الأصول
                new Account { AccountCode = "1", AccountName = "الأصول", AccountType = AccountType.Asset, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = false },
                new Account { AccountCode = "11", AccountName = "الأصول المتداولة", AccountType = AccountType.Asset, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = false },
                new Account { AccountCode = "111", AccountName = "النقدية", AccountType = AccountType.Asset, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },
                new Account { AccountCode = "112", AccountName = "البنوك", AccountType = AccountType.Asset, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },
                new Account { AccountCode = "113", AccountName = "العملاء", AccountType = AccountType.Asset, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },
                new Account { AccountCode = "114", AccountName = "المخزون", AccountType = AccountType.Asset, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },

                // الخصوم
                new Account { AccountCode = "2", AccountName = "الخصوم", AccountType = AccountType.Liability, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = false },
                new Account { AccountCode = "21", AccountName = "الخصوم المتداولة", AccountType = AccountType.Liability, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = false },
                new Account { AccountCode = "211", AccountName = "الموردون", AccountType = AccountType.Liability, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },
                new Account { AccountCode = "212", AccountName = "الضرائب المستحقة", AccountType = AccountType.Liability, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },

                // حقوق الملكية
                new Account { AccountCode = "3", AccountName = "حقوق الملكية", AccountType = AccountType.Equity, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = false },
                new Account { AccountCode = "31", AccountName = "رأس المال", AccountType = AccountType.Equity, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },
                new Account { AccountCode = "32", AccountName = "الأرباح المحتجزة", AccountType = AccountType.Equity, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },

                // الإيرادات
                new Account { AccountCode = "4", AccountName = "الإيرادات", AccountType = AccountType.Revenue, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = false },
                new Account { AccountCode = "41", AccountName = "إيرادات المبيعات", AccountType = AccountType.Revenue, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },
                new Account { AccountCode = "42", AccountName = "إيرادات أخرى", AccountType = AccountType.Revenue, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },

                // المصروفات
                new Account { AccountCode = "5", AccountName = "المصروفات", AccountType = AccountType.Expense, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = false },
                new Account { AccountCode = "51", AccountName = "تكلفة البضاعة المباعة", AccountType = AccountType.Expense, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },
                new Account { AccountCode = "52", AccountName = "مصروفات التشغيل", AccountType = AccountType.Expense, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true },
                new Account { AccountCode = "53", AccountName = "مصروفات إدارية", AccountType = AccountType.Expense, CompanyId = companyId, CreatedBy = createdBy, CreatedDate = DateTime.Now, IsActive = true, IsFinalAccount = true }
            };

            context.Accounts.AddRange(accounts);
            await context.SaveChangesAsync();

            // تحديث الحسابات الفرعية بمعرفات الحسابات الأب
            await UpdateParentAccountIds(context, companyId);

            return accounts.Count;
        }

        private async Task UpdateParentAccountIds(JoudDbContext context, int companyId)
        {
            var accounts = await context.Accounts
                .Where(a => a.CompanyId == companyId)
                .ToListAsync();

            // تحديث الحسابات الفرعية
            foreach (var account in accounts)
            {
                if (account.AccountCode.Length > 1)
                {
                    var parentCode = account.AccountCode.Substring(0, account.AccountCode.Length - 1);
                    var parentAccount = accounts.FirstOrDefault(a => a.AccountCode == parentCode);
                    if (parentAccount != null)
                    {
                        account.ParentAccountId = parentAccount.AccountId;
                    }
                }
            }

            await context.SaveChangesAsync();
        }

        /// <summary>
        /// الحصول على رصيد الحساب
        /// Get account balance
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <param name="asOfDate">حتى تاريخ (اختياري)</param>
        /// <returns>رصيد الحساب</returns>
        public async Task<decimal> GetAccountBalanceAsync(int accountId, DateTime? asOfDate = null)
        {
            using var context = CreateDbContext();

            var query = context.JournalEntryDetails
                .Where(jed => jed.AccountId == accountId);

            if (asOfDate.HasValue)
                query = query.Where(jed => jed.JournalEntry.EntryDate <= asOfDate.Value);

            var debitTotal = await query.SumAsync(jed => jed.DebitAmount);
            var creditTotal = await query.SumAsync(jed => jed.CreditAmount);

            return debitTotal - creditTotal;
        }

        /// <summary>
        /// التحقق من صحة بيانات الحساب
        /// Validate account data
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateAccount(Account account)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(account.AccountCode))
                errors.Add("كود الحساب مطلوب");

            if (string.IsNullOrWhiteSpace(account.AccountName))
                errors.Add("اسم الحساب مطلوب");

            if (account.AccountCode?.Length > 20)
                errors.Add("كود الحساب يجب أن يكون أقل من 20 حرف");

            if (account.AccountName?.Length > 100)
                errors.Add("اسم الحساب يجب أن يكون أقل من 100 حرف");

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }


}
