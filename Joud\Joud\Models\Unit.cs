using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات الوحدات
    /// Unit Data Model
    /// </summary>
    [Table("Units")]
    public class Unit
    {
        [Key]
        public int UnitId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "كود الوحدة")]
        public string UnitCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الوحدة")]
        public string UnitName { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "اسم الوحدة بالإنجليزية")]
        public string? UnitNameEn { get; set; }

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "الشركة")]
        public int CompanyId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "أنشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "عدل بواسطة")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        [ForeignKey("CompanyId")]
        public virtual Company Company { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User CreatedByUser { get; set; } = null!;

        [ForeignKey("ModifiedBy")]
        public virtual User? ModifiedByUser { get; set; }

        // Collections
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
