using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Joud.Models;

namespace Joud.Services
{
    /// <summary>
    /// خدمة التقارير
    /// </summary>
    public class ReportService
    {
        /// <summary>
        /// إنشاء تقرير تجريبي
        /// </summary>
        public ReportData GenerateSampleReport()
        {
            var reportData = new ReportData
            {
                Title = "تقرير تجريبي",
                GeneratedDate = DateTime.Now,
                GeneratedBy = "النظام"
            };

            // إضافة الأعمدة
            reportData.Columns.AddRange(new[] { "الرقم", "الاسم", "التاريخ", "المبلغ" });

            // إضافة البيانات التجريبية
            for (int i = 1; i <= 10; i++)
            {
                reportData.Rows.Add(new List<object>
                {
                    i,
                    $"عنصر {i}",
                    DateTime.Now.AddDays(-i),
                    i * 100
                });
            }

            // إضافة الملخص
            reportData.Summary.Add("إجمالي السجلات", 10);
            reportData.Summary.Add("إجمالي المبلغ", 5500);

            return reportData;
        }

        /// <summary>
        /// الحصول على تقرير المبيعات
        /// </summary>
        public async Task<SalesReport> GetSalesReportAsync(int companyId, DateTime dateFrom, DateTime dateTo)
        {
            await Task.Delay(100); // محاكاة عملية قاعدة البيانات

            return new SalesReport
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                TotalInvoices = 25,
                TotalSales = 125000,
                TotalTax = 18750,
                TotalDiscount = 5000,
                TotalQuantity = 500,
                TopCustomers = new List<CustomerSalesData>
                {
                    new CustomerSalesData { CustomerId = 1, CustomerName = "عميل 1", TotalSales = 50000, InvoiceCount = 10 },
                    new CustomerSalesData { CustomerId = 2, CustomerName = "عميل 2", TotalSales = 35000, InvoiceCount = 7 },
                    new CustomerSalesData { CustomerId = 3, CustomerName = "عميل 3", TotalSales = 25000, InvoiceCount = 5 }
                },
                TopProducts = new List<ProductSalesData>
                {
                    new ProductSalesData { ProductId = 1, ProductName = "منتج 1", TotalQuantity = 200, TotalSales = 60000 },
                    new ProductSalesData { ProductId = 2, ProductName = "منتج 2", TotalQuantity = 150, TotalSales = 40000 },
                    new ProductSalesData { ProductId = 3, ProductName = "منتج 3", TotalQuantity = 100, TotalSales = 25000 }
                }
            };
        }

        /// <summary>
        /// الحصول على تقرير المشتريات
        /// </summary>
        public async Task<PurchaseReport> GetPurchaseReportAsync(int companyId, DateTime dateFrom, DateTime dateTo)
        {
            await Task.Delay(100); // محاكاة عملية قاعدة البيانات

            return new PurchaseReport
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                TotalInvoices = 15,
                TotalPurchases = 75000,
                TotalTax = 11250,
                TotalDiscount = 2500,
                TotalQuantity = 300,
                TopSuppliers = new List<SupplierPurchaseData>
                {
                    new SupplierPurchaseData { SupplierId = 1, SupplierName = "مورد 1", TotalPurchases = 30000, InvoiceCount = 6 },
                    new SupplierPurchaseData { SupplierId = 2, SupplierName = "مورد 2", TotalPurchases = 25000, InvoiceCount = 5 },
                    new SupplierPurchaseData { SupplierId = 3, SupplierName = "مورد 3", TotalPurchases = 20000, InvoiceCount = 4 }
                },
                TopProducts = new List<ProductPurchaseData>
                {
                    new ProductPurchaseData { ProductId = 1, ProductName = "منتج 1", TotalQuantity = 120, TotalPurchases = 36000 },
                    new ProductPurchaseData { ProductId = 2, ProductName = "منتج 2", TotalQuantity = 100, TotalPurchases = 24000 },
                    new ProductPurchaseData { ProductId = 3, ProductName = "منتج 3", TotalQuantity = 80, TotalPurchases = 15000 }
                }
            };
        }

        /// <summary>
        /// الحصول على تقرير المخزون
        /// </summary>
        public async Task<InventoryReport> GetInventoryReportAsync(int companyId)
        {
            await Task.Delay(100); // محاكاة عملية قاعدة البيانات

            return new InventoryReport
            {
                TotalProducts = 50,
                LowStockProducts = 5,
                OutOfStockProducts = 2,
                TotalInventoryValue = 250000,
                Products = new List<ProductInventoryData>
                {
                    new ProductInventoryData
                    {
                        ProductId = 1,
                        ProductCode = "P001",
                        ProductName = "منتج 1",
                        MainCategoryName = "فئة رئيسية 1",
                        SubCategoryName = "فئة فرعية 1",
                        UnitName = "قطعة",
                        WarehouseName = "مخزن رئيسي",
                        CurrentStock = 100,
                        MinimumStock = 20,
                        PurchasePrice = 50,
                        SalePrice = 75,
                        TotalValue = 5000
                    },
                    new ProductInventoryData
                    {
                        ProductId = 2,
                        ProductCode = "P002",
                        ProductName = "منتج 2",
                        MainCategoryName = "فئة رئيسية 2",
                        SubCategoryName = "فئة فرعية 2",
                        UnitName = "كيلو",
                        WarehouseName = "مخزن فرعي",
                        CurrentStock = 50,
                        MinimumStock = 10,
                        PurchasePrice = 30,
                        SalePrice = 45,
                        TotalValue = 1500
                    }
                }
            };
        }

        /// <summary>
        /// الحصول على التقرير المالي
        /// </summary>
        public async Task<FinancialReport> GetFinancialReportAsync(int companyId, DateTime dateFrom, DateTime dateTo)
        {
            await Task.Delay(100); // محاكاة عملية قاعدة البيانات

            var totalRevenue = 125000m;
            var totalExpenses = 85000m;
            var netProfit = totalRevenue - totalExpenses;

            return new FinancialReport
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                TotalRevenue = totalRevenue,
                TotalExpenses = totalExpenses,
                NetProfit = netProfit,
                ProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) : 0,
                CashSales = 75000,
                CreditSales = 50000,
                OtherRevenue = 0,
                CostOfGoodsSold = 60000,
                OperatingExpenses = 20000,
                OtherExpenses = 5000,
                TotalAssets = 500000,
                TotalLiabilities = 150000,
                Equity = 350000,
                AccountBalances = new List<AccountBalanceData>
                {
                    new AccountBalanceData { AccountId = 1, AccountCode = "1001", AccountName = "النقدية", AccountType = "أصول", DebitBalance = 50000, CreditBalance = 0, NetBalance = 50000 },
                    new AccountBalanceData { AccountId = 2, AccountCode = "1002", AccountName = "المخزون", AccountType = "أصول", DebitBalance = 100000, CreditBalance = 0, NetBalance = 100000 },
                    new AccountBalanceData { AccountId = 3, AccountCode = "2001", AccountName = "الموردون", AccountType = "خصوم", DebitBalance = 0, CreditBalance = 25000, NetBalance = -25000 },
                    new AccountBalanceData { AccountId = 4, AccountCode = "3001", AccountName = "رأس المال", AccountType = "حقوق ملكية", DebitBalance = 0, CreditBalance = 350000, NetBalance = -350000 }
                }
            };
        }

        /// <summary>
        /// الحصول على تقرير حركة المخزون
        /// </summary>
        public async Task<InventoryMovementReport> GetInventoryMovementReportAsync(int companyId, DateTime dateFrom, DateTime dateTo, int? productId = null)
        {
            await Task.Delay(100); // محاكاة عملية قاعدة البيانات

            return new InventoryMovementReport
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                ProductId = productId,
                TotalQuantityIn = 500,
                TotalQuantityOut = 300,
                TotalValueIn = 25000,
                TotalValueOut = 22500,
                Movements = new List<InventoryMovementData>
                {
                    new InventoryMovementData
                    {
                        Date = DateTime.Today.AddDays(-5),
                        ProductName = "منتج 1",
                        MovementType = "مشتريات",
                        ReferenceNumber = "P001",
                        ReferenceName = "مورد 1",
                        QuantityIn = 100,
                        QuantityOut = 0,
                        UnitPrice = 50,
                        Total = 5000
                    },
                    new InventoryMovementData
                    {
                        Date = DateTime.Today.AddDays(-3),
                        ProductName = "منتج 1",
                        MovementType = "مبيعات",
                        ReferenceNumber = "S001",
                        ReferenceName = "عميل 1",
                        QuantityIn = 0,
                        QuantityOut = 50,
                        UnitPrice = 75,
                        Total = 3750
                    }
                }
            };
        }
    }
}
