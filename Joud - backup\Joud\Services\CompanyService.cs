using System;
using System.Collections.Generic;
using System.Linq;
using Joud.Models;

namespace Joud.Services
{
    /// <summary>
    /// خدمة إدارة الشركات
    /// </summary>
    public class CompanyService
    {
        private static List<Company> _companies = new List<Company>();
        private static int _nextId = 1;

        /// <summary>
        /// الحصول على جميع الشركات
        /// </summary>
        public IEnumerable<Company> GetAllCompanies()
        {
            return _companies.AsEnumerable();
        }

        /// <summary>
        /// الحصول على شركة بالمعرف
        /// </summary>
        public Company? GetCompanyById(int id)
        {
            return _companies.FirstOrDefault(c => c.CompanyId == id);
        }

        /// <summary>
        /// إضافة شركة جديدة
        /// </summary>
        public Company AddCompany(Company company)
        {
            company.CompanyId = _nextId++;
            company.CreatedDate = DateTime.Now;
            _companies.Add(company);
            return company;
        }

        /// <summary>
        /// تحديث شركة
        /// </summary>
        public void UpdateCompany(Company company)
        {
            var existingCompany = GetCompanyById(company.CompanyId);
            if (existingCompany != null)
            {
                var index = _companies.IndexOf(existingCompany);
                company.ModifiedDate = DateTime.Now;
                _companies[index] = company;
            }
        }

        /// <summary>
        /// حذف شركة
        /// </summary>
        public void DeleteCompany(int id)
        {
            var company = GetCompanyById(id);
            if (company != null)
            {
                _companies.Remove(company);
            }
        }
    }
}
