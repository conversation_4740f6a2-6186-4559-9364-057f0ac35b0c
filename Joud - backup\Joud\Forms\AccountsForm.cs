using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة الحسابات المحاسبية
    /// Accounts Management Form
    /// </summary>
    public partial class AccountsForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly AccountService _accountService;
        
        private List<Account> _accounts;
        private Account? _selectedAccount;
        private bool _isEditing = false;

        public AccountsForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _accountService = new AccountService();
            
            _accounts = new List<Account>();
            
            InitializeFormSettings();
            SetupDataGridView();
            SetupComboBoxes();
            _ = LoadDataAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة الحسابات المحاسبية - نظام جود للمحاسبة المالية";
            this.Size = new Size(1200, 700);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.WindowState = FormWindowState.Maximized;
        }

        private void SetupDataGridView()
        {
            dgvAccounts.AutoGenerateColumns = false;
            dgvAccounts.AllowUserToAddRows = false;
            dgvAccounts.AllowUserToDeleteRows = false;
            dgvAccounts.ReadOnly = true;
            dgvAccounts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvAccounts.MultiSelect = false;
            dgvAccounts.BackgroundColor = Color.White;
            dgvAccounts.BorderStyle = BorderStyle.None;
            dgvAccounts.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvAccounts.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvAccounts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvAccounts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvAccounts.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvAccounts.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountCode",
                HeaderText = "كود الحساب",
                DataPropertyName = "AccountCode",
                Width = 120
            });

            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountName",
                HeaderText = "اسم الحساب",
                DataPropertyName = "AccountName",
                Width = 250
            });

            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountType",
                HeaderText = "نوع الحساب",
                Width = 120
            });

            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ParentAccountName",
                HeaderText = "الحساب الأب",
                Width = 200
            });

            dgvAccounts.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsFinalAccount",
                HeaderText = "حساب نهائي",
                DataPropertyName = "IsFinalAccount",
                Width = 100
            });

            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentBalance",
                HeaderText = "الرصيد الحالي",
                DataPropertyName = "CurrentBalance",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            // أحداث DataGridView
            dgvAccounts.SelectionChanged += DgvAccounts_SelectionChanged;
            dgvAccounts.CellDoubleClick += DgvAccounts_CellDoubleClick;
            dgvAccounts.DataBindingComplete += DgvAccounts_DataBindingComplete;
        }

        private void SetupComboBoxes()
        {
            // إعداد ComboBox لنوع الحساب
            cmbAccountType.Items.Clear();
            cmbAccountType.Items.Add(new { Text = "أصول", Value = AccountType.Asset });
            cmbAccountType.Items.Add(new { Text = "خصوم", Value = AccountType.Liability });
            cmbAccountType.Items.Add(new { Text = "حقوق ملكية", Value = AccountType.Equity });
            cmbAccountType.Items.Add(new { Text = "إيرادات", Value = AccountType.Revenue });
            cmbAccountType.Items.Add(new { Text = "مصروفات", Value = AccountType.Expense });
            cmbAccountType.DisplayMember = "Text";
            cmbAccountType.ValueMember = "Value";
            cmbAccountType.SelectedIndex = -1;
        }

        private async Task LoadDataAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل البيانات...";
                lblStatus.ForeColor = Color.Blue;

                _accounts = await _accountService.GetAllAccountsAsync(_currentCompany.CompanyId);
                dgvAccounts.DataSource = _accounts;

                // تحديث ComboBox للحسابات الأب
                UpdateParentAccountComboBox();

                lblStatus.Text = $"تم تحميل {_accounts.Count} حساب";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void UpdateParentAccountComboBox()
        {
            cmbParentAccount.DataSource = null;
            var parentAccounts = _accounts.Where(a => !a.IsFinalAccount).ToList();
            parentAccounts.Insert(0, new Account { AccountId = 0, AccountName = "-- لا يوجد --" });
            
            cmbParentAccount.DataSource = parentAccounts;
            cmbParentAccount.DisplayMember = "AccountName";
            cmbParentAccount.ValueMember = "AccountId";
            cmbParentAccount.SelectedIndex = 0;
        }

        private void DgvAccounts_DataBindingComplete(object? sender, DataGridViewBindingCompleteEventArgs e)
        {
            foreach (DataGridViewRow row in dgvAccounts.Rows)
            {
                if (row.DataBoundItem is Account account)
                {
                    // تحديث نوع الحساب
                    row.Cells["AccountType"].Value = GetAccountTypeText(account.AccountType);
                    
                    // تحديث اسم الحساب الأب
                    row.Cells["ParentAccountName"].Value = account.ParentAccount?.AccountName ?? "";
                }
            }
        }

        private string GetAccountTypeText(AccountType accountType)
        {
            return accountType switch
            {
                AccountType.Asset => "أصول",
                AccountType.Liability => "خصوم",
                AccountType.Equity => "حقوق ملكية",
                AccountType.Revenue => "إيرادات",
                AccountType.Expense => "مصروفات",
                _ => "غير محدد"
            };
        }

        private void DgvAccounts_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvAccounts.SelectedRows.Count > 0)
            {
                _selectedAccount = dgvAccounts.SelectedRows[0].DataBoundItem as Account;
                LoadAccountDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
            }
            else
            {
                _selectedAccount = null;
                ClearAccountDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void DgvAccounts_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadAccountDetails()
        {
            if (_selectedAccount == null) return;

            txtAccountCode.Text = _selectedAccount.AccountCode;
            txtAccountName.Text = _selectedAccount.AccountName;
            cmbAccountType.SelectedValue = _selectedAccount.AccountType;
            cmbParentAccount.SelectedValue = _selectedAccount.ParentAccountId ?? 0;
            chkIsFinalAccount.Checked = _selectedAccount.IsFinalAccount;
            txtDescription.Text = _selectedAccount.Description;
            lblCurrentBalance.Text = $"الرصيد الحالي: {_selectedAccount.CurrentBalance:N2} {_currentCompany.Currency}";
        }

        private void ClearAccountDetails()
        {
            txtAccountCode.Clear();
            txtAccountName.Clear();
            cmbAccountType.SelectedIndex = -1;
            cmbParentAccount.SelectedIndex = 0;
            chkIsFinalAccount.Checked = false;
            txtDescription.Clear();
            lblCurrentBalance.Text = "الرصيد الحالي: 0.00";
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            _isEditing = false;
            _selectedAccount = null;
            ClearAccountDetails();
            SetEditMode(true);
            txtAccountCode.Focus();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedAccount == null)
            {
                ShowError("يرجى اختيار حساب للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            txtAccountCode.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ الحساب...";
                lblStatus.ForeColor = Color.Blue;

                var account = CreateAccountFromInput();

                if (_isEditing && _selectedAccount != null)
                {
                    account.AccountId = _selectedAccount.AccountId;
                    account.CreatedDate = _selectedAccount.CreatedDate;
                    account.CreatedBy = _selectedAccount.CreatedBy;
                    account.ModifiedBy = _currentUser.Username;

                    await _accountService.UpdateAccountAsync(account);
                    lblStatus.Text = "تم تحديث الحساب بنجاح";
                }
                else
                {
                    account.CreatedBy = _currentUser.Username;
                    account.CompanyId = _currentCompany.CompanyId;

                    await _accountService.AddAccountAsync(account);
                    lblStatus.Text = "تم إضافة الحساب بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadDataAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ الحساب: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedAccount != null)
            {
                LoadAccountDetails();
            }
            else
            {
                ClearAccountDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedAccount == null)
            {
                ShowError("يرجى اختيار حساب للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف الحساب '{_selectedAccount.AccountName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف الحساب...";
                    lblStatus.ForeColor = Color.Blue;

                    await _accountService.DeleteAccountAsync(_selectedAccount.AccountId, _currentUser.Username);
                    
                    lblStatus.Text = "تم حذف الحساب بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadDataAsync();
                    ClearAccountDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف الحساب: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void btnCreateDefaultChart_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(
                "هل تريد إنشاء دليل الحسابات الافتراضي؟\n\nسيتم إنشاء الحسابات الأساسية للشركة.",
                "تأكيد إنشاء دليل الحسابات",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري إنشاء دليل الحسابات...";
                    lblStatus.ForeColor = Color.Blue;

                    int accountsCreated = await _accountService.CreateDefaultChartOfAccountsAsync(_currentCompany.CompanyId, _currentUser.Username);
                    
                    lblStatus.Text = $"تم إنشاء {accountsCreated} حساب بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadDataAsync();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في إنشاء دليل الحسابات: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvAccounts.DataSource = _accounts;
                }
                else
                {
                    var searchResults = await _accountService.SearchAccountsAsync(txtSearch.Text, _currentCompany.CompanyId);
                    dgvAccounts.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadDataAsync();
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtAccountCode.Text))
                errors.Add("كود الحساب مطلوب");

            if (string.IsNullOrWhiteSpace(txtAccountName.Text))
                errors.Add("اسم الحساب مطلوب");

            if (cmbAccountType.SelectedValue == null)
                errors.Add("يجب اختيار نوع الحساب");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private Account CreateAccountFromInput()
        {
            return new Account
            {
                AccountCode = txtAccountCode.Text.Trim(),
                AccountName = txtAccountName.Text.Trim(),
                AccountType = (AccountType)cmbAccountType.SelectedValue!,
                ParentAccountId = (int)cmbParentAccount.SelectedValue! == 0 ? null : (int)cmbParentAccount.SelectedValue!,
                IsFinalAccount = chkIsFinalAccount.Checked,
                Description = txtDescription.Text.Trim()
            };
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال
            txtAccountCode.ReadOnly = !isEditing;
            txtAccountName.ReadOnly = !isEditing;
            cmbAccountType.Enabled = isEditing;
            cmbParentAccount.Enabled = isEditing;
            chkIsFinalAccount.Enabled = isEditing;
            txtDescription.ReadOnly = !isEditing;

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedAccount != null;
            btnDelete.Enabled = !isEditing && _selectedAccount != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            btnCreateDefaultChart.Enabled = !isEditing;
            dgvAccounts.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is ComboBox || 
                    control is CheckBox || control is DataGridView)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void AccountsForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
        }
    }
}
