using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Joud.Models;
using Joud.Services;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة الشركات - Companies Management Form
    /// تسمح بإضافة وتعديل وحذف بيانات الشركات
    /// </summary>
    public partial class CompaniesForm : Form
    {
        #region Private Fields

        private readonly CompanyService _companyService;
        private List<Company> _companies;
        private Company _selectedCompany;
        private bool _isEditing = false;
        private bool _hasChanges = false;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ شاشة إدارة الشركات
        /// </summary>
        public CompaniesForm()
        {
            InitializeComponent();
            _companyService = new CompanyService();
            InitializeForm();
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل الشاشة
        /// </summary>
        private void CompaniesForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoadCompanies();
                SetupDataGridView();
                UpdateUI();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Button Events

        /// <summary>
        /// حدث النقر على زر إضافة شركة جديدة
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                StartAddMode();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء إضافة شركة جديدة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تعديل الشركة
        /// </summary>
        private void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedCompany != null)
                {
                    StartEditMode();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار شركة للتعديل.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء تعديل الشركة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر حذف الشركة
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedCompany != null)
                {
                    DeleteCompany();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار شركة للحذف.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الشركة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الحفظ
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                SaveCompany();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الشركة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الإلغاء
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                CancelOperation();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء العملية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر البحث
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                SearchCompanies();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadCompanies();
                ClearForm();
                lblStatus.Text = "تم تحديث البيانات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region DataGridView Events

        /// <summary>
        /// حدث تغيير التحديد في جدول الشركات
        /// </summary>
        private void dgvCompanies_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dgvCompanies.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvCompanies.SelectedRows[0];
                    if (selectedRow.DataBoundItem is Company company)
                    {
                        _selectedCompany = company;
                        DisplayCompanyDetails(company);
                        UpdateUI();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تغيير التحديد: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على جدول الشركات
        /// </summary>
        private void dgvCompanies_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && _selectedCompany != null)
                {
                    StartEditMode();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الشركة للتعديل: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region TextBox Events

        /// <summary>
        /// حدث تغيير نص البحث
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    SearchCompanies();
                }
                else
                {
                    LoadCompanies();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث التلقائي: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث تغيير البيانات في النموذج
        /// </summary>
        private void FormData_Changed(object sender, EventArgs e)
        {
            if (_isEditing)
            {
                _hasChanges = true;
                UpdateUI();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تهيئة الشاشة
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // ربط أحداث تغيير البيانات
                txtCompanyCode.TextChanged += FormData_Changed;
                txtCompanyName.TextChanged += FormData_Changed;
                txtCommercialRecord.TextChanged += FormData_Changed;
                txtTaxNumber.TextChanged += FormData_Changed;
                txtPhone.TextChanged += FormData_Changed;
                txtEmail.TextChanged += FormData_Changed;
                txtAddress.TextChanged += FormData_Changed;
                txtWebsite.TextChanged += FormData_Changed;
                txtContactPerson.TextChanged += FormData_Changed;
                txtNotes.TextChanged += FormData_Changed;
                txtFax.TextChanged += FormData_Changed;
                chkIsActive.CheckedChanged += FormData_Changed;

                // إعداد التحقق من صحة البيانات
                SetupValidation();
                
                // تعيين القيم الافتراضية
                chkIsActive.Checked = true;
                dtpEstablishmentDate.Value = DateTime.Now;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد جدول البيانات
        /// </summary>
        private void SetupDataGridView()
        {
            try
            {
                dgvCompanies.AutoGenerateColumns = false;
                dgvCompanies.Columns.Clear();

                // إضافة الأعمدة
                dgvCompanies.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "CompanyName",
                    HeaderText = "اسم الشركة",
                    DataPropertyName = "CompanyName",
                    Width = 200,
                    ReadOnly = true
                });

                dgvCompanies.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "CommercialRecord",
                    HeaderText = "السجل التجاري",
                    DataPropertyName = "CommercialRecord",
                    Width = 120,
                    ReadOnly = true
                });

                dgvCompanies.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "TaxNumber",
                    HeaderText = "الرقم الضريبي",
                    DataPropertyName = "TaxNumber",
                    Width = 120,
                    ReadOnly = true
                });

                dgvCompanies.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Phone",
                    HeaderText = "الهاتف",
                    DataPropertyName = "Phone",
                    Width = 120,
                    ReadOnly = true
                });

                dgvCompanies.Columns.Add(new DataGridViewCheckBoxColumn
                {
                    Name = "IsActive",
                    HeaderText = "نشط",
                    DataPropertyName = "IsActive",
                    Width = 60,
                    ReadOnly = true
                });

                dgvCompanies.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "CreatedDate",
                    HeaderText = "تاريخ الإنشاء",
                    DataPropertyName = "CreatedDate",
                    Width = 120,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
                });

                // إعداد خصائص الجدول
                dgvCompanies.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dgvCompanies.MultiSelect = false;
                dgvCompanies.ReadOnly = true;
                dgvCompanies.AllowUserToAddRows = false;
                dgvCompanies.AllowUserToDeleteRows = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد جدول البيانات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد التحقق من صحة البيانات
        /// </summary>
        private void SetupValidation()
        {
            try
            {
                // إعداد التحقق من الحقول المطلوبة
                txtCompanyName.Tag = "required";
                txtCommercialRecord.Tag = "required";
                
                // إعداد التحقق من البريد الإلكتروني
                txtEmail.Leave += ValidateEmail;
                
                // إعداد التحقق من الهاتف
                txtPhone.Leave += ValidatePhone;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد التحقق: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل قائمة الشركات
        /// </summary>
        private void LoadCompanies()
        {
            try
            {
                _companies = _companyService.GetAllCompanies().ToList();
                dgvCompanies.DataSource = _companies;
                
                // تحديث الإحصائيات
                UpdateStatistics();
                
                lblStatus.Text = $"تم تحميل {_companies.Count} شركة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشركات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// البحث في الشركات
        /// </summary>
        private void SearchCompanies()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    LoadCompanies();
                    return;
                }

                string searchTerm = txtSearch.Text.Trim().ToLower();
                var filteredCompanies = _companies.Where(c =>
                    c.CompanyName.ToLower().Contains(searchTerm) ||
                    c.CommercialRecord.ToLower().Contains(searchTerm) ||
                    c.TaxNumber.ToLower().Contains(searchTerm) ||
                    (!string.IsNullOrEmpty(c.Phone) && c.Phone.Contains(searchTerm))
                ).ToList();

                dgvCompanies.DataSource = filteredCompanies;
                lblStatus.Text = $"تم العثور على {filteredCompanies.Count} شركة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل الشركة
        /// </summary>
        /// <param name="company">الشركة المحددة</param>
        private void DisplayCompanyDetails(Company company)
        {
            try
            {
                if (company == null) return;

                txtCompanyCode.Text = company.CompanyId.ToString();
                txtCompanyName.Text = company.CompanyName;
                txtCommercialRecord.Text = company.CommercialRecord;
                txtTaxNumber.Text = company.TaxNumber;
                txtPhone.Text = company.Phone;
                txtEmail.Text = company.Email;
                txtAddress.Text = company.Address;
                txtWebsite.Text = company.Website;
                txtContactPerson.Text = company.ContactPerson;
                txtNotes.Text = company.Notes;
                txtFax.Text = company.Fax ?? "";
                chkIsActive.Checked = company.IsActive;
                
                if (company.EstablishmentDate.HasValue)
                {
                    dtpEstablishmentDate.Value = company.EstablishmentDate.Value;
                }
                else
                {
                    dtpEstablishmentDate.Value = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تفاصيل الشركة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void ClearForm()
        {
            try
            {
                txtCompanyCode.Clear();
                txtCompanyName.Clear();
                txtCommercialRecord.Clear();
                txtTaxNumber.Clear();
                txtPhone.Clear();
                txtEmail.Clear();
                txtAddress.Clear();
                txtWebsite.Clear();
                txtContactPerson.Clear();
                txtNotes.Clear();
                txtFax.Clear();
                chkIsActive.Checked = true;
                dtpEstablishmentDate.Value = DateTime.Now;
                
                _selectedCompany = null;
                _isEditing = false;
                _hasChanges = false;
                
                UpdateUI();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في مسح النموذج: {ex.Message}");
            }
        }

        #endregion

        #region CRUD Operations

        /// <summary>
        /// بدء وضع الإضافة
        /// </summary>
        private void StartAddMode()
        {
            try
            {
                ClearForm();
                _isEditing = true;
                _selectedCompany = null;

                // تركيز على أول حقل
                txtCompanyName.Focus();

                UpdateUI();
                lblStatus.Text = "وضع إضافة شركة جديدة";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في بدء وضع الإضافة: {ex.Message}");
            }
        }

        /// <summary>
        /// بدء وضع التعديل
        /// </summary>
        private void StartEditMode()
        {
            try
            {
                if (_selectedCompany == null)
                {
                    MessageBox.Show("يرجى اختيار شركة للتعديل.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                _isEditing = true;
                _hasChanges = false;

                // تركيز على أول حقل
                txtCompanyName.Focus();

                UpdateUI();
                lblStatus.Text = $"وضع تعديل الشركة: {_selectedCompany.CompanyName}";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في بدء وضع التعديل: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ الشركة
        /// </summary>
        private void SaveCompany()
        {
            try
            {
                if (!ValidateForm())
                {
                    return;
                }

                var company = CreateCompanyFromForm();

                if (_selectedCompany == null)
                {
                    // إضافة شركة جديدة
                    var newCompany = _companyService.AddCompany(company);
                    _companies.Add(newCompany);

                    MessageBox.Show("تم إضافة الشركة بنجاح.", "نجح الحفظ",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = "تم إضافة الشركة بنجاح";
                }
                else
                {
                    // تعديل شركة موجودة
                    company.CompanyId = _selectedCompany.CompanyId;
                    company.CreatedDate = _selectedCompany.CreatedDate;
                    company.ModifiedDate = DateTime.Now;

                    _companyService.UpdateCompany(company);

                    // تحديث القائمة
                    var index = _companies.FindIndex(c => c.CompanyId == company.CompanyId);
                    if (index >= 0)
                    {
                        _companies[index] = company;
                    }

                    MessageBox.Show("تم تعديل الشركة بنجاح.", "نجح الحفظ",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = "تم تعديل الشركة بنجاح";
                }

                // إعادة تحميل البيانات وإنهاء وضع التعديل
                LoadCompanies();
                ClearForm();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الشركة: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف الشركة
        /// </summary>
        private void DeleteCompany()
        {
            try
            {
                if (_selectedCompany == null)
                {
                    MessageBox.Show("يرجى اختيار شركة للحذف.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد حذف الشركة '{_selectedCompany.CompanyName}'؟\n\nتحذير: لا يمكن التراجع عن هذه العملية.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _companyService.DeleteCompany(_selectedCompany.CompanyId);

                    MessageBox.Show("تم حذف الشركة بنجاح.", "نجح الحذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // إعادة تحميل البيانات
                    LoadCompanies();
                    ClearForm();
                    UpdateStatistics();

                    lblStatus.Text = "تم حذف الشركة بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الشركة: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void CancelOperation()
        {
            try
            {
                if (_hasChanges)
                {
                    var result = MessageBox.Show(
                        "هناك تغييرات لم يتم حفظها. هل تريد المتابعة بدون حفظ؟",
                        "تأكيد الإلغاء",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.No)
                    {
                        return;
                    }
                }

                if (_selectedCompany != null)
                {
                    DisplayCompanyDetails(_selectedCompany);
                }
                else
                {
                    ClearForm();
                }

                _isEditing = false;
                _hasChanges = false;
                UpdateUI();

                lblStatus.Text = "تم إلغاء العملية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء العملية: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// التحقق من صحة النموذج
        /// </summary>
        /// <returns>true إذا كان النموذج صحيح</returns>
        private bool ValidateForm()
        {
            try
            {
                // التحقق من الحقول المطلوبة
                if (string.IsNullOrWhiteSpace(txtCompanyName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الشركة.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCompanyName.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(txtCommercialRecord.Text))
                {
                    MessageBox.Show("يرجى إدخال رقم السجل التجاري.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCommercialRecord.Focus();
                    return false;
                }

                // التحقق من تفرد السجل التجاري
                if (IsCommercialRecordExists(txtCommercialRecord.Text))
                {
                    MessageBox.Show("رقم السجل التجاري موجود مسبقاً.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCommercialRecord.Focus();
                    return false;
                }

                // التحقق من البريد الإلكتروني
                if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
                {
                    MessageBox.Show("البريد الإلكتروني غير صحيح.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من النموذج: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود السجل التجاري
        /// </summary>
        /// <param name="commercialRecord">رقم السجل التجاري</param>
        /// <returns>true إذا كان موجود</returns>
        private bool IsCommercialRecordExists(string commercialRecord)
        {
            try
            {
                return _companies.Any(c =>
                    c.CommercialRecord.Equals(commercialRecord, StringComparison.OrdinalIgnoreCase) &&
                    (_selectedCompany == null || c.CompanyId != _selectedCompany.CompanyId));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>true إذا كان صحيح</returns>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من البريد الإلكتروني عند مغادرة الحقل
        /// </summary>
        private void ValidateEmail(object sender, EventArgs e)
        {
            try
            {
                var textBox = sender as TextBox;
                if (!string.IsNullOrWhiteSpace(textBox.Text) && !IsValidEmail(textBox.Text))
                {
                    textBox.BackColor = Color.LightPink;
                    lblStatus.Text = "البريد الإلكتروني غير صحيح";
                }
                else
                {
                    textBox.BackColor = SystemColors.Window;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من البريد الإلكتروني: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من الهاتف عند مغادرة الحقل
        /// </summary>
        private void ValidatePhone(object sender, EventArgs e)
        {
            try
            {
                var textBox = sender as TextBox;
                if (!string.IsNullOrWhiteSpace(textBox.Text))
                {
                    // إزالة المسافات والرموز
                    string phone = textBox.Text.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

                    if (phone.Length < 10)
                    {
                        textBox.BackColor = Color.LightPink;
                        lblStatus.Text = "رقم الهاتف قصير جداً";
                    }
                    else
                    {
                        textBox.BackColor = SystemColors.Window;
                    }
                }
                else
                {
                    textBox.BackColor = SystemColors.Window;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من الهاتف: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// إنشاء كائن الشركة من النموذج
        /// </summary>
        /// <returns>كائن الشركة</returns>
        private Company CreateCompanyFromForm()
        {
            try
            {
                return new Company
                {
                    CompanyName = txtCompanyName.Text.Trim(),
                    CommercialRecord = txtCommercialRecord.Text.Trim(),
                    TaxNumber = txtTaxNumber.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Address = txtAddress.Text.Trim(),
                    Website = txtWebsite.Text.Trim(),
                    ContactPerson = txtContactPerson.Text.Trim(),
                    Notes = txtNotes.Text.Trim(),
                    Fax = txtFax.Text.Trim(),
                    IsActive = chkIsActive.Checked,
                    EstablishmentDate = dtpEstablishmentDate.Value,
                    CreatedDate = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء كائن الشركة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                if (_companies != null)
                {
                    int totalCompanies = _companies.Count;
                    int activeCompanies = _companies.Count(c => c.IsActive);
                    int inactiveCompanies = totalCompanies - activeCompanies;

                    lblTotalCompanies.Text = totalCompanies.ToString();
                    lblActiveCompanies.Text = activeCompanies.ToString();
                    lblInactiveCompanies.Text = inactiveCompanies.ToString();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث واجهة المستخدم
        /// </summary>
        private void UpdateUI()
        {
            try
            {
                // تحديث حالة الأزرار
                btnAdd.Enabled = !_isEditing;
                btnEdit.Enabled = !_isEditing && _selectedCompany != null;
                btnDelete.Enabled = !_isEditing && _selectedCompany != null;
                btnSave.Enabled = _isEditing;
                btnCancel.Enabled = _isEditing;
                btnRefresh.Enabled = !_isEditing;

                // تحديث حالة الحقول
                bool enableFields = _isEditing;
                txtCompanyCode.ReadOnly = true; // دائماً للقراءة فقط
                txtCompanyName.ReadOnly = !enableFields;
                txtCommercialRecord.ReadOnly = !enableFields;
                txtTaxNumber.ReadOnly = !enableFields;
                txtPhone.ReadOnly = !enableFields;
                txtEmail.ReadOnly = !enableFields;
                txtAddress.ReadOnly = !enableFields;
                txtWebsite.ReadOnly = !enableFields;
                txtContactPerson.ReadOnly = !enableFields;
                txtNotes.ReadOnly = !enableFields;
                txtFax.ReadOnly = !enableFields;
                chkIsActive.Enabled = enableFields;
                dtpEstablishmentDate.Enabled = enableFields;

                // تحديث لون الخلفية للحقول
                Color backColor = enableFields ? SystemColors.Window : SystemColors.Control;
                txtCompanyCode.BackColor = SystemColors.Control; // دائماً رمادي
                txtCompanyName.BackColor = backColor;
                txtCommercialRecord.BackColor = backColor;
                txtTaxNumber.BackColor = backColor;
                txtPhone.BackColor = backColor;
                txtEmail.BackColor = backColor;
                txtAddress.BackColor = backColor;
                txtWebsite.BackColor = backColor;
                txtContactPerson.BackColor = backColor;
                txtNotes.BackColor = backColor;
                txtFax.BackColor = backColor;

                // تحديث حالة الجدول
                dgvCompanies.Enabled = !_isEditing;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة المستخدم: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// عرض شاشة إدارة الشركات
        /// </summary>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>نتيجة الحوار</returns>
        public static DialogResult ShowCompaniesForm(IWin32Window parent = null)
        {
            try
            {
                using (var companiesForm = new CompaniesForm())
                {
                    return companiesForm.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض شاشة الشركات: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// الحصول على الشركة المحددة
        /// </summary>
        /// <returns>الشركة المحددة</returns>
        public Company GetSelectedCompany()
        {
            return _selectedCompany;
        }

        /// <summary>
        /// تعيين وضع التحرير
        /// </summary>
        /// <param name="editMode">وضع التحرير</param>
        private void SetEditMode(bool editMode)
        {
            _isEditing = editMode;
            UpdateUI();
        }

        /// <summary>
        /// مسح تفاصيل الشركة
        /// </summary>
        private void ClearCompanyDetails()
        {
            ClearForm();
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        private void ShowError(string message)
        {
            lblStatus.Text = message;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                StartAddMode();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في بدء إضافة شركة جديدة: {ex.Message}");
            }
        }

        #endregion
    }
}
