using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة فواتير المبيعات
    /// Sales Invoice Management Service
    /// </summary>
    public class SalesInvoiceService
    {
        private readonly string _connectionString;
        private readonly JournalEntryService _journalEntryService;

        public SalesInvoiceService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
            _journalEntryService = new JournalEntryService();
        }

        /// <summary>
        /// الحصول على جميع فواتير المبيعات للشركة
        /// Get all sales invoices for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة فواتير المبيعات</returns>
        public async Task<List<SalesInvoice>> GetAllSalesInvoicesAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive)
                .Include(si => si.Customer)
                .Include(si => si.Warehouse)
                .Include(si => si.CreatedByUser)
                .Include(si => si.SalesInvoiceItems)
                    .ThenInclude(sii => sii.Product)
                .OrderByDescending(si => si.InvoiceDate)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على فاتورة مبيعات بالمعرف
        /// Get sales invoice by ID
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <returns>بيانات الفاتورة</returns>
        public async Task<SalesInvoice?> GetSalesInvoiceByIdAsync(int invoiceId)
        {
            using var context = CreateDbContext();
            return await context.SalesInvoices
                .Include(si => si.Customer)
                .Include(si => si.Warehouse)
                .Include(si => si.CreatedByUser)
                .Include(si => si.Company)
                .Include(si => si.SalesInvoiceItems)
                    .ThenInclude(sii => sii.Product)
                        .ThenInclude(p => p.Unit)
                .FirstOrDefaultAsync(si => si.SalesInvoiceId == invoiceId && si.IsActive);
        }

        /// <summary>
        /// الحصول على فاتورة مبيعات بالرقم
        /// Get sales invoice by number
        /// </summary>
        /// <param name="invoiceNumber">رقم الفاتورة</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات الفاتورة</returns>
        public async Task<SalesInvoice?> GetSalesInvoiceByNumberAsync(string invoiceNumber, int companyId)
        {
            using var context = CreateDbContext();
            return await context.SalesInvoices
                .FirstOrDefaultAsync(si => si.InvoiceNumber == invoiceNumber && 
                                          si.CompanyId == companyId && si.IsActive);
        }

        /// <summary>
        /// البحث في فواتير المبيعات
        /// Search sales invoices
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <param name="dateFrom">من تاريخ</param>
        /// <param name="dateTo">إلى تاريخ</param>
        /// <returns>قائمة الفواتير المطابقة</returns>
        public async Task<List<SalesInvoice>> SearchSalesInvoicesAsync(string searchTerm, int companyId, 
            DateTime? dateFrom = null, DateTime? dateTo = null)
        {
            using var context = CreateDbContext();
            
            var query = context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive)
                .Include(si => si.Customer)
                .Include(si => si.Warehouse)
                .Include(si => si.CreatedByUser)
                .Include(si => si.SalesInvoiceItems)
                    .ThenInclude(sii => sii.Product)
                .AsQueryable();

            // تطبيق فلتر التاريخ
            if (dateFrom.HasValue)
                query = query.Where(si => si.InvoiceDate >= dateFrom.Value);
            
            if (dateTo.HasValue)
                query = query.Where(si => si.InvoiceDate <= dateTo.Value);

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.Trim().ToLower();
                query = query.Where(si => 
                    si.InvoiceNumber.ToLower().Contains(searchTerm) ||
                    si.Customer.CustomerName.ToLower().Contains(searchTerm) ||
                    (si.Notes != null && si.Notes.ToLower().Contains(searchTerm)));
            }

            return await query
                .OrderByDescending(si => si.InvoiceDate)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة فاتورة مبيعات جديدة
        /// Add new sales invoice
        /// </summary>
        /// <param name="salesInvoice">بيانات الفاتورة</param>
        /// <returns>معرف الفاتورة الجديدة</returns>
        public async Task<int> AddSalesInvoiceAsync(SalesInvoice salesInvoice)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // التحقق من عدم تكرار رقم الفاتورة
                bool numberExists = await context.SalesInvoices
                    .AnyAsync(si => si.InvoiceNumber == salesInvoice.InvoiceNumber && 
                                   si.CompanyId == salesInvoice.CompanyId);
                
                if (numberExists)
                    throw new InvalidOperationException($"رقم الفاتورة '{salesInvoice.InvoiceNumber}' موجود مسبقاً");

                // إنشاء رقم فاتورة تلقائي إذا لم يتم تحديده
                if (string.IsNullOrEmpty(salesInvoice.InvoiceNumber))
                {
                    salesInvoice.InvoiceNumber = await GenerateSalesInvoiceNumberAsync(salesInvoice.CompanyId);
                }

                // التحقق من وجود العميل
                var customer = await context.Customers
                    .FirstOrDefaultAsync(c => c.CustomerId == salesInvoice.CustomerId && 
                                             c.CompanyId == salesInvoice.CompanyId && c.IsActive);
                
                if (customer == null)
                    throw new InvalidOperationException("العميل غير موجود أو غير نشط");

                // التحقق من وجود المخزن
                var warehouse = await context.Warehouses
                    .FirstOrDefaultAsync(w => w.WarehouseId == salesInvoice.WarehouseId && 
                                             w.CompanyId == salesInvoice.CompanyId && w.IsActive);
                
                if (warehouse == null)
                    throw new InvalidOperationException("المخزن غير موجود أو غير نشط");

                // حساب إجمالي الفاتورة
                decimal subtotal = 0;
                foreach (var item in salesInvoice.SalesInvoiceItems)
                {
                    // التحقق من وجود الصنف
                    var product = await context.Products
                        .FirstOrDefaultAsync(p => p.ProductId == item.ProductId && 
                                                 p.CompanyId == salesInvoice.CompanyId && p.IsActive);
                    
                    if (product == null)
                        throw new InvalidOperationException($"الصنف غير موجود أو غير نشط");

                    // التحقق من توفر المخزون
                    if (product.CurrentStock < item.Quantity)
                        throw new InvalidOperationException($"المخزون المتاح للصنف '{product.ProductName}' غير كافي. المتاح: {product.CurrentStock}");

                    // حساب إجمالي البند
                    item.Total = item.Quantity * item.UnitPrice;
                    subtotal += item.Total;

                    // تحديث المخزون
                    product.CurrentStock -= item.Quantity;
                }

                // حساب الضريبة والإجمالي
                salesInvoice.Subtotal = subtotal;
                salesInvoice.TaxAmount = subtotal * (salesInvoice.TaxRate / 100);
                salesInvoice.DiscountAmount = subtotal * (salesInvoice.DiscountRate / 100);
                salesInvoice.TotalAmount = subtotal + salesInvoice.TaxAmount - salesInvoice.DiscountAmount;

                salesInvoice.CreatedDate = DateTime.Now;
                salesInvoice.IsActive = true;

                // إضافة الفاتورة
                context.SalesInvoices.Add(salesInvoice);
                await context.SaveChangesAsync();

                // تحديث رصيد العميل
                customer.CurrentBalance += salesInvoice.TotalAmount;

                // إنشاء القيد المحاسبي التلقائي
                try
                {
                    await _journalEntryService.CreateSalesInvoiceJournalEntryAsync(salesInvoice, salesInvoice.CreatedBy.ToString());
                }
                catch (Exception ex)
                {
                    // تسجيل الخطأ ولكن لا نوقف العملية
                    // يمكن إنشاء القيد لاحقاً يدوياً
                    System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء القيد المحاسبي: {ex.Message}");
                }

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return salesInvoice.SalesInvoiceId;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// تحديث فاتورة مبيعات
        /// Update sales invoice
        /// </summary>
        /// <param name="salesInvoice">بيانات الفاتورة المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateSalesInvoiceAsync(SalesInvoice salesInvoice)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var existingInvoice = await context.SalesInvoices
                    .Include(si => si.SalesInvoiceItems)
                    .Include(si => si.Customer)
                    .FirstOrDefaultAsync(si => si.SalesInvoiceId == salesInvoice.SalesInvoiceId);

                if (existingInvoice == null)
                    throw new InvalidOperationException("الفاتورة غير موجودة");

                // استرداد المخزون من الفاتورة القديمة
                foreach (var oldItem in existingInvoice.SalesInvoiceItems)
                {
                    var product = await context.Products
                        .FirstOrDefaultAsync(p => p.ProductId == oldItem.ProductId);
                    
                    if (product != null)
                    {
                        product.CurrentStock += oldItem.Quantity;
                    }
                }

                // تحديث رصيد العميل (استرداد المبلغ القديم)
                existingInvoice.Customer.CurrentBalance -= existingInvoice.TotalAmount;

                // حذف البنود القديمة
                context.SalesInvoiceItems.RemoveRange(existingInvoice.SalesInvoiceItems);

                // تحديث بيانات الفاتورة
                existingInvoice.InvoiceNumber = salesInvoice.InvoiceNumber;
                existingInvoice.InvoiceDate = salesInvoice.InvoiceDate;
                existingInvoice.CustomerId = salesInvoice.CustomerId;
                existingInvoice.WarehouseId = salesInvoice.WarehouseId;
                existingInvoice.TaxRate = salesInvoice.TaxRate;
                existingInvoice.DiscountRate = salesInvoice.DiscountRate;
                existingInvoice.Notes = salesInvoice.Notes;
                existingInvoice.ModifiedDate = DateTime.Now;
                existingInvoice.ModifiedBy = salesInvoice.ModifiedBy;

                // إضافة البنود الجديدة وحساب الإجمالي
                decimal subtotal = 0;
                foreach (var item in salesInvoice.SalesInvoiceItems)
                {
                    var product = await context.Products
                        .FirstOrDefaultAsync(p => p.ProductId == item.ProductId);
                    
                    if (product == null)
                        throw new InvalidOperationException($"الصنف غير موجود");

                    if (product.CurrentStock < item.Quantity)
                        throw new InvalidOperationException($"المخزون المتاح للصنف '{product.ProductName}' غير كافي");

                    item.Total = item.Quantity * item.UnitPrice;
                    subtotal += item.Total;
                    item.SalesInvoiceId = existingInvoice.SalesInvoiceId;

                    // تحديث المخزون
                    product.CurrentStock -= item.Quantity;

                    existingInvoice.SalesInvoiceItems.Add(item);
                }

                // حساب الضريبة والإجمالي
                existingInvoice.Subtotal = subtotal;
                existingInvoice.TaxAmount = subtotal * (existingInvoice.TaxRate / 100);
                existingInvoice.DiscountAmount = subtotal * (existingInvoice.DiscountRate / 100);
                existingInvoice.TotalAmount = subtotal + existingInvoice.TaxAmount - existingInvoice.DiscountAmount;

                // تحديث رصيد العميل (إضافة المبلغ الجديد)
                existingInvoice.Customer.CurrentBalance += existingInvoice.TotalAmount;

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// حذف فاتورة مبيعات (حذف منطقي)
        /// Delete sales invoice (soft delete)
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteSalesInvoiceAsync(int invoiceId, int deletedBy)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var invoice = await context.SalesInvoices
                    .Include(si => si.SalesInvoiceItems)
                    .Include(si => si.Customer)
                    .FirstOrDefaultAsync(si => si.SalesInvoiceId == invoiceId);

                if (invoice == null)
                    throw new InvalidOperationException("الفاتورة غير موجودة");

                // استرداد المخزون
                foreach (var item in invoice.SalesInvoiceItems)
                {
                    var product = await context.Products
                        .FirstOrDefaultAsync(p => p.ProductId == item.ProductId);
                    
                    if (product != null)
                    {
                        product.CurrentStock += item.Quantity;
                    }
                }

                // تحديث رصيد العميل
                invoice.Customer.CurrentBalance -= invoice.TotalAmount;

                // حذف منطقي
                invoice.IsActive = false;
                invoice.ModifiedDate = DateTime.Now;
                invoice.ModifiedBy = deletedBy;

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// إنشاء رقم فاتورة مبيعات تلقائي
        /// Generate automatic sales invoice number
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>رقم الفاتورة الجديد</returns>
        public async Task<string> GenerateSalesInvoiceNumberAsync(int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على أعلى رقم موجود لهذا العام
            var currentYear = DateTime.Now.Year;
            var lastInvoice = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && 
                            si.InvoiceNumber.StartsWith($"SI{currentYear}") &&
                            si.InvoiceDate.Year == currentYear)
                .OrderByDescending(si => si.InvoiceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastInvoice != null)
            {
                string numberPart = lastInvoice.InvoiceNumber.Substring($"SI{currentYear}".Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"SI{currentYear}{nextNumber:000000}";
        }

        /// <summary>
        /// الحصول على إحصائيات فواتير المبيعات
        /// Get sales invoice statistics
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>إحصائيات فواتير المبيعات</returns>
        public async Task<SalesInvoiceStatistics> GetSalesInvoiceStatisticsAsync(int companyId)
        {
            using var context = CreateDbContext();

            var today = DateTime.Today;
            var thisMonth = new DateTime(today.Year, today.Month, 1);
            var thisYear = new DateTime(today.Year, 1, 1);

            var totalInvoices = await context.SalesInvoices
                .CountAsync(si => si.CompanyId == companyId && si.IsActive);

            var totalSales = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive)
                .SumAsync(si => si.TotalAmount);

            var todaySales = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive && 
                            si.InvoiceDate.Date == today)
                .SumAsync(si => si.TotalAmount);

            var monthSales = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive && 
                            si.InvoiceDate >= thisMonth)
                .SumAsync(si => si.TotalAmount);

            var yearSales = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive && 
                            si.InvoiceDate >= thisYear)
                .SumAsync(si => si.TotalAmount);

            return new SalesInvoiceStatistics
            {
                TotalInvoices = totalInvoices,
                TotalSales = totalSales,
                TodaySales = todaySales,
                MonthSales = monthSales,
                YearSales = yearSales
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات فاتورة المبيعات
        /// Validate sales invoice data
        /// </summary>
        /// <param name="salesInvoice">بيانات الفاتورة</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateSalesInvoice(SalesInvoice salesInvoice)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(salesInvoice.InvoiceNumber))
                errors.Add("رقم الفاتورة مطلوب");

            if (salesInvoice.CustomerId <= 0)
                errors.Add("يجب اختيار العميل");

            if (salesInvoice.WarehouseId <= 0)
                errors.Add("يجب اختيار المخزن");

            if (salesInvoice.InvoiceDate == default)
                errors.Add("تاريخ الفاتورة مطلوب");

            if (salesInvoice.TaxRate < 0 || salesInvoice.TaxRate > 100)
                errors.Add("نسبة الضريبة يجب أن تكون بين 0 و 100");

            if (salesInvoice.DiscountRate < 0 || salesInvoice.DiscountRate > 100)
                errors.Add("نسبة الخصم يجب أن تكون بين 0 و 100");

            if (salesInvoice.SalesInvoiceItems == null || !salesInvoice.SalesInvoiceItems.Any())
                errors.Add("يجب إضافة بند واحد على الأقل للفاتورة");

            if (salesInvoice.SalesInvoiceItems != null)
            {
                foreach (var item in salesInvoice.SalesInvoiceItems)
                {
                    if (item.ProductId <= 0)
                        errors.Add("يجب اختيار الصنف لجميع البنود");

                    if (item.Quantity <= 0)
                        errors.Add("الكمية يجب أن تكون أكبر من صفر");

                    if (item.UnitPrice < 0)
                        errors.Add("سعر الوحدة لا يمكن أن يكون سالباً");
                }
            }

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }

    /// <summary>
    /// إحصائيات فواتير المبيعات
    /// Sales Invoice Statistics
    /// </summary>
    public class SalesInvoiceStatistics
    {
        public int TotalInvoices { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TodaySales { get; set; }
        public decimal MonthSales { get; set; }
        public decimal YearSales { get; set; }
    }
}
