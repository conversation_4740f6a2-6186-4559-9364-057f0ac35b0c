-- ===================================================================
-- نظام جود للمحاسبة المالية - قاعدة البيانات - الجزء الثاني
-- Joud Accounting System Database - Part 2
-- ===================================================================

USE [JoudAccountingDB]
GO

-- ===================================================================
-- جدول فواتير المبيعات - Sales Invoices
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoices' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[SalesInvoices] (
        [SalesInvoiceId] INT IDENTITY(1,1) PRIMARY KEY,
        [InvoiceNumber] NVARCHAR(50) NOT NULL UNIQUE,
        [InvoiceDate] DATETIME2 NOT NULL,
        [CustomerId] INT NOT NULL,
        [WarehouseId] INT NOT NULL,
        [SubTotal] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [DiscountAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [DiscountPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TaxAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TaxPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TotalAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [PaidAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [RemainingAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [PaymentStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending', -- Pending, Partial, Paid
        [Notes] NVARCHAR(1000) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_SalesInvoices_Customers FOREIGN KEY (CustomerId) REFERENCES Customers(CustomerId),
        CONSTRAINT FK_SalesInvoices_Warehouses FOREIGN KEY (WarehouseId) REFERENCES Warehouses(WarehouseId),
        CONSTRAINT FK_SalesInvoices_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_SalesInvoices_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول تفاصيل فواتير المبيعات - Sales Invoice Details
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoiceDetails' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[SalesInvoiceDetails] (
        [SalesInvoiceDetailId] INT IDENTITY(1,1) PRIMARY KEY,
        [SalesInvoiceId] INT NOT NULL,
        [ProductId] INT NOT NULL,
        [Quantity] DECIMAL(18,2) NOT NULL,
        [UnitPrice] DECIMAL(18,2) NOT NULL,
        [DiscountAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [DiscountPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TaxAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TaxPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [TotalAmount] DECIMAL(18,2) NOT NULL,
        [Notes] NVARCHAR(500) NULL,
        CONSTRAINT FK_SalesInvoiceDetails_SalesInvoices FOREIGN KEY (SalesInvoiceId) REFERENCES SalesInvoices(SalesInvoiceId) ON DELETE CASCADE,
        CONSTRAINT FK_SalesInvoiceDetails_Products FOREIGN KEY (ProductId) REFERENCES Products(ProductId)
    )
END
GO

-- ===================================================================
-- جدول أنواع الحسابات - Account Types
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AccountTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[AccountTypes] (
        [AccountTypeId] INT IDENTITY(1,1) PRIMARY KEY,
        [TypeCode] NVARCHAR(20) NOT NULL UNIQUE,
        [TypeName] NVARCHAR(200) NOT NULL,
        [TypeNameEn] NVARCHAR(200) NULL,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE()
    )
END
GO

-- ===================================================================
-- جدول الحسابات - Accounts
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Accounts' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Accounts] (
        [AccountId] INT IDENTITY(1,1) PRIMARY KEY,
        [AccountCode] NVARCHAR(20) NOT NULL UNIQUE,
        [AccountName] NVARCHAR(200) NOT NULL,
        [AccountNameEn] NVARCHAR(200) NULL,
        [AccountTypeId] INT NOT NULL,
        [ParentAccountId] INT NULL,
        [CurrentBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Accounts_AccountTypes FOREIGN KEY (AccountTypeId) REFERENCES AccountTypes(AccountTypeId),
        CONSTRAINT FK_Accounts_ParentAccount FOREIGN KEY (ParentAccountId) REFERENCES Accounts(AccountId),
        CONSTRAINT FK_Accounts_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_Accounts_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول أنواع المصاريف - Expense Types
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ExpenseTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ExpenseTypes] (
        [ExpenseTypeId] INT IDENTITY(1,1) PRIMARY KEY,
        [TypeCode] NVARCHAR(20) NOT NULL UNIQUE,
        [TypeName] NVARCHAR(200) NOT NULL,
        [TypeNameEn] NVARCHAR(200) NULL,
        [Description] NVARCHAR(500) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_ExpenseTypes_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_ExpenseTypes_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول المصاريف - Expenses
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Expenses' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Expenses] (
        [ExpenseId] INT IDENTITY(1,1) PRIMARY KEY,
        [ExpenseNumber] NVARCHAR(50) NOT NULL UNIQUE,
        [ExpenseDate] DATETIME2 NOT NULL,
        [ExpenseTypeId] INT NOT NULL,
        [Amount] DECIMAL(18,2) NOT NULL,
        [Description] NVARCHAR(1000) NULL,
        [PaymentMethod] NVARCHAR(50) NULL, -- Cash, Bank, Credit
        [ReferenceNumber] NVARCHAR(100) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Expenses_ExpenseTypes FOREIGN KEY (ExpenseTypeId) REFERENCES ExpenseTypes(ExpenseTypeId),
        CONSTRAINT FK_Expenses_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_Expenses_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول أنواع الإيرادات - Revenue Types
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RevenueTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[RevenueTypes] (
        [RevenueTypeId] INT IDENTITY(1,1) PRIMARY KEY,
        [TypeCode] NVARCHAR(20) NOT NULL UNIQUE,
        [TypeName] NVARCHAR(200) NOT NULL,
        [TypeNameEn] NVARCHAR(200) NULL,
        [Description] NVARCHAR(500) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_RevenueTypes_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_RevenueTypes_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول الإيرادات - Revenues
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Revenues' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Revenues] (
        [RevenueId] INT IDENTITY(1,1) PRIMARY KEY,
        [RevenueNumber] NVARCHAR(50) NOT NULL UNIQUE,
        [RevenueDate] DATETIME2 NOT NULL,
        [RevenueTypeId] INT NOT NULL,
        [Amount] DECIMAL(18,2) NOT NULL,
        [Description] NVARCHAR(1000) NULL,
        [PaymentMethod] NVARCHAR(50) NULL, -- Cash, Bank, Credit
        [ReferenceNumber] NVARCHAR(100) NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_Revenues_RevenueTypes FOREIGN KEY (RevenueTypeId) REFERENCES RevenueTypes(RevenueTypeId),
        CONSTRAINT FK_Revenues_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_Revenues_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO

-- ===================================================================
-- جدول المعاملات المالية - Financial Transactions
-- ===================================================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FinancialTransactions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[FinancialTransactions] (
        [TransactionId] INT IDENTITY(1,1) PRIMARY KEY,
        [TransactionNumber] NVARCHAR(50) NOT NULL UNIQUE,
        [TransactionDate] DATETIME2 NOT NULL,
        [TransactionType] NVARCHAR(50) NOT NULL, -- Debit, Credit
        [AccountId] INT NOT NULL,
        [Amount] DECIMAL(18,2) NOT NULL,
        [Description] NVARCHAR(1000) NULL,
        [ReferenceType] NVARCHAR(50) NULL, -- Invoice, Expense, Revenue, Manual
        [ReferenceId] INT NULL,
        [CompanyId] INT NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedDate] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        CONSTRAINT FK_FinancialTransactions_Accounts FOREIGN KEY (AccountId) REFERENCES Accounts(AccountId),
        CONSTRAINT FK_FinancialTransactions_Companies FOREIGN KEY (CompanyId) REFERENCES Companies(CompanyId),
        CONSTRAINT FK_FinancialTransactions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
    )
END
GO
