namespace Joud.Models
{
    /// <summary>
    /// تقرير المبيعات
    /// Sales Report
    /// </summary>
    public class SalesReport
    {
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public int TotalInvoices { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalQuantity { get; set; }
        public List<CustomerSalesData> TopCustomers { get; set; } = new();
        public List<ProductSalesData> TopProducts { get; set; } = new();
        public List<DailySalesData> DailySales { get; set; } = new();
    }

    /// <summary>
    /// بيانات مبيعات العميل
    /// Customer Sales Data
    /// </summary>
    public class CustomerSalesData
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
    }

    /// <summary>
    /// بيانات مبيعات الصنف
    /// Product Sales Data
    /// </summary>
    public class ProductSalesData
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal TotalQuantity { get; set; }
        public decimal TotalSales { get; set; }
    }

    /// <summary>
    /// بيانات المبيعات اليومية
    /// Daily Sales Data
    /// </summary>
    public class DailySalesData
    {
        public DateTime Date { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
    }

    /// <summary>
    /// تقرير المشتريات
    /// Purchase Report
    /// </summary>
    public class PurchaseReport
    {
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public int TotalInvoices { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalQuantity { get; set; }
        public List<SupplierPurchaseData> TopSuppliers { get; set; } = new();
        public List<ProductPurchaseData> TopProducts { get; set; } = new();
        public List<DailyPurchaseData> DailyPurchases { get; set; } = new();
    }

    /// <summary>
    /// بيانات مشتريات المورد
    /// Supplier Purchase Data
    /// </summary>
    public class SupplierPurchaseData
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public decimal TotalPurchases { get; set; }
        public int InvoiceCount { get; set; }
    }

    /// <summary>
    /// بيانات مشتريات الصنف
    /// Product Purchase Data
    /// </summary>
    public class ProductPurchaseData
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal TotalQuantity { get; set; }
        public decimal TotalPurchases { get; set; }
    }

    /// <summary>
    /// بيانات المشتريات اليومية
    /// Daily Purchase Data
    /// </summary>
    public class DailyPurchaseData
    {
        public DateTime Date { get; set; }
        public decimal TotalPurchases { get; set; }
        public int InvoiceCount { get; set; }
    }

    /// <summary>
    /// تقرير أرصدة العملاء
    /// Customer Balance Report
    /// </summary>
    public class CustomerBalanceReport
    {
        public List<CustomerBalanceData> Customers { get; set; } = new();
        public decimal TotalBalance { get; set; }
        public int CustomersWithBalance { get; set; }
        public int CustomersOverLimit { get; set; }
    }

    /// <summary>
    /// بيانات رصيد العميل
    /// Customer Balance Data
    /// </summary>
    public class CustomerBalanceData
    {
        public int CustomerId { get; set; }
        public string CustomerCode { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; }
        public decimal CreditLimit { get; set; }
    }

    /// <summary>
    /// تقرير أرصدة الموردين
    /// Supplier Balance Report
    /// </summary>
    public class SupplierBalanceReport
    {
        public List<SupplierBalanceData> Suppliers { get; set; } = new();
        public decimal TotalBalance { get; set; }
        public int SuppliersWithBalance { get; set; }
        public int SuppliersOverLimit { get; set; }
    }

    /// <summary>
    /// بيانات رصيد المورد
    /// Supplier Balance Data
    /// </summary>
    public class SupplierBalanceData
    {
        public int SupplierId { get; set; }
        public string SupplierCode { get; set; } = string.Empty;
        public string SupplierName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; }
        public decimal CreditLimit { get; set; }
    }

    /// <summary>
    /// تقرير المخزون
    /// Inventory Report
    /// </summary>
    public class InventoryReport
    {
        public List<ProductInventoryData> Products { get; set; } = new();
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public int OutOfStockProducts { get; set; }
        public decimal TotalInventoryValue { get; set; }
    }

    /// <summary>
    /// بيانات مخزون الصنف
    /// Product Inventory Data
    /// </summary>
    public class ProductInventoryData
    {
        public int ProductId { get; set; }
        public string ProductCode { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string MainCategoryName { get; set; } = string.Empty;
        public string SubCategoryName { get; set; } = string.Empty;
        public string UnitName { get; set; } = string.Empty;
        public string WarehouseName { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal TotalValue { get; set; }
    }

    /// <summary>
    /// تقرير حركة المخزون
    /// Inventory Movement Report
    /// </summary>
    public class InventoryMovementReport
    {
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public int? ProductId { get; set; }
        public List<InventoryMovementData> Movements { get; set; } = new();
        public decimal TotalQuantityIn { get; set; }
        public decimal TotalQuantityOut { get; set; }
        public decimal TotalValueIn { get; set; }
        public decimal TotalValueOut { get; set; }
    }

    /// <summary>
    /// بيانات حركة المخزون
    /// Inventory Movement Data
    /// </summary>
    public class InventoryMovementData
    {
        public DateTime Date { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string MovementType { get; set; } = string.Empty; // مبيعات، مشتريات، تسوية
        public string ReferenceNumber { get; set; } = string.Empty; // رقم الفاتورة
        public string ReferenceName { get; set; } = string.Empty; // اسم العميل أو المورد
        public decimal QuantityIn { get; set; }
        public decimal QuantityOut { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Total { get; set; }
    }

    /// <summary>
    /// تقرير الربحية
    /// Profitability Report
    /// </summary>
    public class ProfitabilityReport
    {
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalCostOfSales { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public List<ProductProfitabilityData> ProductProfitability { get; set; } = new();
    }

    /// <summary>
    /// بيانات ربحية الصنف
    /// Product Profitability Data
    /// </summary>
    public class ProductProfitabilityData
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal QuantitySold { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalCost { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal ProfitMargin { get; set; }
    }

    /// <summary>
    /// التقرير المالي
    /// Financial Report
    /// </summary>
    public class FinancialReport
    {
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal CashSales { get; set; }
        public decimal CreditSales { get; set; }
        public decimal OtherRevenue { get; set; }
        public decimal CostOfGoodsSold { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal OtherExpenses { get; set; }
        public decimal TotalAssets { get; set; }
        public decimal TotalLiabilities { get; set; }
        public decimal Equity { get; set; }
        public List<AccountBalanceData> AccountBalances { get; set; } = new();
    }

    /// <summary>
    /// بيانات رصيد الحساب
    /// Account Balance Data
    /// </summary>
    public class AccountBalanceData
    {
        public int AccountId { get; set; }
        public string AccountCode { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public decimal DebitBalance { get; set; }
        public decimal CreditBalance { get; set; }
        public decimal NetBalance { get; set; }
    }

    /// <summary>
    /// ملخص لوحة المعلومات
    /// Dashboard Summary
    /// </summary>
    public class DashboardSummary
    {
        public decimal TodaySales { get; set; }
        public decimal TodayPurchases { get; set; }
        public decimal MonthSales { get; set; }
        public decimal MonthPurchases { get; set; }
        public decimal YearSales { get; set; }
        public decimal YearPurchases { get; set; }
        public int TotalCustomers { get; set; }
        public int TotalSuppliers { get; set; }
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public decimal TotalCustomerBalance { get; set; }
        public decimal TotalSupplierBalance { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public List<DailySalesData> Last7DaysSales { get; set; } = new();
        public List<DailyPurchaseData> Last7DaysPurchases { get; set; } = new();
        public List<CustomerSalesData> TopCustomers { get; set; } = new();
        public List<ProductSalesData> TopProducts { get; set; } = new();
    }
}
