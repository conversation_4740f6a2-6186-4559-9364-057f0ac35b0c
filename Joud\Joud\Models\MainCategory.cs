using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات الفئات الرئيسية
    /// Main Category Data Model
    /// </summary>
    [Table("MainCategories")]
    public class MainCategory
    {
        [Key]
        public int MainCategoryId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "كود الفئة")]
        public string CategoryCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الفئة")]
        public string CategoryName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "اسم الفئة بالإنجليزية")]
        public string? CategoryNameEn { get; set; }

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "الشركة")]
        public int CompanyId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "أنشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "عدل بواسطة")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        [ForeignKey("CompanyId")]
        public virtual Company Company { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User CreatedByUser { get; set; } = null!;

        [ForeignKey("ModifiedBy")]
        public virtual User? ModifiedByUser { get; set; }

        // Collections
        public virtual ICollection<SubCategory> SubCategories { get; set; } = new List<SubCategory>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
