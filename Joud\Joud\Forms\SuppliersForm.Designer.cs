namespace Joud.Forms
{
    partial class SuppliersForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            // نفس التصميم كما في CustomersForm ولكن مع تغيير الأسماء للموردين
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.btnRefresh = new Button();
            
            this.pnlStatistics = new Panel();
            this.lblTotalSuppliers = new Label();
            this.lblSuppliersWithBalance = new Label();
            this.lblTotalBalance = new Label();
            this.lblNewSuppliers = new Label();
            
            this.pnlMain = new Panel();
            this.dgvSuppliers = new DataGridView();
            
            this.pnlDetails = new Panel();
            this.grpSupplierDetails = new GroupBox();
            this.lblSupplierCode = new Label();
            this.txtSupplierCode = new TextBox();
            this.lblSupplierName = new Label();
            this.txtSupplierName = new TextBox();
            this.lblSupplierNameEn = new Label();
            this.txtSupplierNameEn = new TextBox();
            this.lblPhone = new Label();
            this.txtPhone = new TextBox();
            this.lblMobile = new Label();
            this.txtMobile = new TextBox();
            this.lblEmail = new Label();
            this.txtEmail = new TextBox();
            this.lblAddress = new Label();
            this.txtAddress = new TextBox();
            this.lblCity = new Label();
            this.txtCity = new TextBox();
            this.lblCountry = new Label();
            this.txtCountry = new TextBox();
            this.lblTaxNumber = new Label();
            this.txtTaxNumber = new TextBox();
            this.lblCreditLimit = new Label();
            this.numCreditLimit = new NumericUpDown();
            this.lblCurrentBalance = new Label();
            
            this.pnlButtons = new Panel();
            this.btnNew = new Button();
            this.btnEdit = new Button();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.btnDelete = new Button();
            this.lblStatus = new Label();

            this.SuspendLayout();

            // نفس التصميم كما في CustomersForm مع تغيير النصوص للموردين
            // Header
            this.pnlHeader.BackColor = Color.FromArgb(52, 73, 94);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Controls.Add(this.txtSearch);
            this.pnlHeader.Controls.Add(this.lblSearch);
            this.pnlHeader.Controls.Add(this.btnRefresh);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Size = new Size(1200, 60);

            this.lblTitle.Text = "إدارة الموردين";
            this.lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 15);

            this.lblSearch.Text = "البحث:";
            this.lblSearch.Font = new Font("Segoe UI", 10F);
            this.lblSearch.ForeColor = Color.White;
            this.lblSearch.Location = new Point(550, 20);

            this.txtSearch.PlaceholderText = "ابحث بالاسم أو الكود أو الهاتف...";
            this.txtSearch.Font = new Font("Segoe UI", 10F);
            this.txtSearch.Location = new Point(300, 17);
            this.txtSearch.Size = new Size(240, 25);
            this.txtSearch.TextChanged += new EventHandler(this.txtSearch_TextChanged);

            this.btnRefresh.Text = "تحديث";
            this.btnRefresh.BackColor = Color.FromArgb(39, 174, 96);
            this.btnRefresh.FlatStyle = FlatStyle.Flat;
            this.btnRefresh.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnRefresh.ForeColor = Color.White;
            this.btnRefresh.Location = new Point(200, 15);
            this.btnRefresh.Size = new Size(80, 30);
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);

            // Statistics
            this.pnlStatistics.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlStatistics.Controls.Add(this.lblTotalSuppliers);
            this.pnlStatistics.Controls.Add(this.lblSuppliersWithBalance);
            this.pnlStatistics.Controls.Add(this.lblTotalBalance);
            this.pnlStatistics.Controls.Add(this.lblNewSuppliers);
            this.pnlStatistics.Dock = DockStyle.Top;
            this.pnlStatistics.Location = new Point(0, 60);
            this.pnlStatistics.Size = new Size(1200, 40);

            this.lblTotalSuppliers.Text = "إجمالي الموردين: 0";
            this.lblTotalSuppliers.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalSuppliers.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalSuppliers.Location = new Point(950, 12);

            this.lblSuppliersWithBalance.Text = "موردين لديهم رصيد: 0";
            this.lblSuppliersWithBalance.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblSuppliersWithBalance.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblSuppliersWithBalance.Location = new Point(750, 12);

            this.lblTotalBalance.Text = "إجمالي الأرصدة: 0.00";
            this.lblTotalBalance.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalBalance.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalBalance.Location = new Point(500, 12);

            this.lblNewSuppliers.Text = "موردين جدد هذا الشهر: 0";
            this.lblNewSuppliers.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblNewSuppliers.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblNewSuppliers.Location = new Point(250, 12);

            // Main Panel with DataGridView
            this.pnlMain.Controls.Add(this.dgvSuppliers);
            this.pnlMain.Dock = DockStyle.Left;
            this.pnlMain.Location = new Point(0, 100);
            this.pnlMain.Size = new Size(700, 540);

            this.dgvSuppliers.BackgroundColor = Color.White;
            this.dgvSuppliers.BorderStyle = BorderStyle.None;
            this.dgvSuppliers.Dock = DockStyle.Fill;

            // Details Panel
            this.pnlDetails.BackColor = Color.FromArgb(240, 244, 247);
            this.pnlDetails.Controls.Add(this.grpSupplierDetails);
            this.pnlDetails.Dock = DockStyle.Fill;
            this.pnlDetails.Location = new Point(700, 100);
            this.pnlDetails.Size = new Size(500, 540);

            this.grpSupplierDetails.Text = "تفاصيل المورد";
            this.grpSupplierDetails.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpSupplierDetails.Location = new Point(20, 20);
            this.grpSupplierDetails.Size = new Size(460, 420);
            this.grpSupplierDetails.ForeColor = Color.FromArgb(52, 73, 94);

            // إضافة جميع عناصر التحكم إلى GroupBox
            this.grpSupplierDetails.Controls.Add(this.lblSupplierCode);
            this.grpSupplierDetails.Controls.Add(this.txtSupplierCode);
            this.grpSupplierDetails.Controls.Add(this.lblSupplierName);
            this.grpSupplierDetails.Controls.Add(this.txtSupplierName);
            this.grpSupplierDetails.Controls.Add(this.lblSupplierNameEn);
            this.grpSupplierDetails.Controls.Add(this.txtSupplierNameEn);
            this.grpSupplierDetails.Controls.Add(this.lblPhone);
            this.grpSupplierDetails.Controls.Add(this.txtPhone);
            this.grpSupplierDetails.Controls.Add(this.lblMobile);
            this.grpSupplierDetails.Controls.Add(this.txtMobile);
            this.grpSupplierDetails.Controls.Add(this.lblEmail);
            this.grpSupplierDetails.Controls.Add(this.txtEmail);
            this.grpSupplierDetails.Controls.Add(this.lblAddress);
            this.grpSupplierDetails.Controls.Add(this.txtAddress);
            this.grpSupplierDetails.Controls.Add(this.lblCity);
            this.grpSupplierDetails.Controls.Add(this.txtCity);
            this.grpSupplierDetails.Controls.Add(this.lblCountry);
            this.grpSupplierDetails.Controls.Add(this.txtCountry);
            this.grpSupplierDetails.Controls.Add(this.lblTaxNumber);
            this.grpSupplierDetails.Controls.Add(this.txtTaxNumber);
            this.grpSupplierDetails.Controls.Add(this.lblCreditLimit);
            this.grpSupplierDetails.Controls.Add(this.numCreditLimit);
            this.grpSupplierDetails.Controls.Add(this.lblCurrentBalance);

            // تفاصيل مواضع العناصر داخل GroupBox
            // كود المورد
            this.lblSupplierCode.Text = "كود المورد:";
            this.lblSupplierCode.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblSupplierCode.Location = new Point(350, 30);
            this.lblSupplierCode.Size = new Size(80, 20);

            this.txtSupplierCode.Location = new Point(200, 30);
            this.txtSupplierCode.Size = new Size(140, 23);
            this.txtSupplierCode.ReadOnly = true;
            this.txtSupplierCode.BackColor = Color.FromArgb(236, 240, 241);

            // اسم المورد
            this.lblSupplierName.Text = "اسم المورد:";
            this.lblSupplierName.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblSupplierName.Location = new Point(350, 60);
            this.lblSupplierName.Size = new Size(80, 20);

            this.txtSupplierName.Location = new Point(50, 60);
            this.txtSupplierName.Size = new Size(290, 23);

            // اسم المورد بالإنجليزية
            this.lblSupplierNameEn.Text = "الاسم بالإنجليزية:";
            this.lblSupplierNameEn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblSupplierNameEn.Location = new Point(350, 90);
            this.lblSupplierNameEn.Size = new Size(100, 20);

            this.txtSupplierNameEn.Location = new Point(50, 90);
            this.txtSupplierNameEn.Size = new Size(290, 23);

            // الهاتف
            this.lblPhone.Text = "الهاتف:";
            this.lblPhone.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblPhone.Location = new Point(350, 120);
            this.lblPhone.Size = new Size(50, 20);

            this.txtPhone.Location = new Point(200, 120);
            this.txtPhone.Size = new Size(140, 23);

            // الجوال
            this.lblMobile.Text = "الجوال:";
            this.lblMobile.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblMobile.Location = new Point(130, 120);
            this.lblMobile.Size = new Size(50, 20);

            this.txtMobile.Location = new Point(50, 120);
            this.txtMobile.Size = new Size(70, 23);

            // البريد الإلكتروني
            this.lblEmail.Text = "البريد الإلكتروني:";
            this.lblEmail.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblEmail.Location = new Point(350, 150);
            this.lblEmail.Size = new Size(100, 20);

            this.txtEmail.Location = new Point(50, 150);
            this.txtEmail.Size = new Size(290, 23);

            // العنوان
            this.lblAddress.Text = "العنوان:";
            this.lblAddress.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblAddress.Location = new Point(350, 180);
            this.lblAddress.Size = new Size(60, 20);

            this.txtAddress.Location = new Point(50, 180);
            this.txtAddress.Size = new Size(290, 23);
            this.txtAddress.Multiline = true;
            this.txtAddress.Height = 40;

            // المدينة
            this.lblCity.Text = "المدينة:";
            this.lblCity.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCity.Location = new Point(350, 230);
            this.lblCity.Size = new Size(60, 20);

            this.txtCity.Location = new Point(200, 230);
            this.txtCity.Size = new Size(140, 23);

            // البلد
            this.lblCountry.Text = "البلد:";
            this.lblCountry.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCountry.Location = new Point(130, 230);
            this.lblCountry.Size = new Size(50, 20);

            this.txtCountry.Location = new Point(50, 230);
            this.txtCountry.Size = new Size(70, 23);

            // الرقم الضريبي
            this.lblTaxNumber.Text = "الرقم الضريبي:";
            this.lblTaxNumber.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTaxNumber.Location = new Point(350, 260);
            this.lblTaxNumber.Size = new Size(90, 20);

            this.txtTaxNumber.Location = new Point(200, 260);
            this.txtTaxNumber.Size = new Size(140, 23);

            // الحد الائتماني
            this.lblCreditLimit.Text = "الحد الائتماني:";
            this.lblCreditLimit.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCreditLimit.Location = new Point(350, 290);
            this.lblCreditLimit.Size = new Size(90, 20);

            this.numCreditLimit.Location = new Point(200, 290);
            this.numCreditLimit.Size = new Size(140, 23);
            this.numCreditLimit.DecimalPlaces = 2;
            this.numCreditLimit.Maximum = *********;
            this.numCreditLimit.ThousandsSeparator = true;

            // الرصيد الحالي
            this.lblCurrentBalance.Text = "الرصيد الحالي: 0.00";
            this.lblCurrentBalance.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCurrentBalance.ForeColor = Color.FromArgb(39, 174, 96);
            this.lblCurrentBalance.Location = new Point(200, 320);
            this.lblCurrentBalance.Size = new Size(200, 20);

            // Buttons Panel
            this.pnlButtons.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlButtons.Controls.Add(this.btnNew);
            this.pnlButtons.Controls.Add(this.btnEdit);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.btnDelete);
            this.pnlButtons.Controls.Add(this.lblStatus);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Size = new Size(1200, 60);

            // Buttons
            this.btnNew.Text = "جديد";
            this.btnNew.BackColor = Color.FromArgb(39, 174, 96);
            this.btnNew.FlatStyle = FlatStyle.Flat;
            this.btnNew.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnNew.ForeColor = Color.White;
            this.btnNew.Location = new Point(1080, 15);
            this.btnNew.Size = new Size(100, 35);
            this.btnNew.Click += new EventHandler(this.btnNew_Click);

            this.btnEdit.Text = "تعديل";
            this.btnEdit.BackColor = Color.FromArgb(41, 128, 185);
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.Location = new Point(970, 15);
            this.btnEdit.Size = new Size(100, 35);
            this.btnEdit.Click += new EventHandler(this.btnEdit_Click);

            this.btnSave.Text = "حفظ";
            this.btnSave.BackColor = Color.FromArgb(39, 174, 96);
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(860, 15);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            this.btnCancel.Text = "إلغاء";
            this.btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(750, 15);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.btnDelete.Text = "حذف";
            this.btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.Location = new Point(640, 15);
            this.btnDelete.Size = new Size(100, 35);
            this.btnDelete.Click += new EventHandler(this.btnDelete_Click);

            this.lblStatus.Text = "جاهز...";
            this.lblStatus.Font = new Font("Segoe UI", 9F);
            this.lblStatus.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblStatus.Location = new Point(20, 25);

            // Form
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.ClientSize = new Size(1200, 700);
            this.Controls.Add(this.pnlDetails);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlStatistics);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.pnlButtons);
            this.Name = "SuppliersForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة الموردين - نظام جود للمحاسبة المالية";
            this.Load += new EventHandler(this.SuppliersForm_Load);

            this.ResumeLayout(false);
        }

        #endregion

        private Panel pnlHeader;
        private Label lblTitle;
        private TextBox txtSearch;
        private Label lblSearch;
        private Button btnRefresh;
        private Panel pnlStatistics;
        private Label lblTotalSuppliers;
        private Label lblSuppliersWithBalance;
        private Label lblTotalBalance;
        private Label lblNewSuppliers;
        private Panel pnlMain;
        private DataGridView dgvSuppliers;
        private Panel pnlDetails;
        private GroupBox grpSupplierDetails;
        private Label lblSupplierCode;
        private TextBox txtSupplierCode;
        private Label lblSupplierName;
        private TextBox txtSupplierName;
        private Label lblSupplierNameEn;
        private TextBox txtSupplierNameEn;
        private Label lblPhone;
        private TextBox txtPhone;
        private Label lblMobile;
        private TextBox txtMobile;
        private Label lblEmail;
        private TextBox txtEmail;
        private Label lblAddress;
        private TextBox txtAddress;
        private Label lblCity;
        private TextBox txtCity;
        private Label lblCountry;
        private TextBox txtCountry;
        private Label lblTaxNumber;
        private TextBox txtTaxNumber;
        private Label lblCreditLimit;
        private NumericUpDown numCreditLimit;
        private Label lblCurrentBalance;
        private Panel pnlButtons;
        private Button btnNew;
        private Button btnEdit;
        private Button btnSave;
        private Button btnCancel;
        private Button btnDelete;
        private Label lblStatus;
    }
}
