using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة الفئات الفرعية
    /// Sub Category Management Service
    /// </summary>
    public class SubCategoryService
    {
        private readonly string _connectionString;

        public SubCategoryService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// الحصول على جميع الفئات الفرعية للشركة
        /// Get all sub categories for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الفئات الفرعية</returns>
        public async Task<List<SubCategory>> GetAllSubCategoriesAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.SubCategories
                .Where(sc => sc.CompanyId == companyId && sc.IsActive)
                .Include(sc => sc.MainCategory)
                .Include(sc => sc.CreatedByUser)
                .Include(sc => sc.Products.Where(p => p.IsActive))
                .OrderBy(sc => sc.MainCategory.CategoryName)
                .ThenBy(sc => sc.CategoryName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الفئات الفرعية لفئة رئيسية محددة
        /// Get sub categories for specific main category
        /// </summary>
        /// <param name="mainCategoryId">معرف الفئة الرئيسية</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الفئات الفرعية</returns>
        public async Task<List<SubCategory>> GetSubCategoriesByMainCategoryAsync(int mainCategoryId, int companyId)
        {
            using var context = CreateDbContext();
            return await context.SubCategories
                .Where(sc => sc.MainCategoryId == mainCategoryId && 
                            sc.CompanyId == companyId && sc.IsActive)
                .Include(sc => sc.CreatedByUser)
                .OrderBy(sc => sc.CategoryName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على فئة فرعية بالمعرف
        /// Get sub category by ID
        /// </summary>
        /// <param name="subCategoryId">معرف الفئة الفرعية</param>
        /// <returns>بيانات الفئة الفرعية</returns>
        public async Task<SubCategory?> GetSubCategoryByIdAsync(int subCategoryId)
        {
            using var context = CreateDbContext();
            return await context.SubCategories
                .Include(sc => sc.MainCategory)
                .Include(sc => sc.CreatedByUser)
                .Include(sc => sc.Company)
                .Include(sc => sc.Products.Where(p => p.IsActive))
                .FirstOrDefaultAsync(sc => sc.SubCategoryId == subCategoryId && sc.IsActive);
        }

        /// <summary>
        /// الحصول على فئة فرعية بالكود
        /// Get sub category by code
        /// </summary>
        /// <param name="categoryCode">كود الفئة</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات الفئة الفرعية</returns>
        public async Task<SubCategory?> GetSubCategoryByCodeAsync(string categoryCode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.SubCategories
                .FirstOrDefaultAsync(sc => sc.CategoryCode == categoryCode && 
                                          sc.CompanyId == companyId && sc.IsActive);
        }

        /// <summary>
        /// البحث في الفئات الفرعية
        /// Search sub categories
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الفئات الفرعية المطابقة</returns>
        public async Task<List<SubCategory>> SearchSubCategoriesAsync(string searchTerm, int companyId)
        {
            using var context = CreateDbContext();
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllSubCategoriesAsync(companyId);

            searchTerm = searchTerm.Trim().ToLower();

            return await context.SubCategories
                .Where(sc => sc.CompanyId == companyId && sc.IsActive &&
                            (sc.CategoryName.ToLower().Contains(searchTerm) ||
                             sc.CategoryCode.ToLower().Contains(searchTerm) ||
                             (sc.CategoryNameEn != null && sc.CategoryNameEn.ToLower().Contains(searchTerm)) ||
                             (sc.Description != null && sc.Description.ToLower().Contains(searchTerm)) ||
                             sc.MainCategory.CategoryName.ToLower().Contains(searchTerm)))
                .Include(sc => sc.MainCategory)
                .Include(sc => sc.CreatedByUser)
                .Include(sc => sc.Products.Where(p => p.IsActive))
                .OrderBy(sc => sc.MainCategory.CategoryName)
                .ThenBy(sc => sc.CategoryName)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة فئة فرعية جديدة
        /// Add new sub category
        /// </summary>
        /// <param name="subCategory">بيانات الفئة الفرعية</param>
        /// <returns>معرف الفئة الفرعية الجديدة</returns>
        public async Task<int> AddSubCategoryAsync(SubCategory subCategory)
        {
            using var context = CreateDbContext();
            
            // التحقق من وجود الفئة الرئيسية
            var mainCategory = await context.MainCategories
                .FirstOrDefaultAsync(mc => mc.MainCategoryId == subCategory.MainCategoryId && 
                                          mc.CompanyId == subCategory.CompanyId && mc.IsActive);
            
            if (mainCategory == null)
                throw new InvalidOperationException("الفئة الرئيسية غير موجودة أو غير نشطة");

            // التحقق من عدم تكرار الكود
            bool codeExists = await context.SubCategories
                .AnyAsync(sc => sc.CategoryCode == subCategory.CategoryCode && 
                               sc.CompanyId == subCategory.CompanyId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود الفئة '{subCategory.CategoryCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الاسم في نفس الفئة الرئيسية
            bool nameExists = await context.SubCategories
                .AnyAsync(sc => sc.CategoryName == subCategory.CategoryName && 
                               sc.MainCategoryId == subCategory.MainCategoryId &&
                               sc.CompanyId == subCategory.CompanyId);
            
            if (nameExists)
                throw new InvalidOperationException($"اسم الفئة '{subCategory.CategoryName}' موجود مسبقاً في نفس الفئة الرئيسية");

            // إنشاء كود تلقائي إذا لم يتم تحديده
            if (string.IsNullOrEmpty(subCategory.CategoryCode))
            {
                subCategory.CategoryCode = await GenerateSubCategoryCodeAsync(subCategory.MainCategoryId, subCategory.CompanyId);
            }

            subCategory.CreatedDate = DateTime.Now;
            subCategory.IsActive = true;

            context.SubCategories.Add(subCategory);
            await context.SaveChangesAsync();

            return subCategory.SubCategoryId;
        }

        /// <summary>
        /// تحديث بيانات فئة فرعية
        /// Update sub category
        /// </summary>
        /// <param name="subCategory">بيانات الفئة الفرعية المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateSubCategoryAsync(SubCategory subCategory)
        {
            using var context = CreateDbContext();

            var existingCategory = await context.SubCategories
                .FirstOrDefaultAsync(sc => sc.SubCategoryId == subCategory.SubCategoryId);

            if (existingCategory == null)
                throw new InvalidOperationException("الفئة الفرعية غير موجودة");

            // التحقق من وجود الفئة الرئيسية
            var mainCategory = await context.MainCategories
                .FirstOrDefaultAsync(mc => mc.MainCategoryId == subCategory.MainCategoryId && 
                                          mc.CompanyId == subCategory.CompanyId && mc.IsActive);
            
            if (mainCategory == null)
                throw new InvalidOperationException("الفئة الرئيسية غير موجودة أو غير نشطة");

            // التحقق من عدم تكرار الكود مع فئات أخرى
            bool codeExists = await context.SubCategories
                .AnyAsync(sc => sc.CategoryCode == subCategory.CategoryCode && 
                               sc.CompanyId == subCategory.CompanyId &&
                               sc.SubCategoryId != subCategory.SubCategoryId);

            if (codeExists)
                throw new InvalidOperationException($"كود الفئة '{subCategory.CategoryCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الاسم في نفس الفئة الرئيسية
            bool nameExists = await context.SubCategories
                .AnyAsync(sc => sc.CategoryName == subCategory.CategoryName && 
                               sc.MainCategoryId == subCategory.MainCategoryId &&
                               sc.CompanyId == subCategory.CompanyId &&
                               sc.SubCategoryId != subCategory.SubCategoryId);

            if (nameExists)
                throw new InvalidOperationException($"اسم الفئة '{subCategory.CategoryName}' موجود مسبقاً في نفس الفئة الرئيسية");

            // تحديث البيانات
            existingCategory.CategoryCode = subCategory.CategoryCode;
            existingCategory.CategoryName = subCategory.CategoryName;
            existingCategory.CategoryNameEn = subCategory.CategoryNameEn;
            existingCategory.MainCategoryId = subCategory.MainCategoryId;
            existingCategory.Description = subCategory.Description;
            existingCategory.ModifiedDate = DateTime.Now;
            existingCategory.ModifiedBy = subCategory.ModifiedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف فئة فرعية (حذف منطقي)
        /// Delete sub category (soft delete)
        /// </summary>
        /// <param name="subCategoryId">معرف الفئة الفرعية</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteSubCategoryAsync(int subCategoryId, int deletedBy)
        {
            using var context = CreateDbContext();

            var subCategory = await context.SubCategories
                .Include(sc => sc.Products)
                .FirstOrDefaultAsync(sc => sc.SubCategoryId == subCategoryId);

            if (subCategory == null)
                throw new InvalidOperationException("الفئة الفرعية غير موجودة");

            // التحقق من عدم وجود أصناف مرتبطة
            if (subCategory.Products.Any(p => p.IsActive))
                throw new InvalidOperationException("لا يمكن حذف الفئة الفرعية لوجود أصناف مرتبطة بها");

            // حذف منطقي
            subCategory.IsActive = false;
            subCategory.ModifiedDate = DateTime.Now;
            subCategory.ModifiedBy = deletedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// إنشاء كود فئة فرعية تلقائي
        /// Generate automatic sub category code
        /// </summary>
        /// <param name="mainCategoryId">معرف الفئة الرئيسية</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>كود الفئة الفرعية الجديد</returns>
        public async Task<string> GenerateSubCategoryCodeAsync(int mainCategoryId, int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على كود الفئة الرئيسية
            var mainCategory = await context.MainCategories
                .FirstOrDefaultAsync(mc => mc.MainCategoryId == mainCategoryId);

            if (mainCategory == null)
                throw new InvalidOperationException("الفئة الرئيسية غير موجودة");

            // الحصول على أعلى رقم موجود للفئة الرئيسية
            var lastSubCategory = await context.SubCategories
                .Where(sc => sc.MainCategoryId == mainCategoryId && 
                            sc.CompanyId == companyId && 
                            sc.CategoryCode.StartsWith(mainCategory.CategoryCode))
                .OrderByDescending(sc => sc.CategoryCode)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastSubCategory != null)
            {
                string numberPart = lastSubCategory.CategoryCode.Substring(mainCategory.CategoryCode.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{mainCategory.CategoryCode}{nextNumber:00}";
        }

        /// <summary>
        /// الحصول على الفئات الرئيسية للاختيار
        /// Get main categories for selection
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الفئات الرئيسية</returns>
        public async Task<List<MainCategory>> GetMainCategoriesForSelectionAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.MainCategories
                .Where(mc => mc.CompanyId == companyId && mc.IsActive)
                .OrderBy(mc => mc.CategoryName)
                .ToListAsync();
        }

        /// <summary>
        /// التحقق من صحة بيانات الفئة الفرعية
        /// Validate sub category data
        /// </summary>
        /// <param name="subCategory">بيانات الفئة الفرعية</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateSubCategory(SubCategory subCategory)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(subCategory.CategoryName))
                errors.Add("اسم الفئة مطلوب");

            if (subCategory.CategoryName?.Length > 200)
                errors.Add("اسم الفئة يجب أن يكون أقل من 200 حرف");

            if (string.IsNullOrWhiteSpace(subCategory.CategoryCode))
                errors.Add("كود الفئة مطلوب");

            if (subCategory.CategoryCode?.Length > 20)
                errors.Add("كود الفئة يجب أن يكون أقل من 20 حرف");

            if (subCategory.MainCategoryId <= 0)
                errors.Add("يجب اختيار الفئة الرئيسية");

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }
}
