-- ===================================================================
-- تحديث الجداول - إضافة الأعمدة المفقودة
-- Update Tables - Add Missing Columns
-- ===================================================================

USE [JoudAccountingDB]
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول Companies
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'Fax')
BEGIN
    ALTER TABLE Companies ADD [Fax] NVARCHAR(50) NULL
    PRINT 'تم إضافة عمود Fax إلى جدول Companies'
END
ELSE
BEGIN
    PRINT 'عمود Fax موجود مسبقاً في جدول Companies'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'CommercialRecord')
BEGIN
    ALTER TABLE Companies ADD [CommercialRecord] NVARCHAR(50) NULL
    PRINT 'تم إضافة عمود CommercialRecord إلى جدول Companies'
END
ELSE
BEGIN
    PRINT 'عمود CommercialRecord موجود مسبقاً في جدول Companies'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'ContactPerson')
BEGIN
    ALTER TABLE Companies ADD [ContactPerson] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود ContactPerson إلى جدول Companies'
END
ELSE
BEGIN
    PRINT 'عمود ContactPerson موجود مسبقاً في جدول Companies'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'EstablishmentDate')
BEGIN
    ALTER TABLE Companies ADD [EstablishmentDate] DATETIME2 NULL
    PRINT 'تم إضافة عمود EstablishmentDate إلى جدول Companies'
END
ELSE
BEGIN
    PRINT 'عمود EstablishmentDate موجود مسبقاً في جدول Companies'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'Notes')
BEGIN
    ALTER TABLE Companies ADD [Notes] NVARCHAR(1000) NULL
    PRINT 'تم إضافة عمود Notes إلى جدول Companies'
END
ELSE
BEGIN
    PRINT 'عمود Notes موجود مسبقاً في جدول Companies'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول Users
-- ===================================================================

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'LastLoginDate')
BEGIN
    ALTER TABLE Users ADD [LastLoginDate] DATETIME2 NULL
    PRINT 'تم إضافة عمود LastLoginDate إلى جدول Users'
END
ELSE
BEGIN
    PRINT 'عمود LastLoginDate موجود مسبقاً في جدول Users'
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'RoleName')
BEGIN
    ALTER TABLE Users ADD [RoleName] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود RoleName إلى جدول Users'
END
ELSE
BEGIN
    PRINT 'عمود RoleName موجود مسبقاً في جدول Users'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول SubCategories
-- ===================================================================

-- التحقق من وجود عمود Description في SubCategories
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SubCategories' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE SubCategories ADD [Description] NVARCHAR(500) NULL
    PRINT 'تم إضافة عمود Description إلى جدول SubCategories'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول SubCategories'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول Products (إذا كانت مفقودة)
-- ===================================================================

-- التحقق من وجود عمود SalePrice في Products
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'SalePrice')
BEGIN
    -- إضافة عمود SalePrice إذا لم يكن موجود
    ALTER TABLE Products ADD [SalePrice] DECIMAL(18,2) NOT NULL DEFAULT 0
    PRINT 'تم إضافة عمود SalePrice إلى جدول Products'
END
ELSE
BEGIN
    PRINT 'عمود SalePrice موجود مسبقاً في جدول Products'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول Units
-- ===================================================================

-- التحقق من وجود عمود Description في Units
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Units' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE Units ADD [Description] NVARCHAR(500) NULL
    PRINT 'تم إضافة عمود Description إلى جدول Units'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول Units'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول MainCategories
-- ===================================================================

-- التحقق من وجود عمود Description في MainCategories
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'MainCategories' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE MainCategories ADD [Description] NVARCHAR(500) NULL
    PRINT 'تم إضافة عمود Description إلى جدول MainCategories'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول MainCategories'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول Warehouses
-- ===================================================================

-- التحقق من وجود عمود Description في Warehouses
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Warehouses' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE Warehouses ADD [Description] NVARCHAR(500) NULL
    PRINT 'تم إضافة عمود Description إلى جدول Warehouses'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول Warehouses'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول Warehouses
-- ===================================================================

-- التحقق من وجود عمود Location في Warehouses
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Warehouses' AND COLUMN_NAME = 'Location')
BEGIN
    ALTER TABLE Warehouses ADD [Location] NVARCHAR(500) NULL
    PRINT 'تم إضافة عمود Location إلى جدول Warehouses'
END
ELSE
BEGIN
    PRINT 'عمود Location موجود مسبقاً في جدول Warehouses'
END
GO

-- التحقق من وجود عمود Email في Warehouses
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Warehouses' AND COLUMN_NAME = 'Email')
BEGIN
    ALTER TABLE Warehouses ADD [Email] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود Email إلى جدول Warehouses'
END
ELSE
BEGIN
    PRINT 'عمود Email موجود مسبقاً في جدول Warehouses'
END
GO

-- التحقق من وجود عمود Capacity في Warehouses
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Warehouses' AND COLUMN_NAME = 'Capacity')
BEGIN
    ALTER TABLE Warehouses ADD [Capacity] DECIMAL(18,2) NULL
    PRINT 'تم إضافة عمود Capacity إلى جدول Warehouses'
END
ELSE
BEGIN
    PRINT 'عمود Capacity موجود مسبقاً في جدول Warehouses'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول Customers
-- ===================================================================

-- التحقق من وجود عمود Website في Customers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Website')
BEGIN
    ALTER TABLE Customers ADD [Website] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود Website إلى جدول Customers'
END
ELSE
BEGIN
    PRINT 'عمود Website موجود مسبقاً في جدول Customers'
END
GO

-- التحقق من وجود عمود Fax في Customers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Fax')
BEGIN
    ALTER TABLE Customers ADD [Fax] NVARCHAR(50) NULL
    PRINT 'تم إضافة عمود Fax إلى جدول Customers'
END
ELSE
BEGIN
    PRINT 'عمود Fax موجود مسبقاً في جدول Customers'
END
GO

-- ===================================================================
-- إضافة الأعمدة المفقودة إلى جدول Suppliers
-- ===================================================================

-- التحقق من وجود عمود Website في Suppliers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Website')
BEGIN
    ALTER TABLE Suppliers ADD [Website] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود Website إلى جدول Suppliers'
END
ELSE
BEGIN
    PRINT 'عمود Website موجود مسبقاً في جدول Suppliers'
END
GO

-- التحقق من وجود عمود Fax في Suppliers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Fax')
BEGIN
    ALTER TABLE Suppliers ADD [Fax] NVARCHAR(50) NULL
    PRINT 'تم إضافة عمود Fax إلى جدول Suppliers'
END
ELSE
BEGIN
    PRINT 'عمود Fax موجود مسبقاً في جدول Suppliers'
END
GO

-- ===================================================================
-- إضافة عمود Description لجميع الجداول المفقودة
-- ===================================================================

-- إضافة Description إلى جدول Products
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE Products ADD [Description] NVARCHAR(1000) NULL
    PRINT 'تم إضافة عمود Description إلى جدول Products'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول Products'
END
GO

-- إضافة Description إلى جدول SalesInvoices
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SalesInvoices' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE SalesInvoices ADD [Description] NVARCHAR(1000) NULL
    PRINT 'تم إضافة عمود Description إلى جدول SalesInvoices'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول SalesInvoices'
END
GO

-- إضافة Description إلى جدول PurchaseInvoices
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PurchaseInvoices' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE PurchaseInvoices ADD [Description] NVARCHAR(1000) NULL
    PRINT 'تم إضافة عمود Description إلى جدول PurchaseInvoices'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول PurchaseInvoices'
END
GO

-- إضافة Description إلى جدول SalesInvoiceDetails
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SalesInvoiceDetails' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE SalesInvoiceDetails ADD [Description] NVARCHAR(500) NULL
    PRINT 'تم إضافة عمود Description إلى جدول SalesInvoiceDetails'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول SalesInvoiceDetails'
END
GO

-- إضافة Description إلى جدول PurchaseInvoiceDetails
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PurchaseInvoiceDetails' AND COLUMN_NAME = 'Description')
BEGIN
    ALTER TABLE PurchaseInvoiceDetails ADD [Description] NVARCHAR(500) NULL
    PRINT 'تم إضافة عمود Description إلى جدول PurchaseInvoiceDetails'
END
ELSE
BEGIN
    PRINT 'عمود Description موجود مسبقاً في جدول PurchaseInvoiceDetails'
END
GO

-- ===================================================================
-- إضافة أعمدة أخرى مفقودة
-- ===================================================================

-- إضافة Email إلى جدول Customers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Email')
BEGIN
    ALTER TABLE Customers ADD [Email] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود Email إلى جدول Customers'
END
ELSE
BEGIN
    PRINT 'عمود Email موجود مسبقاً في جدول Customers'
END
GO

-- إضافة Email إلى جدول Suppliers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Email')
BEGIN
    ALTER TABLE Suppliers ADD [Email] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود Email إلى جدول Suppliers'
END
ELSE
BEGIN
    PRINT 'عمود Email موجود مسبقاً في جدول Suppliers'
END
GO

-- إضافة Mobile إلى جدول Customers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Mobile')
BEGIN
    ALTER TABLE Customers ADD [Mobile] NVARCHAR(50) NULL
    PRINT 'تم إضافة عمود Mobile إلى جدول Customers'
END
ELSE
BEGIN
    PRINT 'عمود Mobile موجود مسبقاً في جدول Customers'
END
GO

-- إضافة Mobile إلى جدول Suppliers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Mobile')
BEGIN
    ALTER TABLE Suppliers ADD [Mobile] NVARCHAR(50) NULL
    PRINT 'تم إضافة عمود Mobile إلى جدول Suppliers'
END
ELSE
BEGIN
    PRINT 'عمود Mobile موجود مسبقاً في جدول Suppliers'
END
GO

-- إضافة City إلى جدول Customers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'City')
BEGIN
    ALTER TABLE Customers ADD [City] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود City إلى جدول Customers'
END
ELSE
BEGIN
    PRINT 'عمود City موجود مسبقاً في جدول Customers'
END
GO

-- إضافة City إلى جدول Suppliers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'City')
BEGIN
    ALTER TABLE Suppliers ADD [City] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود City إلى جدول Suppliers'
END
ELSE
BEGIN
    PRINT 'عمود City موجود مسبقاً في جدول Suppliers'
END
GO

-- إضافة Country إلى جدول Customers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'Country')
BEGIN
    ALTER TABLE Customers ADD [Country] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود Country إلى جدول Customers'
END
ELSE
BEGIN
    PRINT 'عمود Country موجود مسبقاً في جدول Customers'
END
GO

-- إضافة Country إلى جدول Suppliers
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Suppliers' AND COLUMN_NAME = 'Country')
BEGIN
    ALTER TABLE Suppliers ADD [Country] NVARCHAR(100) NULL
    PRINT 'تم إضافة عمود Country إلى جدول Suppliers'
END
ELSE
BEGIN
    PRINT 'عمود Country موجود مسبقاً في جدول Suppliers'
END
GO

PRINT 'تم الانتهاء من تحديث الجداول'
