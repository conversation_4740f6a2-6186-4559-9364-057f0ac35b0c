using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة الوحدات
    /// Units Management Form
    /// </summary>
    public partial class UnitsForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly UnitService _unitService;
        private List<Unit> _units;
        private Unit? _selectedUnit;
        private bool _isEditing = false;

        public UnitsForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _unitService = new UnitService();
            _units = new List<Unit>();
            
            InitializeFormSettings();
            SetupDataGridView();
            SetupCommonUnits();
            _ = LoadUnitsAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة الوحدات - نظام جود للمحاسبة المالية";
            this.Size = new Size(1000, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private void SetupDataGridView()
        {
            // إعداد DataGridView
            dgvUnits.AutoGenerateColumns = false;
            dgvUnits.AllowUserToAddRows = false;
            dgvUnits.AllowUserToDeleteRows = false;
            dgvUnits.ReadOnly = true;
            dgvUnits.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvUnits.MultiSelect = false;
            dgvUnits.BackgroundColor = Color.White;
            dgvUnits.BorderStyle = BorderStyle.None;
            dgvUnits.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvUnits.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvUnits.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvUnits.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvUnits.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUnits.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvUnits.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitCode",
                HeaderText = "كود الوحدة",
                DataPropertyName = "UnitCode",
                Width = 100,
                ReadOnly = true
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitName",
                HeaderText = "اسم الوحدة",
                DataPropertyName = "UnitName",
                Width = 150,
                ReadOnly = true
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitNameEn",
                HeaderText = "الاسم بالإنجليزية",
                DataPropertyName = "UnitNameEn",
                Width = 150,
                ReadOnly = true
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 250,
                ReadOnly = true
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductsCount",
                HeaderText = "عدد الأصناف",
                Width = 100,
                ReadOnly = true
            });

            dgvUnits.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            // أحداث DataGridView
            dgvUnits.SelectionChanged += DgvUnits_SelectionChanged;
            dgvUnits.CellDoubleClick += DgvUnits_CellDoubleClick;
            dgvUnits.DataBindingComplete += DgvUnits_DataBindingComplete;
        }

        private void SetupCommonUnits()
        {
            // إعداد قائمة الوحدات الشائعة
            var commonUnits = _unitService.GetCommonUnits();
            cmbCommonUnits.DataSource = commonUnits;
            cmbCommonUnits.SelectedIndex = -1;
        }

        private async Task LoadUnitsAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل بيانات الوحدات...";
                lblStatus.ForeColor = Color.Blue;

                _units = await _unitService.GetAllUnitsAsync(_currentCompany.CompanyId);
                dgvUnits.DataSource = _units;

                // تحديث الإحصائيات
                await UpdateStatisticsAsync();

                lblStatus.Text = $"تم تحميل {_units.Count} وحدة";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في تحميل بيانات الوحدات: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية: {ex.InnerException.Message}";

                    // التحقق من أخطاء الأعمدة المفقودة
                    if (ex.InnerException.Message.Contains("Invalid column name"))
                    {
                        errorMessage += "\n\nيبدو أن هناك أعمدة مفقودة في قاعدة البيانات. تأكد من تنفيذ ملفات SQL المحدثة.";

                        // تحديد العمود المفقود
                        if (ex.InnerException.Message.Contains("Description"))
                        {
                            errorMessage += "\nالعمود المفقود: Description في جدول Units";
                        }
                        if (ex.InnerException.Message.Contains("SalePrice"))
                        {
                            errorMessage += "\nالعمود المفقود: SalePrice في جدول Products";
                        }
                    }
                }

                ShowError(errorMessage);

                // إنشاء قائمة فارغة لتجنب أخطاء أخرى
                _units = new List<Unit>();
                dgvUnits.DataSource = _units;
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void DgvUnits_DataBindingComplete(object? sender, DataGridViewBindingCompleteEventArgs e)
        {
            // تحديث عدد الأصناف لكل وحدة
            foreach (DataGridViewRow row in dgvUnits.Rows)
            {
                if (row.DataBoundItem is Unit unit)
                {
                    row.Cells["ProductsCount"].Value = unit.Products?.Count ?? 0;
                }
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _unitService.GetUnitStatisticsAsync(_currentCompany.CompanyId);
                lblTotalUnits.Text = $"إجمالي الوحدات: {stats.TotalUnits}";
                lblUnitsWithProducts.Text = $"وحدات تحتوي على أصناف: {stats.UnitsWithProducts}";
                lblTotalProducts.Text = $"إجمالي الأصناف: {stats.TotalProducts}";
                lblNewUnits.Text = $"وحدات جديدة هذا الشهر: {stats.NewUnitsThisMonth}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        private void DgvUnits_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvUnits.SelectedRows.Count > 0)
            {
                _selectedUnit = dgvUnits.SelectedRows[0].DataBoundItem as Unit;
                LoadUnitDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
            }
            else
            {
                _selectedUnit = null;
                ClearUnitDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void DgvUnits_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadUnitDetails()
        {
            if (_selectedUnit == null) return;

            txtUnitCode.Text = _selectedUnit.UnitCode;
            txtUnitName.Text = _selectedUnit.UnitName;
            txtUnitNameEn.Text = _selectedUnit.UnitNameEn;
            txtDescription.Text = _selectedUnit.Description;
            lblProductsCount.Text = $"عدد الأصناف: {_selectedUnit.Products?.Count ?? 0}";
        }

        private void ClearUnitDetails()
        {
            txtUnitCode.Clear();
            txtUnitName.Clear();
            txtUnitNameEn.Clear();
            txtDescription.Clear();
            lblProductsCount.Text = "عدد الأصناف: 0";
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                _isEditing = false;
                _selectedUnit = null;
                ClearUnitDetails();
                
                // إنشاء كود تلقائي
                string newCode = await _unitService.GenerateUnitCodeAsync(_currentCompany.CompanyId);
                txtUnitCode.Text = newCode;
                
                SetEditMode(true);
                txtUnitName.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء وحدة جديدة: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedUnit == null)
            {
                ShowError("يرجى اختيار وحدة للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            txtUnitName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ بيانات الوحدة...";
                lblStatus.ForeColor = Color.Blue;

                var unit = CreateUnitFromInput();

                if (_isEditing && _selectedUnit != null)
                {
                    unit.UnitId = _selectedUnit.UnitId;
                    unit.CreatedDate = _selectedUnit.CreatedDate;
                    unit.CreatedBy = _selectedUnit.CreatedBy;
                    unit.ModifiedBy = _currentUser.UserId;

                    await _unitService.UpdateUnitAsync(unit);
                    lblStatus.Text = "تم تحديث بيانات الوحدة بنجاح";
                }
                else
                {
                    unit.CreatedBy = _currentUser.UserId;
                    unit.CompanyId = _currentCompany.CompanyId;

                    await _unitService.AddUnitAsync(unit);
                    lblStatus.Text = "تم إضافة الوحدة بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadUnitsAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ بيانات الوحدة: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedUnit != null)
            {
                LoadUnitDetails();
            }
            else
            {
                ClearUnitDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedUnit == null)
            {
                ShowError("يرجى اختيار وحدة للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف الوحدة '{_selectedUnit.UnitName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف الوحدة...";
                    lblStatus.ForeColor = Color.Blue;

                    await _unitService.DeleteUnitAsync(_selectedUnit.UnitId, _currentUser.UserId);
                    
                    lblStatus.Text = "تم حذف الوحدة بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadUnitsAsync();
                    ClearUnitDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف الوحدة: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvUnits.DataSource = _units;
                }
                else
                {
                    var searchResults = await _unitService.SearchUnitsAsync(txtSearch.Text, _currentCompany.CompanyId);
                    dgvUnits.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadUnitsAsync();
        }

        private void cmbCommonUnits_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbCommonUnits.SelectedItem != null && !_isEditing)
            {
                txtUnitName.Text = cmbCommonUnits.SelectedItem.ToString();
            }
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtUnitName.Text))
                errors.Add("اسم الوحدة مطلوب");

            if (string.IsNullOrWhiteSpace(txtUnitCode.Text))
                errors.Add("كود الوحدة مطلوب");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private Unit CreateUnitFromInput()
        {
            return new Unit
            {
                UnitCode = txtUnitCode.Text.Trim(),
                UnitName = txtUnitName.Text.Trim(),
                UnitNameEn = txtUnitNameEn.Text.Trim(),
                Description = txtDescription.Text.Trim()
            };
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال
            txtUnitCode.ReadOnly = isEditing && _isEditing; // الكود للقراءة فقط عند التعديل
            txtUnitName.ReadOnly = !isEditing;
            txtUnitNameEn.ReadOnly = !isEditing;
            txtDescription.ReadOnly = !isEditing;
            cmbCommonUnits.Enabled = isEditing && !_isEditing; // الوحدات الشائعة فقط عند الإضافة

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedUnit != null;
            btnDelete.Enabled = !isEditing && _selectedUnit != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            dgvUnits.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is ComboBox || control is DataGridView)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void UnitsForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
        }
    }
}
