using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة العملاء
    /// Customer Management Service
    /// </summary>
    public class CustomerService
    {
        private readonly string _connectionString;

        public CustomerService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// الحصول على جميع العملاء للشركة
        /// Get all customers for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة العملاء</returns>
        public async Task<List<Customer>> GetAllCustomersAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.Customers
                .Where(c => c.CompanyId == companyId && c.IsActive)
                .Include(c => c.CreatedByUser)
                .OrderBy(c => c.CustomerName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على عميل بالمعرف
        /// Get customer by ID
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>بيانات العميل</returns>
        public async Task<Customer?> GetCustomerByIdAsync(int customerId)
        {
            using var context = CreateDbContext();
            return await context.Customers
                .Include(c => c.CreatedByUser)
                .Include(c => c.Company)
                .FirstOrDefaultAsync(c => c.CustomerId == customerId && c.IsActive);
        }

        /// <summary>
        /// الحصول على عميل بالكود
        /// Get customer by code
        /// </summary>
        /// <param name="customerCode">كود العميل</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات العميل</returns>
        public async Task<Customer?> GetCustomerByCodeAsync(string customerCode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.Customers
                .FirstOrDefaultAsync(c => c.CustomerCode == customerCode && 
                                         c.CompanyId == companyId && c.IsActive);
        }

        /// <summary>
        /// البحث في العملاء
        /// Search customers
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة العملاء المطابقة</returns>
        public async Task<List<Customer>> SearchCustomersAsync(string searchTerm, int companyId)
        {
            using var context = CreateDbContext();
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllCustomersAsync(companyId);

            searchTerm = searchTerm.Trim().ToLower();

            return await context.Customers
                .Where(c => c.CompanyId == companyId && c.IsActive &&
                           (c.CustomerName.ToLower().Contains(searchTerm) ||
                            c.CustomerCode.ToLower().Contains(searchTerm) ||
                            (c.Phone != null && c.Phone.Contains(searchTerm)) ||
                            (c.Mobile != null && c.Mobile.Contains(searchTerm)) ||
                            (c.Email != null && c.Email.ToLower().Contains(searchTerm))))
                .Include(c => c.CreatedByUser)
                .OrderBy(c => c.CustomerName)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة عميل جديد
        /// Add new customer
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <returns>معرف العميل الجديد</returns>
        public async Task<int> AddCustomerAsync(Customer customer)
        {
            using var context = CreateDbContext();
            
            // التحقق من عدم تكرار الكود
            bool codeExists = await context.Customers
                .AnyAsync(c => c.CustomerCode == customer.CustomerCode && 
                              c.CompanyId == customer.CompanyId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود العميل '{customer.CustomerCode}' موجود مسبقاً");

            // إنشاء كود تلقائي إذا لم يتم تحديده
            if (string.IsNullOrEmpty(customer.CustomerCode))
            {
                customer.CustomerCode = await GenerateCustomerCodeAsync(customer.CompanyId);
            }

            customer.CreatedDate = DateTime.Now;
            customer.IsActive = true;

            context.Customers.Add(customer);
            await context.SaveChangesAsync();

            return customer.CustomerId;
        }

        /// <summary>
        /// تحديث بيانات عميل
        /// Update customer
        /// </summary>
        /// <param name="customer">بيانات العميل المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            using var context = CreateDbContext();

            var existingCustomer = await context.Customers
                .FirstOrDefaultAsync(c => c.CustomerId == customer.CustomerId);

            if (existingCustomer == null)
                throw new InvalidOperationException("العميل غير موجود");

            // التحقق من عدم تكرار الكود مع عملاء آخرين
            bool codeExists = await context.Customers
                .AnyAsync(c => c.CustomerCode == customer.CustomerCode && 
                              c.CompanyId == customer.CompanyId &&
                              c.CustomerId != customer.CustomerId);

            if (codeExists)
                throw new InvalidOperationException($"كود العميل '{customer.CustomerCode}' موجود مسبقاً");

            // تحديث البيانات
            existingCustomer.CustomerCode = customer.CustomerCode;
            existingCustomer.CustomerName = customer.CustomerName;
            existingCustomer.CustomerNameEn = customer.CustomerNameEn;
            existingCustomer.Phone = customer.Phone;
            existingCustomer.Mobile = customer.Mobile;
            existingCustomer.Email = customer.Email;
            existingCustomer.Address = customer.Address;
            existingCustomer.City = customer.City;
            existingCustomer.Country = customer.Country;
            existingCustomer.TaxNumber = customer.TaxNumber;
            existingCustomer.CreditLimit = customer.CreditLimit;
            existingCustomer.ModifiedDate = DateTime.Now;
            existingCustomer.ModifiedBy = customer.ModifiedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف عميل (حذف منطقي)
        /// Delete customer (soft delete)
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteCustomerAsync(int customerId, int deletedBy)
        {
            using var context = CreateDbContext();

            var customer = await context.Customers
                .FirstOrDefaultAsync(c => c.CustomerId == customerId);

            if (customer == null)
                throw new InvalidOperationException("العميل غير موجود");

            // التحقق من عدم وجود فواتير مرتبطة
            bool hasInvoices = await context.SalesInvoices
                .AnyAsync(si => si.CustomerId == customerId);

            if (hasInvoices)
                throw new InvalidOperationException("لا يمكن حذف العميل لوجود فواتير مرتبطة به");

            // حذف منطقي
            customer.IsActive = false;
            customer.ModifiedDate = DateTime.Now;
            customer.ModifiedBy = deletedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// إنشاء كود عميل تلقائي
        /// Generate automatic customer code
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>كود العميل الجديد</returns>
        public async Task<string> GenerateCustomerCodeAsync(int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على أعلى رقم موجود
            var lastCustomer = await context.Customers
                .Where(c => c.CompanyId == companyId && c.CustomerCode.StartsWith("CUST"))
                .OrderByDescending(c => c.CustomerCode)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastCustomer != null)
            {
                string numberPart = lastCustomer.CustomerCode.Substring(4);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"CUST{nextNumber:000000}";
        }

        /// <summary>
        /// الحصول على إحصائيات العملاء
        /// Get customer statistics
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>إحصائيات العملاء</returns>
        public async Task<CustomerStatistics> GetCustomerStatisticsAsync(int companyId)
        {
            using var context = CreateDbContext();

            var totalCustomers = await context.Customers
                .CountAsync(c => c.CompanyId == companyId && c.IsActive);

            var customersWithBalance = await context.Customers
                .CountAsync(c => c.CompanyId == companyId && c.IsActive && c.CurrentBalance > 0);

            var totalBalance = await context.Customers
                .Where(c => c.CompanyId == companyId && c.IsActive)
                .SumAsync(c => c.CurrentBalance);

            var newCustomersThisMonth = await context.Customers
                .CountAsync(c => c.CompanyId == companyId && c.IsActive &&
                               c.CreatedDate.Month == DateTime.Now.Month &&
                               c.CreatedDate.Year == DateTime.Now.Year);

            return new CustomerStatistics
            {
                TotalCustomers = totalCustomers,
                CustomersWithBalance = customersWithBalance,
                TotalBalance = totalBalance,
                NewCustomersThisMonth = newCustomersThisMonth
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات العميل
        /// Validate customer data
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateCustomer(Customer customer)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(customer.CustomerName))
                errors.Add("اسم العميل مطلوب");

            if (customer.CustomerName?.Length > 200)
                errors.Add("اسم العميل يجب أن يكون أقل من 200 حرف");

            if (!string.IsNullOrEmpty(customer.Email) && !IsValidEmail(customer.Email))
                errors.Add("البريد الإلكتروني غير صحيح");

            if (customer.CreditLimit < 0)
                errors.Add("الحد الائتماني لا يمكن أن يكون سالباً");

            return errors;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }

    /// <summary>
    /// إحصائيات العملاء
    /// Customer Statistics
    /// </summary>
    public class CustomerStatistics
    {
        public int TotalCustomers { get; set; }
        public int CustomersWithBalance { get; set; }
        public decimal TotalBalance { get; set; }
        public int NewCustomersThisMonth { get; set; }
    }
}
