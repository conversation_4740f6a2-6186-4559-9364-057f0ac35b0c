using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة الأصناف
    /// Product Management Service
    /// </summary>
    public class ProductService
    {
        private readonly string _connectionString;

        public ProductService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// الحصول على جميع الأصناف للشركة
        /// Get all products for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الأصناف</returns>
        public async Task<List<Product>> GetAllProductsAsync(int companyId)
        {
            using var context = CreateDbContext();

            try
            {
                return await context.Products
                    .Where(p => p.CompanyId == companyId && p.IsActive)
                    .Include(p => p.MainCategory)
                    .Include(p => p.SubCategory)
                    .Include(p => p.Unit)
                    .Include(p => p.Warehouse)
                    .Include(p => p.CreatedByUser)
                    .OrderBy(p => p.ProductName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                // إذا فشل تحميل بسبب أعمدة مفقودة، نحاول بدون Include
                if (ex.InnerException?.Message.Contains("Invalid column name") == true)
                {
                    return await context.Products
                        .Where(p => p.CompanyId == companyId && p.IsActive)
                        .Include(p => p.CreatedByUser)
                        .OrderBy(p => p.ProductName)
                        .ToListAsync();
                }
                throw;
            }
        }

        /// <summary>
        /// الحصول على صنف بالمعرف
        /// Get product by ID
        /// </summary>
        /// <param name="productId">معرف الصنف</param>
        /// <returns>بيانات الصنف</returns>
        public async Task<Product?> GetProductByIdAsync(int productId)
        {
            using var context = CreateDbContext();
            return await context.Products
                .Include(p => p.MainCategory)
                .Include(p => p.SubCategory)
                .Include(p => p.Unit)
                .Include(p => p.Warehouse)
                .Include(p => p.CreatedByUser)
                .Include(p => p.Company)
                .FirstOrDefaultAsync(p => p.ProductId == productId && p.IsActive);
        }

        /// <summary>
        /// الحصول على صنف بالكود
        /// Get product by code
        /// </summary>
        /// <param name="productCode">كود الصنف</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات الصنف</returns>
        public async Task<Product?> GetProductByCodeAsync(string productCode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.Products
                .FirstOrDefaultAsync(p => p.ProductCode == productCode && 
                                         p.CompanyId == companyId && p.IsActive);
        }

        /// <summary>
        /// الحصول على صنف بالباركود
        /// Get product by barcode
        /// </summary>
        /// <param name="barcode">الباركود</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات الصنف</returns>
        public async Task<Product?> GetProductByBarcodeAsync(string barcode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.Products
                .FirstOrDefaultAsync(p => p.Barcode == barcode && 
                                         p.CompanyId == companyId && p.IsActive);
        }

        /// <summary>
        /// البحث في الأصناف
        /// Search products
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الأصناف المطابقة</returns>
        public async Task<List<Product>> SearchProductsAsync(string searchTerm, int companyId)
        {
            using var context = CreateDbContext();
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllProductsAsync(companyId);

            searchTerm = searchTerm.Trim().ToLower();

            return await context.Products
                .Where(p => p.CompanyId == companyId && p.IsActive &&
                           (p.ProductName.ToLower().Contains(searchTerm) ||
                            p.ProductCode.ToLower().Contains(searchTerm) ||
                            (p.ProductNameEn != null && p.ProductNameEn.ToLower().Contains(searchTerm)) ||
                            (p.Barcode != null && p.Barcode.Contains(searchTerm)) ||
                            (p.Description != null && p.Description.ToLower().Contains(searchTerm)) ||
                            p.MainCategory.CategoryName.ToLower().Contains(searchTerm) ||
                            (p.SubCategory != null && p.SubCategory.CategoryName.ToLower().Contains(searchTerm))))
                .Include(p => p.MainCategory)
                .Include(p => p.SubCategory)
                .Include(p => p.Unit)
                .Include(p => p.Warehouse)
                .Include(p => p.CreatedByUser)
                .OrderBy(p => p.ProductName)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة صنف جديد
        /// Add new product
        /// </summary>
        /// <param name="product">بيانات الصنف</param>
        /// <returns>معرف الصنف الجديد</returns>
        public async Task<int> AddProductAsync(Product product)
        {
            using var context = CreateDbContext();
            
            // التحقق من عدم تكرار الكود
            bool codeExists = await context.Products
                .AnyAsync(p => p.ProductCode == product.ProductCode && 
                              p.CompanyId == product.CompanyId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود الصنف '{product.ProductCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الباركود
            if (!string.IsNullOrEmpty(product.Barcode))
            {
                bool barcodeExists = await context.Products
                    .AnyAsync(p => p.Barcode == product.Barcode && 
                                  p.CompanyId == product.CompanyId);
                
                if (barcodeExists)
                    throw new InvalidOperationException($"الباركود '{product.Barcode}' موجود مسبقاً");
            }

            // التحقق من وجود الفئة الرئيسية
            var mainCategory = await context.MainCategories
                .FirstOrDefaultAsync(mc => mc.MainCategoryId == product.MainCategoryId && 
                                          mc.CompanyId == product.CompanyId && mc.IsActive);
            
            if (mainCategory == null)
                throw new InvalidOperationException("الفئة الرئيسية غير موجودة أو غير نشطة");

            // التحقق من وجود الفئة الفرعية إذا تم تحديدها
            if (product.SubCategoryId.HasValue)
            {
                var subCategory = await context.SubCategories
                    .FirstOrDefaultAsync(sc => sc.SubCategoryId == product.SubCategoryId &&
                                              sc.CompanyId == product.CompanyId && sc.IsActive);

                if (subCategory == null)
                    throw new InvalidOperationException("الفئة الفرعية غير موجودة أو غير نشطة");
            }

            // التحقق من وجود الوحدة
            var unit = await context.Units
                .FirstOrDefaultAsync(u => u.UnitId == product.UnitId && 
                                         u.CompanyId == product.CompanyId && u.IsActive);
            
            if (unit == null)
                throw new InvalidOperationException("الوحدة غير موجودة أو غير نشطة");

            // التحقق من وجود المخزن
            var warehouse = await context.Warehouses
                .FirstOrDefaultAsync(w => w.WarehouseId == product.WarehouseId && 
                                         w.CompanyId == product.CompanyId && w.IsActive);
            
            if (warehouse == null)
                throw new InvalidOperationException("المخزن غير موجود أو غير نشط");

            // إنشاء كود تلقائي إذا لم يتم تحديده
            if (string.IsNullOrEmpty(product.ProductCode))
            {
                product.ProductCode = await GenerateProductCodeAsync(product.CompanyId);
            }

            product.CreatedDate = DateTime.Now;
            product.IsActive = true;

            context.Products.Add(product);
            await context.SaveChangesAsync();

            return product.ProductId;
        }

        /// <summary>
        /// تحديث بيانات صنف
        /// Update product
        /// </summary>
        /// <param name="product">بيانات الصنف المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateProductAsync(Product product)
        {
            using var context = CreateDbContext();

            var existingProduct = await context.Products
                .FirstOrDefaultAsync(p => p.ProductId == product.ProductId);

            if (existingProduct == null)
                throw new InvalidOperationException("الصنف غير موجود");

            // التحقق من عدم تكرار الكود مع أصناف أخرى
            bool codeExists = await context.Products
                .AnyAsync(p => p.ProductCode == product.ProductCode && 
                              p.CompanyId == product.CompanyId &&
                              p.ProductId != product.ProductId);

            if (codeExists)
                throw new InvalidOperationException($"كود الصنف '{product.ProductCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الباركود مع أصناف أخرى
            if (!string.IsNullOrEmpty(product.Barcode))
            {
                bool barcodeExists = await context.Products
                    .AnyAsync(p => p.Barcode == product.Barcode && 
                                  p.CompanyId == product.CompanyId &&
                                  p.ProductId != product.ProductId);

                if (barcodeExists)
                    throw new InvalidOperationException($"الباركود '{product.Barcode}' موجود مسبقاً");
            }

            // تحديث البيانات
            existingProduct.ProductCode = product.ProductCode;
            existingProduct.ProductName = product.ProductName;
            existingProduct.ProductNameEn = product.ProductNameEn;
            existingProduct.Barcode = product.Barcode;
            existingProduct.MainCategoryId = product.MainCategoryId;
            existingProduct.SubCategoryId = product.SubCategoryId;
            existingProduct.UnitId = product.UnitId;
            existingProduct.WarehouseId = product.WarehouseId;
            existingProduct.PurchasePrice = product.PurchasePrice;
            existingProduct.SalePrice = product.SalePrice;
            existingProduct.MinimumStock = product.MinimumStock;
            existingProduct.Description = product.Description;
            existingProduct.ModifiedDate = DateTime.Now;
            existingProduct.ModifiedBy = product.ModifiedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف صنف (حذف منطقي)
        /// Delete product (soft delete)
        /// </summary>
        /// <param name="productId">معرف الصنف</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteProductAsync(int productId, int deletedBy)
        {
            using var context = CreateDbContext();

            var product = await context.Products
                .FirstOrDefaultAsync(p => p.ProductId == productId);

            if (product == null)
                throw new InvalidOperationException("الصنف غير موجود");

            // التحقق من عدم وجود فواتير مرتبطة
            bool hasInvoiceItems = await context.SalesInvoiceItems
                .AnyAsync(sii => sii.ProductId == productId) ||
                await context.PurchaseInvoiceItems
                .AnyAsync(pii => pii.ProductId == productId);

            if (hasInvoiceItems)
                throw new InvalidOperationException("لا يمكن حذف الصنف لوجود فواتير مرتبطة به");

            // حذف منطقي
            product.IsActive = false;
            product.ModifiedDate = DateTime.Now;
            product.ModifiedBy = deletedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// إنشاء كود صنف تلقائي
        /// Generate automatic product code
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>كود الصنف الجديد</returns>
        public async Task<string> GenerateProductCodeAsync(int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على أعلى رقم موجود
            var lastProduct = await context.Products
                .Where(p => p.CompanyId == companyId && p.ProductCode.StartsWith("PROD"))
                .OrderByDescending(p => p.ProductCode)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastProduct != null)
            {
                string numberPart = lastProduct.ProductCode.Substring(4);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"PROD{nextNumber:000000}";
        }

        /// <summary>
        /// الحصول على إحصائيات الأصناف
        /// Get product statistics
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>إحصائيات الأصناف</returns>
        public async Task<ProductStatistics> GetProductStatisticsAsync(int companyId)
        {
            using var context = CreateDbContext();

            var totalProducts = await context.Products
                .CountAsync(p => p.CompanyId == companyId && p.IsActive);

            var productsWithLowStock = await context.Products
                .CountAsync(p => p.CompanyId == companyId && p.IsActive && 
                               p.CurrentStock <= p.MinimumStock);

            var totalValue = await context.Products
                .Where(p => p.CompanyId == companyId && p.IsActive)
                .SumAsync(p => p.CurrentStock * p.PurchasePrice);

            var newProductsThisMonth = await context.Products
                .CountAsync(p => p.CompanyId == companyId && p.IsActive &&
                               p.CreatedDate.Month == DateTime.Now.Month &&
                               p.CreatedDate.Year == DateTime.Now.Year);

            return new ProductStatistics
            {
                TotalProducts = totalProducts,
                ProductsWithLowStock = productsWithLowStock,
                TotalInventoryValue = totalValue,
                NewProductsThisMonth = newProductsThisMonth
            };
        }

        /// <summary>
        /// الحصول على البيانات المرجعية للأصناف
        /// Get reference data for products
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>البيانات المرجعية</returns>
        public async Task<ProductReferenceData> GetProductReferenceDataAsync(int companyId)
        {
            using var context = CreateDbContext();

            var mainCategories = await context.MainCategories
                .Where(mc => mc.CompanyId == companyId && mc.IsActive)
                .OrderBy(mc => mc.CategoryName)
                .ToListAsync();

            var subCategories = await context.SubCategories
                .Where(sc => sc.CompanyId == companyId && sc.IsActive)
                .Include(sc => sc.MainCategory)
                .OrderBy(sc => sc.CategoryName)
                .ToListAsync();

            var units = await context.Units
                .Where(u => u.CompanyId == companyId && u.IsActive)
                .OrderBy(u => u.UnitName)
                .ToListAsync();

            var warehouses = await context.Warehouses
                .Where(w => w.CompanyId == companyId && w.IsActive)
                .OrderBy(w => w.WarehouseName)
                .ToListAsync();

            return new ProductReferenceData
            {
                MainCategories = mainCategories,
                SubCategories = subCategories,
                Units = units,
                Warehouses = warehouses
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات الصنف
        /// Validate product data
        /// </summary>
        /// <param name="product">بيانات الصنف</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateProduct(Product product)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(product.ProductName))
                errors.Add("اسم الصنف مطلوب");

            if (product.ProductName?.Length > 200)
                errors.Add("اسم الصنف يجب أن يكون أقل من 200 حرف");

            if (string.IsNullOrWhiteSpace(product.ProductCode))
                errors.Add("كود الصنف مطلوب");

            if (product.ProductCode?.Length > 50)
                errors.Add("كود الصنف يجب أن يكون أقل من 50 حرف");

            if (product.MainCategoryId <= 0)
                errors.Add("يجب اختيار الفئة الرئيسية");

            if (product.UnitId <= 0)
                errors.Add("يجب اختيار الوحدة");

            if (product.WarehouseId <= 0)
                errors.Add("يجب اختيار المخزن");

            if (product.PurchasePrice < 0)
                errors.Add("سعر الشراء لا يمكن أن يكون سالباً");

            if (product.SalePrice < 0)
                errors.Add("سعر البيع لا يمكن أن يكون سالباً");

            if (product.MinimumStock < 0)
                errors.Add("الحد الأدنى للمخزون لا يمكن أن يكون سالباً");

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }

    /// <summary>
    /// إحصائيات الأصناف
    /// Product Statistics
    /// </summary>
    public class ProductStatistics
    {
        public int TotalProducts { get; set; }
        public int ProductsWithLowStock { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public int NewProductsThisMonth { get; set; }
    }

    /// <summary>
    /// البيانات المرجعية للأصناف
    /// Product Reference Data
    /// </summary>
    public class ProductReferenceData
    {
        public List<MainCategory> MainCategories { get; set; } = new();
        public List<SubCategory> SubCategories { get; set; } = new();
        public List<Unit> Units { get; set; } = new();
        public List<Warehouse> Warehouses { get; set; } = new();
    }
}
