using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة فواتير المشتريات
    /// Purchase Invoice Management Service
    /// </summary>
    public class PurchaseInvoiceService
    {
        private readonly string _connectionString;
        private readonly JournalEntryService _journalEntryService;

        public PurchaseInvoiceService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
            _journalEntryService = new JournalEntryService();
        }

        /// <summary>
        /// الحصول على جميع فواتير المشتريات للشركة
        /// Get all purchase invoices for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة فواتير المشتريات</returns>
        public async Task<List<PurchaseInvoice>> GetAllPurchaseInvoicesAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive)
                .Include(pi => pi.Supplier)
                .Include(pi => pi.Warehouse)
                .Include(pi => pi.CreatedByUser)
                .Include(pi => pi.PurchaseInvoiceItems)
                    .ThenInclude(pii => pii.Product)
                .OrderByDescending(pi => pi.InvoiceDate)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على فاتورة مشتريات بالمعرف
        /// Get purchase invoice by ID
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <returns>بيانات الفاتورة</returns>
        public async Task<PurchaseInvoice?> GetPurchaseInvoiceByIdAsync(int invoiceId)
        {
            using var context = CreateDbContext();
            return await context.PurchaseInvoices
                .Include(pi => pi.Supplier)
                .Include(pi => pi.Warehouse)
                .Include(pi => pi.CreatedByUser)
                .Include(pi => pi.Company)
                .Include(pi => pi.PurchaseInvoiceItems)
                    .ThenInclude(pii => pii.Product)
                        .ThenInclude(p => p.Unit)
                .FirstOrDefaultAsync(pi => pi.PurchaseInvoiceId == invoiceId && pi.IsActive);
        }

        /// <summary>
        /// الحصول على فاتورة مشتريات بالرقم
        /// Get purchase invoice by number
        /// </summary>
        /// <param name="invoiceNumber">رقم الفاتورة</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات الفاتورة</returns>
        public async Task<PurchaseInvoice?> GetPurchaseInvoiceByNumberAsync(string invoiceNumber, int companyId)
        {
            using var context = CreateDbContext();
            return await context.PurchaseInvoices
                .FirstOrDefaultAsync(pi => pi.InvoiceNumber == invoiceNumber && 
                                          pi.CompanyId == companyId && pi.IsActive);
        }

        /// <summary>
        /// البحث في فواتير المشتريات
        /// Search purchase invoices
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <param name="dateFrom">من تاريخ</param>
        /// <param name="dateTo">إلى تاريخ</param>
        /// <returns>قائمة الفواتير المطابقة</returns>
        public async Task<List<PurchaseInvoice>> SearchPurchaseInvoicesAsync(string searchTerm, int companyId, 
            DateTime? dateFrom = null, DateTime? dateTo = null)
        {
            using var context = CreateDbContext();
            
            var query = context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive)
                .Include(pi => pi.Supplier)
                .Include(pi => pi.Warehouse)
                .Include(pi => pi.CreatedByUser)
                .Include(pi => pi.PurchaseInvoiceItems)
                    .ThenInclude(pii => pii.Product)
                .AsQueryable();

            // تطبيق فلتر التاريخ
            if (dateFrom.HasValue)
                query = query.Where(pi => pi.InvoiceDate >= dateFrom.Value);
            
            if (dateTo.HasValue)
                query = query.Where(pi => pi.InvoiceDate <= dateTo.Value);

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.Trim().ToLower();
                query = query.Where(pi => 
                    pi.InvoiceNumber.ToLower().Contains(searchTerm) ||
                    pi.Supplier.SupplierName.ToLower().Contains(searchTerm) ||
                    (pi.Notes != null && pi.Notes.ToLower().Contains(searchTerm)));
            }

            return await query
                .OrderByDescending(pi => pi.InvoiceDate)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة فاتورة مشتريات جديدة
        /// Add new purchase invoice
        /// </summary>
        /// <param name="purchaseInvoice">بيانات الفاتورة</param>
        /// <returns>معرف الفاتورة الجديدة</returns>
        public async Task<int> AddPurchaseInvoiceAsync(PurchaseInvoice purchaseInvoice)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // التحقق من عدم تكرار رقم الفاتورة
                bool numberExists = await context.PurchaseInvoices
                    .AnyAsync(pi => pi.InvoiceNumber == purchaseInvoice.InvoiceNumber && 
                                   pi.CompanyId == purchaseInvoice.CompanyId);
                
                if (numberExists)
                    throw new InvalidOperationException($"رقم الفاتورة '{purchaseInvoice.InvoiceNumber}' موجود مسبقاً");

                // إنشاء رقم فاتورة تلقائي إذا لم يتم تحديده
                if (string.IsNullOrEmpty(purchaseInvoice.InvoiceNumber))
                {
                    purchaseInvoice.InvoiceNumber = await GeneratePurchaseInvoiceNumberAsync(purchaseInvoice.CompanyId);
                }

                // التحقق من وجود المورد
                var supplier = await context.Suppliers
                    .FirstOrDefaultAsync(s => s.SupplierId == purchaseInvoice.SupplierId && 
                                             s.CompanyId == purchaseInvoice.CompanyId && s.IsActive);
                
                if (supplier == null)
                    throw new InvalidOperationException("المورد غير موجود أو غير نشط");

                // التحقق من وجود المخزن
                var warehouse = await context.Warehouses
                    .FirstOrDefaultAsync(w => w.WarehouseId == purchaseInvoice.WarehouseId && 
                                             w.CompanyId == purchaseInvoice.CompanyId && w.IsActive);
                
                if (warehouse == null)
                    throw new InvalidOperationException("المخزن غير موجود أو غير نشط");

                // حساب إجمالي الفاتورة
                decimal subtotal = 0;
                foreach (var item in purchaseInvoice.PurchaseInvoiceItems)
                {
                    // التحقق من وجود الصنف
                    var product = await context.Products
                        .FirstOrDefaultAsync(p => p.ProductId == item.ProductId && 
                                                 p.CompanyId == purchaseInvoice.CompanyId && p.IsActive);
                    
                    if (product == null)
                        throw new InvalidOperationException($"الصنف غير موجود أو غير نشط");

                    // حساب إجمالي البند
                    item.Total = item.Quantity * item.UnitPrice;
                    subtotal += item.Total;

                    // تحديث المخزون
                    product.CurrentStock += item.Quantity;
                    
                    // تحديث سعر الشراء للصنف
                    product.PurchasePrice = item.UnitPrice;
                }

                // حساب الضريبة والإجمالي
                purchaseInvoice.Subtotal = subtotal;
                purchaseInvoice.TaxAmount = subtotal * (purchaseInvoice.TaxRate / 100);
                purchaseInvoice.DiscountAmount = subtotal * (purchaseInvoice.DiscountRate / 100);
                purchaseInvoice.TotalAmount = subtotal + purchaseInvoice.TaxAmount - purchaseInvoice.DiscountAmount;

                purchaseInvoice.CreatedDate = DateTime.Now;
                purchaseInvoice.IsActive = true;

                // إضافة الفاتورة
                context.PurchaseInvoices.Add(purchaseInvoice);
                await context.SaveChangesAsync();

                // تحديث رصيد المورد
                supplier.CurrentBalance += purchaseInvoice.TotalAmount;

                // إنشاء القيد المحاسبي التلقائي
                try
                {
                    await _journalEntryService.CreatePurchaseInvoiceJournalEntryAsync(purchaseInvoice, purchaseInvoice.CreatedBy.ToString());
                }
                catch (Exception ex)
                {
                    // تسجيل الخطأ ولكن لا نوقف العملية
                    // يمكن إنشاء القيد لاحقاً يدوياً
                    System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء القيد المحاسبي: {ex.Message}");
                }

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return purchaseInvoice.PurchaseInvoiceId;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// تحديث فاتورة مشتريات
        /// Update purchase invoice
        /// </summary>
        /// <param name="purchaseInvoice">بيانات الفاتورة المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdatePurchaseInvoiceAsync(PurchaseInvoice purchaseInvoice)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var existingInvoice = await context.PurchaseInvoices
                    .Include(pi => pi.PurchaseInvoiceItems)
                    .Include(pi => pi.Supplier)
                    .FirstOrDefaultAsync(pi => pi.PurchaseInvoiceId == purchaseInvoice.PurchaseInvoiceId);

                if (existingInvoice == null)
                    throw new InvalidOperationException("الفاتورة غير موجودة");

                // استرداد المخزون من الفاتورة القديمة
                foreach (var oldItem in existingInvoice.PurchaseInvoiceItems)
                {
                    var product = await context.Products
                        .FirstOrDefaultAsync(p => p.ProductId == oldItem.ProductId);
                    
                    if (product != null)
                    {
                        product.CurrentStock -= oldItem.Quantity;
                    }
                }

                // تحديث رصيد المورد (استرداد المبلغ القديم)
                existingInvoice.Supplier.CurrentBalance -= existingInvoice.TotalAmount;

                // حذف البنود القديمة
                context.PurchaseInvoiceItems.RemoveRange(existingInvoice.PurchaseInvoiceItems);

                // تحديث بيانات الفاتورة
                existingInvoice.InvoiceNumber = purchaseInvoice.InvoiceNumber;
                existingInvoice.InvoiceDate = purchaseInvoice.InvoiceDate;
                existingInvoice.SupplierId = purchaseInvoice.SupplierId;
                existingInvoice.WarehouseId = purchaseInvoice.WarehouseId;
                existingInvoice.TaxRate = purchaseInvoice.TaxRate;
                existingInvoice.DiscountRate = purchaseInvoice.DiscountRate;
                existingInvoice.Notes = purchaseInvoice.Notes;
                existingInvoice.ModifiedDate = DateTime.Now;
                existingInvoice.ModifiedBy = purchaseInvoice.ModifiedBy;

                // إضافة البنود الجديدة وحساب الإجمالي
                decimal subtotal = 0;
                foreach (var item in purchaseInvoice.PurchaseInvoiceItems)
                {
                    var product = await context.Products
                        .FirstOrDefaultAsync(p => p.ProductId == item.ProductId);
                    
                    if (product == null)
                        throw new InvalidOperationException($"الصنف غير موجود");

                    item.Total = item.Quantity * item.UnitPrice;
                    subtotal += item.Total;
                    item.PurchaseInvoiceId = existingInvoice.PurchaseInvoiceId;

                    // تحديث المخزون
                    product.CurrentStock += item.Quantity;
                    
                    // تحديث سعر الشراء
                    product.PurchasePrice = item.UnitPrice;

                    existingInvoice.PurchaseInvoiceItems.Add(item);
                }

                // حساب الضريبة والإجمالي
                existingInvoice.Subtotal = subtotal;
                existingInvoice.TaxAmount = subtotal * (existingInvoice.TaxRate / 100);
                existingInvoice.DiscountAmount = subtotal * (existingInvoice.DiscountRate / 100);
                existingInvoice.TotalAmount = subtotal + existingInvoice.TaxAmount - existingInvoice.DiscountAmount;

                // تحديث رصيد المورد (إضافة المبلغ الجديد)
                existingInvoice.Supplier.CurrentBalance += existingInvoice.TotalAmount;

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// حذف فاتورة مشتريات (حذف منطقي)
        /// Delete purchase invoice (soft delete)
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeletePurchaseInvoiceAsync(int invoiceId, int deletedBy)
        {
            using var context = CreateDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var invoice = await context.PurchaseInvoices
                    .Include(pi => pi.PurchaseInvoiceItems)
                    .Include(pi => pi.Supplier)
                    .FirstOrDefaultAsync(pi => pi.PurchaseInvoiceId == invoiceId);

                if (invoice == null)
                    throw new InvalidOperationException("الفاتورة غير موجودة");

                // استرداد المخزون
                foreach (var item in invoice.PurchaseInvoiceItems)
                {
                    var product = await context.Products
                        .FirstOrDefaultAsync(p => p.ProductId == item.ProductId);
                    
                    if (product != null)
                    {
                        product.CurrentStock -= item.Quantity;
                    }
                }

                // تحديث رصيد المورد
                invoice.Supplier.CurrentBalance -= invoice.TotalAmount;

                // حذف منطقي
                invoice.IsActive = false;
                invoice.ModifiedDate = DateTime.Now;
                invoice.ModifiedBy = deletedBy;

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// إنشاء رقم فاتورة مشتريات تلقائي
        /// Generate automatic purchase invoice number
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>رقم الفاتورة الجديد</returns>
        public async Task<string> GeneratePurchaseInvoiceNumberAsync(int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على أعلى رقم موجود لهذا العام
            var currentYear = DateTime.Now.Year;
            var lastInvoice = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && 
                            pi.InvoiceNumber.StartsWith($"PI{currentYear}") &&
                            pi.InvoiceDate.Year == currentYear)
                .OrderByDescending(pi => pi.InvoiceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastInvoice != null)
            {
                string numberPart = lastInvoice.InvoiceNumber.Substring($"PI{currentYear}".Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"PI{currentYear}{nextNumber:000000}";
        }

        /// <summary>
        /// الحصول على إحصائيات فواتير المشتريات
        /// Get purchase invoice statistics
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>إحصائيات فواتير المشتريات</returns>
        public async Task<PurchaseInvoiceStatistics> GetPurchaseInvoiceStatisticsAsync(int companyId)
        {
            using var context = CreateDbContext();

            var today = DateTime.Today;
            var thisMonth = new DateTime(today.Year, today.Month, 1);
            var thisYear = new DateTime(today.Year, 1, 1);

            var totalInvoices = await context.PurchaseInvoices
                .CountAsync(pi => pi.CompanyId == companyId && pi.IsActive);

            var totalPurchases = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive)
                .SumAsync(pi => pi.TotalAmount);

            var todayPurchases = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive && 
                            pi.InvoiceDate.Date == today)
                .SumAsync(pi => pi.TotalAmount);

            var monthPurchases = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive && 
                            pi.InvoiceDate >= thisMonth)
                .SumAsync(pi => pi.TotalAmount);

            var yearPurchases = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive && 
                            pi.InvoiceDate >= thisYear)
                .SumAsync(pi => pi.TotalAmount);

            return new PurchaseInvoiceStatistics
            {
                TotalInvoices = totalInvoices,
                TotalPurchases = totalPurchases,
                TodayPurchases = todayPurchases,
                MonthPurchases = monthPurchases,
                YearPurchases = yearPurchases
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات فاتورة المشتريات
        /// Validate purchase invoice data
        /// </summary>
        /// <param name="purchaseInvoice">بيانات الفاتورة</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidatePurchaseInvoice(PurchaseInvoice purchaseInvoice)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(purchaseInvoice.InvoiceNumber))
                errors.Add("رقم الفاتورة مطلوب");

            if (purchaseInvoice.SupplierId <= 0)
                errors.Add("يجب اختيار المورد");

            if (purchaseInvoice.WarehouseId <= 0)
                errors.Add("يجب اختيار المخزن");

            if (purchaseInvoice.InvoiceDate == default)
                errors.Add("تاريخ الفاتورة مطلوب");

            if (purchaseInvoice.TaxRate < 0 || purchaseInvoice.TaxRate > 100)
                errors.Add("نسبة الضريبة يجب أن تكون بين 0 و 100");

            if (purchaseInvoice.DiscountRate < 0 || purchaseInvoice.DiscountRate > 100)
                errors.Add("نسبة الخصم يجب أن تكون بين 0 و 100");

            if (purchaseInvoice.PurchaseInvoiceItems == null || !purchaseInvoice.PurchaseInvoiceItems.Any())
                errors.Add("يجب إضافة بند واحد على الأقل للفاتورة");

            if (purchaseInvoice.PurchaseInvoiceItems != null)
            {
                foreach (var item in purchaseInvoice.PurchaseInvoiceItems)
                {
                    if (item.ProductId <= 0)
                        errors.Add("يجب اختيار الصنف لجميع البنود");

                    if (item.Quantity <= 0)
                        errors.Add("الكمية يجب أن تكون أكبر من صفر");

                    if (item.UnitPrice < 0)
                        errors.Add("سعر الوحدة لا يمكن أن يكون سالباً");
                }
            }

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }

    /// <summary>
    /// إحصائيات فواتير المشتريات
    /// Purchase Invoice Statistics
    /// </summary>
    public class PurchaseInvoiceStatistics
    {
        public int TotalInvoices { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TodayPurchases { get; set; }
        public decimal MonthPurchases { get; set; }
        public decimal YearPurchases { get; set; }
    }
}
