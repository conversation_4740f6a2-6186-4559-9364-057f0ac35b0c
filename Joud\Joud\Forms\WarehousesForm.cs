using Joud.BLL;
using Joud.Models;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة المخازن
    /// Warehouses Management Form
    /// </summary>
    public partial class WarehousesForm : Form
    {
        private readonly User _currentUser;
        private readonly Company _currentCompany;
        private readonly WarehouseService _warehouseService;
        private List<Warehouse> _warehouses;
        private Warehouse? _selectedWarehouse;
        private bool _isEditing = false;

        public WarehousesForm(User currentUser, Company currentCompany)
        {
            InitializeComponent();
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _currentCompany = currentCompany ?? throw new ArgumentNullException(nameof(currentCompany));
            _warehouseService = new WarehouseService();
            _warehouses = new List<Warehouse>();
            
            InitializeFormSettings();
            SetupDataGridView();
            _ = LoadWarehousesAsync();
        }

        private void InitializeFormSettings()
        {
            this.Text = "إدارة المخازن - نظام جود للمحاسبة المالية";
            this.Size = new Size(1100, 650);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private void SetupDataGridView()
        {
            // إعداد DataGridView
            dgvWarehouses.AutoGenerateColumns = false;
            dgvWarehouses.AllowUserToAddRows = false;
            dgvWarehouses.AllowUserToDeleteRows = false;
            dgvWarehouses.ReadOnly = true;
            dgvWarehouses.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvWarehouses.MultiSelect = false;
            dgvWarehouses.BackgroundColor = Color.White;
            dgvWarehouses.BorderStyle = BorderStyle.None;
            dgvWarehouses.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvWarehouses.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
            dgvWarehouses.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvWarehouses.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvWarehouses.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvWarehouses.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvWarehouses.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WarehouseCode",
                HeaderText = "كود المخزن",
                DataPropertyName = "WarehouseCode",
                Width = 100,
                ReadOnly = true
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WarehouseName",
                HeaderText = "اسم المخزن",
                DataPropertyName = "WarehouseName",
                Width = 200,
                ReadOnly = true
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WarehouseNameEn",
                HeaderText = "الاسم بالإنجليزية",
                DataPropertyName = "WarehouseNameEn",
                Width = 180,
                ReadOnly = true
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Location",
                HeaderText = "الموقع",
                DataPropertyName = "Location",
                Width = 150,
                ReadOnly = true
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200,
                ReadOnly = true
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductsCount",
                HeaderText = "عدد الأصناف",
                Width = 100,
                ReadOnly = true
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            // أحداث DataGridView
            dgvWarehouses.SelectionChanged += DgvWarehouses_SelectionChanged;
            dgvWarehouses.CellDoubleClick += DgvWarehouses_CellDoubleClick;
            dgvWarehouses.DataBindingComplete += DgvWarehouses_DataBindingComplete;
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                SetControlsEnabled(false);
                lblStatus.Text = "جاري تحميل بيانات المخازن...";
                lblStatus.ForeColor = Color.Blue;

                _warehouses = await _warehouseService.GetAllWarehousesAsync(_currentCompany.CompanyId);
                dgvWarehouses.DataSource = _warehouses;

                // تحديث الإحصائيات
                await UpdateStatisticsAsync();

                lblStatus.Text = $"تم تحميل {_warehouses.Count} مخزن";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في تحميل بيانات المخازن: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية: {ex.InnerException.Message}";

                    // التحقق من أخطاء الأعمدة المفقودة
                    if (ex.InnerException.Message.Contains("Invalid column name"))
                    {
                        errorMessage += "\n\nيبدو أن هناك أعمدة مفقودة في قاعدة البيانات. تأكد من تنفيذ ملفات SQL المحدثة.";

                        // تحديد العمود المفقود
                        if (ex.InnerException.Message.Contains("Description"))
                        {
                            errorMessage += "\nالعمود المفقود: Description في جدول Warehouses";
                        }
                        if (ex.InnerException.Message.Contains("Location"))
                        {
                            errorMessage += "\nالعمود المفقود: Location في جدول Warehouses";
                        }
                        if (ex.InnerException.Message.Contains("SalePrice"))
                        {
                            errorMessage += "\nالعمود المفقود: SalePrice في جدول Products";
                        }
                    }
                }

                ShowError(errorMessage);

                // إنشاء قائمة فارغة لتجنب أخطاء أخرى
                _warehouses = new List<Warehouse>();
                dgvWarehouses.DataSource = _warehouses;
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void DgvWarehouses_DataBindingComplete(object? sender, DataGridViewBindingCompleteEventArgs e)
        {
            // تحديث عدد الأصناف لكل مخزن
            foreach (DataGridViewRow row in dgvWarehouses.Rows)
            {
                if (row.DataBoundItem is Warehouse warehouse)
                {
                    row.Cells["ProductsCount"].Value = warehouse.Products?.Count ?? 0;
                }
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var stats = await _warehouseService.GetWarehouseStatisticsAsync(_currentCompany.CompanyId);
                lblTotalWarehouses.Text = $"إجمالي المخازن: {stats.TotalWarehouses}";
                lblWarehousesWithProducts.Text = $"مخازن تحتوي على أصناف: {stats.WarehousesWithProducts}";
                lblTotalProducts.Text = $"إجمالي الأصناف: {stats.TotalProducts}";
                lblTotalInvoices.Text = $"إجمالي الفواتير: {stats.TotalSalesInvoices + stats.TotalPurchaseInvoices}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        private void DgvWarehouses_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvWarehouses.SelectedRows.Count > 0)
            {
                _selectedWarehouse = dgvWarehouses.SelectedRows[0].DataBoundItem as Warehouse;
                LoadWarehouseDetails();
                btnEdit.Enabled = true;
                btnDelete.Enabled = true;
            }
            else
            {
                _selectedWarehouse = null;
                ClearWarehouseDetails();
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void DgvWarehouses_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void LoadWarehouseDetails()
        {
            if (_selectedWarehouse == null) return;

            txtWarehouseCode.Text = _selectedWarehouse.WarehouseCode;
            txtWarehouseName.Text = _selectedWarehouse.WarehouseName;
            txtWarehouseNameEn.Text = _selectedWarehouse.WarehouseNameEn;
            txtLocation.Text = _selectedWarehouse.Location;
            txtDescription.Text = _selectedWarehouse.Description;
            lblProductsCount.Text = $"عدد الأصناف: {_selectedWarehouse.Products?.Count ?? 0}";
        }

        private void ClearWarehouseDetails()
        {
            txtWarehouseCode.Clear();
            txtWarehouseName.Clear();
            txtWarehouseNameEn.Clear();
            txtLocation.Clear();
            txtDescription.Clear();
            lblProductsCount.Text = "عدد الأصناف: 0";
        }

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                _isEditing = false;
                _selectedWarehouse = null;
                ClearWarehouseDetails();
                
                // إنشاء كود تلقائي
                string newCode = await _warehouseService.GenerateWarehouseCodeAsync(_currentCompany.CompanyId);
                txtWarehouseCode.Text = newCode;
                
                SetEditMode(true);
                txtWarehouseName.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء مخزن جديد: {ex.Message}");
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedWarehouse == null)
            {
                ShowError("يرجى اختيار مخزن للتعديل");
                return;
            }

            _isEditing = true;
            SetEditMode(true);
            txtWarehouseName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SetControlsEnabled(false);
                lblStatus.Text = "جاري حفظ بيانات المخزن...";
                lblStatus.ForeColor = Color.Blue;

                var warehouse = CreateWarehouseFromInput();

                if (_isEditing && _selectedWarehouse != null)
                {
                    warehouse.WarehouseId = _selectedWarehouse.WarehouseId;
                    warehouse.CreatedDate = _selectedWarehouse.CreatedDate;
                    warehouse.CreatedBy = _selectedWarehouse.CreatedBy;
                    warehouse.ModifiedBy = _currentUser.UserId;

                    await _warehouseService.UpdateWarehouseAsync(warehouse);
                    lblStatus.Text = "تم تحديث بيانات المخزن بنجاح";
                }
                else
                {
                    warehouse.CreatedBy = _currentUser.UserId;
                    warehouse.CompanyId = _currentCompany.CompanyId;

                    await _warehouseService.AddWarehouseAsync(warehouse);
                    lblStatus.Text = "تم إضافة المخزن بنجاح";
                }

                lblStatus.ForeColor = Color.Green;
                SetEditMode(false);
                await LoadWarehousesAsync();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ بيانات المخزن: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_selectedWarehouse != null)
            {
                LoadWarehouseDetails();
            }
            else
            {
                ClearWarehouseDetails();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedWarehouse == null)
            {
                ShowError("يرجى اختيار مخزن للحذف");
                return;
            }

            if (MessageBox.Show(
                $"هل تريد حذف المخزن '{_selectedWarehouse.WarehouseName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                try
                {
                    SetControlsEnabled(false);
                    lblStatus.Text = "جاري حذف المخزن...";
                    lblStatus.ForeColor = Color.Blue;

                    await _warehouseService.DeleteWarehouseAsync(_selectedWarehouse.WarehouseId, _currentUser.UserId);
                    
                    lblStatus.Text = "تم حذف المخزن بنجاح";
                    lblStatus.ForeColor = Color.Green;
                    
                    await LoadWarehousesAsync();
                    ClearWarehouseDetails();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف المخزن: {ex.Message}");
                }
                finally
                {
                    SetControlsEnabled(true);
                }
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    dgvWarehouses.DataSource = _warehouses;
                }
                else
                {
                    var searchResults = await _warehouseService.SearchWarehousesAsync(txtSearch.Text, _currentCompany.CompanyId);
                    dgvWarehouses.DataSource = searchResults;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadWarehousesAsync();
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(txtWarehouseName.Text))
                errors.Add("اسم المخزن مطلوب");

            if (string.IsNullOrWhiteSpace(txtWarehouseCode.Text))
                errors.Add("كود المخزن مطلوب");

            if (errors.Any())
            {
                ShowError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private Warehouse CreateWarehouseFromInput()
        {
            return new Warehouse
            {
                WarehouseCode = txtWarehouseCode.Text.Trim(),
                WarehouseName = txtWarehouseName.Text.Trim(),
                WarehouseNameEn = txtWarehouseNameEn.Text.Trim(),
                Location = txtLocation.Text.Trim(),
                Description = txtDescription.Text.Trim()
            };
        }

        private void SetEditMode(bool isEditing)
        {
            // تفعيل/تعطيل حقول الإدخال
            txtWarehouseCode.ReadOnly = isEditing && _isEditing; // الكود للقراءة فقط عند التعديل
            txtWarehouseName.ReadOnly = !isEditing;
            txtWarehouseNameEn.ReadOnly = !isEditing;
            txtLocation.ReadOnly = !isEditing;
            txtDescription.ReadOnly = !isEditing;

            // تفعيل/تعطيل الأزرار
            btnNew.Enabled = !isEditing;
            btnEdit.Enabled = !isEditing && _selectedWarehouse != null;
            btnDelete.Enabled = !isEditing && _selectedWarehouse != null;
            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;
            btnRefresh.Enabled = !isEditing;
            dgvWarehouses.Enabled = !isEditing;
            txtSearch.Enabled = !isEditing;
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is DataGridView)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void WarehousesForm_Load(object sender, EventArgs e)
        {
            SetEditMode(false);
        }
    }
}
