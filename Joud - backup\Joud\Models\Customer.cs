using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات العملاء
    /// Customer Data Model
    /// </summary>
    [Table("Customers")]
    public class Customer
    {
        [Key]
        public int CustomerId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "كود العميل")]
        public string CustomerCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم العميل")]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "اسم العميل بالإنجليزية")]
        public string? CustomerNameEn { get; set; }

        [StringLength(50)]
        [Display(Name = "الهاتف")]
        public string? Phone { get; set; }

        [StringLength(50)]
        [Display(Name = "الجوال")]
        public string? Mobile { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(100)]
        [Display(Name = "المدينة")]
        public string? City { get; set; }

        [StringLength(100)]
        [Display(Name = "البلد")]
        public string? Country { get; set; }

        [StringLength(50)]
        [Display(Name = "الرقم الضريبي")]
        public string? TaxNumber { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الحد الائتماني")]
        public decimal CreditLimit { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الرصيد الحالي")]
        public decimal CurrentBalance { get; set; } = 0;

        [Required]
        [Display(Name = "الشركة")]
        public int CompanyId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "أنشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "عدل بواسطة")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        [ForeignKey("CompanyId")]
        public virtual Company Company { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User CreatedByUser { get; set; } = null!;

        [ForeignKey("ModifiedBy")]
        public virtual User? ModifiedByUser { get; set; }

        // Collections
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; } = new List<SalesInvoice>();
    }
}
