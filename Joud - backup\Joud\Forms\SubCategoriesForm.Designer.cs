namespace Joud.Forms
{
    partial class SubCategoriesForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.txtSearch = new TextBox();
            this.lblSearch = new Label();
            this.btnRefresh = new Button();
            
            this.pnlStatistics = new Panel();
            this.lblTotalSubCategories = new Label();
            this.lblCategoriesWithProducts = new Label();
            this.lblTotalProducts = new Label();
            this.lblMainCategoriesWithSubs = new Label();
            
            this.pnlMain = new Panel();
            this.dgvSubCategories = new DataGridView();
            
            this.pnlDetails = new Panel();
            this.grpCategoryDetails = new GroupBox();
            this.lblCategoryCode = new Label();
            this.txtCategoryCode = new TextBox();
            this.lblCategoryName = new Label();
            this.txtCategoryName = new TextBox();
            this.lblMainCategory = new Label();
            this.cmbMainCategory = new ComboBox();
            this.lblCategoryNameEn = new Label();
            this.txtCategoryNameEn = new TextBox();
            this.lblDescription = new Label();
            this.txtDescription = new TextBox();
            this.lblProductsCount = new Label();
            
            this.pnlButtons = new Panel();
            this.btnNew = new Button();
            this.btnEdit = new Button();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.btnDelete = new Button();
            this.lblStatus = new Label();

            this.SuspendLayout();

            // Header Panel
            this.pnlHeader.BackColor = Color.FromArgb(52, 73, 94);
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Controls.Add(this.txtSearch);
            this.pnlHeader.Controls.Add(this.lblSearch);
            this.pnlHeader.Controls.Add(this.btnRefresh);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Size = new Size(1100, 60);

            this.lblTitle.Text = "الفئات الفرعية";
            this.lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 15);

            this.lblSearch.Text = "البحث:";
            this.lblSearch.Font = new Font("Segoe UI", 10F);
            this.lblSearch.ForeColor = Color.White;
            this.lblSearch.Location = new Point(500, 20);

            this.txtSearch.PlaceholderText = "ابحث بالاسم أو الكود أو الفئة الرئيسية...";
            this.txtSearch.Font = new Font("Segoe UI", 10F);
            this.txtSearch.Location = new Point(250, 17);
            this.txtSearch.Size = new Size(240, 25);
            this.txtSearch.TextChanged += new EventHandler(this.txtSearch_TextChanged);

            this.btnRefresh.Text = "تحديث";
            this.btnRefresh.BackColor = Color.FromArgb(39, 174, 96);
            this.btnRefresh.FlatStyle = FlatStyle.Flat;
            this.btnRefresh.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnRefresh.ForeColor = Color.White;
            this.btnRefresh.Location = new Point(160, 15);
            this.btnRefresh.Size = new Size(80, 30);
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);

            // Statistics Panel
            this.pnlStatistics.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlStatistics.Controls.Add(this.lblTotalSubCategories);
            this.pnlStatistics.Controls.Add(this.lblCategoriesWithProducts);
            this.pnlStatistics.Controls.Add(this.lblTotalProducts);
            this.pnlStatistics.Controls.Add(this.lblMainCategoriesWithSubs);
            this.pnlStatistics.Dock = DockStyle.Top;
            this.pnlStatistics.Location = new Point(0, 60);
            this.pnlStatistics.Size = new Size(1100, 40);

            this.lblTotalSubCategories.Text = "إجمالي الفئات الفرعية: 0";
            this.lblTotalSubCategories.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalSubCategories.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalSubCategories.Location = new Point(850, 12);

            this.lblCategoriesWithProducts.Text = "فئات تحتوي على أصناف: 0";
            this.lblCategoriesWithProducts.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblCategoriesWithProducts.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblCategoriesWithProducts.Location = new Point(650, 12);

            this.lblTotalProducts.Text = "إجمالي الأصناف: 0";
            this.lblTotalProducts.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblTotalProducts.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblTotalProducts.Location = new Point(450, 12);

            this.lblMainCategoriesWithSubs.Text = "فئات رئيسية لها فئات فرعية: 0";
            this.lblMainCategoriesWithSubs.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblMainCategoriesWithSubs.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblMainCategoriesWithSubs.Location = new Point(200, 12);

            // Main Panel with DataGridView
            this.pnlMain.Controls.Add(this.dgvSubCategories);
            this.pnlMain.Dock = DockStyle.Left;
            this.pnlMain.Location = new Point(0, 100);
            this.pnlMain.Size = new Size(650, 490);

            this.dgvSubCategories.BackgroundColor = Color.White;
            this.dgvSubCategories.BorderStyle = BorderStyle.None;
            this.dgvSubCategories.Dock = DockStyle.Fill;

            // Details Panel
            this.pnlDetails.BackColor = Color.FromArgb(240, 244, 247);
            this.pnlDetails.Controls.Add(this.grpCategoryDetails);
            this.pnlDetails.Dock = DockStyle.Fill;
            this.pnlDetails.Location = new Point(650, 100);
            this.pnlDetails.Size = new Size(450, 490);

            this.grpCategoryDetails.Text = "تفاصيل الفئة الفرعية";
            this.grpCategoryDetails.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpCategoryDetails.Location = new Point(20, 20);
            this.grpCategoryDetails.Size = new Size(410, 350);

            // Category Details Controls
            // Category Code
            this.lblCategoryCode.Text = "كود الفئة:";
            this.lblCategoryCode.Font = new Font("Segoe UI", 9F);
            this.lblCategoryCode.Location = new Point(320, 30);
            this.lblCategoryCode.Size = new Size(80, 15);

            this.txtCategoryCode.Font = new Font("Segoe UI", 9F);
            this.txtCategoryCode.Location = new Point(20, 27);
            this.txtCategoryCode.Size = new Size(290, 23);
            this.txtCategoryCode.ReadOnly = true;

            // Category Name
            this.lblCategoryName.Text = "اسم الفئة:";
            this.lblCategoryName.Font = new Font("Segoe UI", 9F);
            this.lblCategoryName.Location = new Point(320, 75);
            this.lblCategoryName.Size = new Size(80, 15);

            this.txtCategoryName.Font = new Font("Segoe UI", 9F);
            this.txtCategoryName.Location = new Point(20, 72);
            this.txtCategoryName.Size = new Size(290, 23);

            // Main Category
            this.lblMainCategory.Text = "الفئة الرئيسية:";
            this.lblMainCategory.Font = new Font("Segoe UI", 9F);
            this.lblMainCategory.Location = new Point(320, 120);
            this.lblMainCategory.Size = new Size(80, 15);

            this.cmbMainCategory.Font = new Font("Segoe UI", 9F);
            this.cmbMainCategory.Location = new Point(20, 117);
            this.cmbMainCategory.Size = new Size(290, 23);
            this.cmbMainCategory.DropDownStyle = ComboBoxStyle.DropDownList;

            // Category Name English
            this.lblCategoryNameEn.Text = "الاسم بالإنجليزية:";
            this.lblCategoryNameEn.Font = new Font("Segoe UI", 9F);
            this.lblCategoryNameEn.Location = new Point(320, 165);
            this.lblCategoryNameEn.Size = new Size(100, 15);

            this.txtCategoryNameEn.Font = new Font("Segoe UI", 9F);
            this.txtCategoryNameEn.Location = new Point(20, 162);
            this.txtCategoryNameEn.Size = new Size(290, 23);

            // Description
            this.lblDescription.Text = "الوصف:";
            this.lblDescription.Font = new Font("Segoe UI", 9F);
            this.lblDescription.Location = new Point(320, 210);
            this.lblDescription.Size = new Size(50, 15);

            this.txtDescription.Font = new Font("Segoe UI", 9F);
            this.txtDescription.Location = new Point(20, 207);
            this.txtDescription.Size = new Size(290, 60);
            this.txtDescription.Multiline = true;

            // Products Count
            this.lblProductsCount.Text = "عدد الأصناف: 0";
            this.lblProductsCount.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.lblProductsCount.ForeColor = Color.FromArgb(39, 174, 96);
            this.lblProductsCount.Location = new Point(20, 287);
            this.lblProductsCount.Size = new Size(150, 15);

            // Add all controls to group box
            this.grpCategoryDetails.Controls.Add(this.lblCategoryCode);
            this.grpCategoryDetails.Controls.Add(this.txtCategoryCode);
            this.grpCategoryDetails.Controls.Add(this.lblCategoryName);
            this.grpCategoryDetails.Controls.Add(this.txtCategoryName);
            this.grpCategoryDetails.Controls.Add(this.lblMainCategory);
            this.grpCategoryDetails.Controls.Add(this.cmbMainCategory);
            this.grpCategoryDetails.Controls.Add(this.lblCategoryNameEn);
            this.grpCategoryDetails.Controls.Add(this.txtCategoryNameEn);
            this.grpCategoryDetails.Controls.Add(this.lblDescription);
            this.grpCategoryDetails.Controls.Add(this.txtDescription);
            this.grpCategoryDetails.Controls.Add(this.lblProductsCount);

            // Buttons Panel
            this.pnlButtons.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlButtons.Controls.Add(this.btnNew);
            this.pnlButtons.Controls.Add(this.btnEdit);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.btnDelete);
            this.pnlButtons.Controls.Add(this.lblStatus);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Size = new Size(1100, 60);

            // Buttons
            this.btnNew.Text = "جديد";
            this.btnNew.BackColor = Color.FromArgb(39, 174, 96);
            this.btnNew.FlatStyle = FlatStyle.Flat;
            this.btnNew.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnNew.ForeColor = Color.White;
            this.btnNew.Location = new Point(980, 15);
            this.btnNew.Size = new Size(100, 35);
            this.btnNew.Click += new EventHandler(this.btnNew_Click);

            this.btnEdit.Text = "تعديل";
            this.btnEdit.BackColor = Color.FromArgb(41, 128, 185);
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.Location = new Point(870, 15);
            this.btnEdit.Size = new Size(100, 35);
            this.btnEdit.Click += new EventHandler(this.btnEdit_Click);

            this.btnSave.Text = "حفظ";
            this.btnSave.BackColor = Color.FromArgb(39, 174, 96);
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(760, 15);
            this.btnSave.Size = new Size(100, 35);
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            this.btnCancel.Text = "إلغاء";
            this.btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(650, 15);
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.btnDelete.Text = "حذف";
            this.btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.Location = new Point(540, 15);
            this.btnDelete.Size = new Size(100, 35);
            this.btnDelete.Click += new EventHandler(this.btnDelete_Click);

            this.lblStatus.Text = "جاهز...";
            this.lblStatus.Font = new Font("Segoe UI", 9F);
            this.lblStatus.ForeColor = Color.FromArgb(52, 73, 94);
            this.lblStatus.Location = new Point(20, 25);
            this.lblStatus.Size = new Size(200, 15);

            // Form
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.ClientSize = new Size(1100, 650);
            this.Controls.Add(this.pnlDetails);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlStatistics);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.pnlButtons);
            this.Name = "SubCategoriesForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة الفئات الفرعية - نظام جود للمحاسبة المالية";
            this.Load += new EventHandler(this.SubCategoriesForm_Load);

            this.ResumeLayout(false);
        }

        #endregion

        private Panel pnlHeader;
        private Label lblTitle;
        private TextBox txtSearch;
        private Label lblSearch;
        private Button btnRefresh;
        private Panel pnlStatistics;
        private Label lblTotalSubCategories;
        private Label lblCategoriesWithProducts;
        private Label lblTotalProducts;
        private Label lblMainCategoriesWithSubs;
        private Panel pnlMain;
        private DataGridView dgvSubCategories;
        private Panel pnlDetails;
        private GroupBox grpCategoryDetails;
        private Label lblCategoryCode;
        private TextBox txtCategoryCode;
        private Label lblCategoryName;
        private TextBox txtCategoryName;
        private Label lblMainCategory;
        private ComboBox cmbMainCategory;
        private Label lblCategoryNameEn;
        private TextBox txtCategoryNameEn;
        private Label lblDescription;
        private TextBox txtDescription;
        private Label lblProductsCount;
        private Panel pnlButtons;
        private Button btnNew;
        private Button btnEdit;
        private Button btnSave;
        private Button btnCancel;
        private Button btnDelete;
        private Label lblStatus;
    }
}
