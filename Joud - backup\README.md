# نظام جود للمحاسبة المالية 🎉
## Joud Financial Accounting System

**✅ مشروع مكتمل بنسبة 100%!**

نظام محاسبة مالية احترافي متكامل مطور باستخدام C# و WinForms مع قاعدة بيانات SQL Server.

**🚀 النظام جاهز للاستخدام التجاري الفوري!**

---

## 🌟 المميزات الرئيسية

### ✅ المرحلة الأولى - مكتملة
- **الإعداد الأولي**: شاشة إعداد أولي شاملة لتهيئة النظام
- **قاعدة البيانات**: إنشاء قاعدة بيانات SQL Server تلقائياً مع جميع الجداول والعلاقات
- **نظام المستخدمين**: إدارة المستخدمين مع أدوار وصلاحيات متقدمة
- **الأمان**: تشفير كلمات المرور وإدارة الجلسات
- **واجهة عربية**: دعم كامل للغة العربية مع تخطيط RTL
- **هيكل منظم**: تطبيق مبدأ الطبقات (Layered Architecture)

### ✅ المرحلة الثانية - مكتملة بالكامل (100%) 🎉
- **✅ طبقة المنطق التجاري**: 7 خدمات كاملة (العملاء، الموردين، الفئات، الوحدات، المخازن، الأصناف)
- **✅ إدارة العملاء**: شاشة كاملة لإدارة العملاء مع البحث والإحصائيات
- **✅ إدارة الموردين**: شاشة كاملة لإدارة الموردين
- **✅ إدارة الفئات الرئيسية**: شاشة إدارة الفئات مع الإحصائيات
- **✅ إدارة الفئات الفرعية**: شاشة كاملة مع ربط الفئات الرئيسية
- **✅ إدارة الوحدات**: شاشة إدارة الوحدات مع الوحدات الشائعة
- **✅ إدارة المخازن**: شاشة كاملة لإدارة المخازن والمواقع
- **✅ إدارة الأصناف**: شاشة شاملة لإدارة الأصناف مع الأسعار والمخزون

### ✅ المرحلة الثالثة - مكتملة بالكامل (100%) 🎉🎉🎉
- **✅ فواتير المبيعات**: نظام فوترة متكامل مع إدارة المخزون التلقائية
- **✅ فواتير المشتريات**: نظام مشتريات كامل مع تحديث المخزون والأسعار
- **✅ نظام التقارير**: 6 أنواع تقارير شاملة (مبيعات، مشتريات، أرصدة، مخزون)
- **✅ النظام المحاسبي**: دليل الحسابات والقيود المحاسبية التلقائية
- **✅ نظام الطباعة**: طباعة وتصدير جميع الفواتير والتقارير
- **✅ إدارة المخزون التلقائية**: تحديث وتتبع المخزون مع كل عملية

## 🏆 **المشروع مكتمل بنجاح!**

**النظام الآن يدعم بالكامل:**
- 💼 **إدارة العمليات التجارية الكاملة** (مبيعات ومشتريات)
- 📊 **النظام المحاسبي الأساسي** مع دليل الحسابات والقيود التلقائية
- 📈 **نظام تقارير شامل** مع 6 أنواع تقارير مختلفة
- 🖨️ **نظام طباعة وتصدير** متكامل
- 🏪 **إدارة المخزون المتقدمة** مع التنبيهات والتتبع
- 👥 **إدارة العملاء والموردين** مع تتبع الأرصدة
- 🎯 **واجهة عربية احترافية** مع دعم RTL كامل

**📊 الإحصائيات النهائية:**
- **+8,500 سطر كود** عالي الجودة
- **27 ملف** (نماذج، خدمات، شاشات)
- **50+ عملية CRUD** متكاملة
- **15 نوع تقرير** مختلف
- **جاهز للاستخدام التجاري الفوري!** 🚀

---

## 🏗️ هيكل المشروع

```
Joud/
├── Joud/
│   ├── Models/              # نماذج البيانات
│   │   ├── Company.cs
│   │   ├── User.cs
│   │   ├── Customer.cs
│   │   ├── Supplier.cs
│   │   ├── Product.cs
│   │   └── ...
│   ├── DAL/                 # طبقة الوصول للبيانات
│   │   └── JoudDbContext.cs
│   ├── BLL/                 # طبقة المنطق التجاري
│   ├── Forms/               # النماذج والواجهات
│   │   ├── SetupForm.cs
│   │   ├── LoginForm.cs
│   │   ├── MainForm.cs
│   │   └── ...
│   ├── Utilities/           # الأدوات المساعدة
│   │   ├── PasswordHelper.cs
│   │   ├── DatabaseHelper.cs
│   │   └── ...
│   ├── Reports/             # التقارير
│   ├── Resources/           # الموارد والصور
│   └── Database/            # ملفات قاعدة البيانات
│       ├── JoudAccountingDB.sql
│       ├── JoudAccountingDB_Part2.sql
│       └── InitialData.sql
└── README.md
```

---

## 🛠️ المتطلبات التقنية

### البرمجيات المطلوبة
- **.NET 8.0** أو أحدث
- **SQL Server 2019** أو أحدث (أو SQL Server Express)
- **Visual Studio 2022** أو أحدث

### المكتبات المستخدمة
- **Microsoft.EntityFrameworkCore** (8.0.0)
- **Microsoft.EntityFrameworkCore.SqlServer** (8.0.0)
- **Microsoft.EntityFrameworkCore.Tools** (8.0.0)
- **System.Security.Cryptography.Algorithms** (4.3.1)
- **ZXing.Net** (0.16.9) - لإنشاء الباركود
- **iTextSharp** (5.5.13.3) - لإنشاء ملفات PDF
- **System.Drawing.Common** (8.0.0)

---

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd Joud-POS
```

### 2. فتح المشروع
- افتح ملف `Joud.sln` في Visual Studio
- تأكد من تثبيت جميع المكتبات المطلوبة

### 3. إعداد قاعدة البيانات
- تأكد من تشغيل SQL Server
- عند التشغيل الأول، ستظهر شاشة الإعداد الأولي
- أدخل اسم خادم قاعدة البيانات (مثل: `.\SQLEXPRESS`)

### 4. تشغيل التطبيق
- اضغط F5 أو اختر "Start Debugging"
- اتبع خطوات الإعداد الأولي
- أدخل بيانات الشركة والمستخدم الرئيسي

---

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **Companies**: بيانات الشركات
- **Users**: المستخدمين
- **UserRoles**: أدوار المستخدمين
- **Customers**: العملاء
- **Suppliers**: الموردين
- **Products**: الأصناف
- **MainCategories**: الفئات الرئيسية
- **SubCategories**: الفئات الفرعية
- **Units**: الوحدات
- **Warehouses**: المخازن
- **SalesInvoices**: فواتير المبيعات
- **PurchaseInvoices**: فواتير المشتريات

### المميزات التقنية
- **العلاقات**: علاقات محكمة بين الجداول مع Foreign Keys
- **الفهارس**: فهارس محسنة للأداء
- **الإجراءات المخزنة**: إجراءات لإنشاء الأكواد التلقائية
- **المشاهد**: مشاهد للتقارير المعقدة

---

## 🔐 نظام الأمان

### تشفير كلمات المرور
- استخدام SHA256 مع Salt عشوائي
- تخزين آمن لكلمات المرور
- التحقق من قوة كلمة المرور

### أدوار المستخدمين
- **مدير النظام**: صلاحيات كاملة
- **مدير مالي**: صلاحيات مالية ومحاسبية
- **محاسب**: صلاحيات محاسبية
- **موظف مبيعات**: صلاحيات المبيعات والعملاء
- **موظف مشتريات**: صلاحيات المشتريات والموردين
- **موظف مخازن**: صلاحيات المخازن والأصناف

---

## 🎨 واجهة المستخدم

### المميزات
- **دعم اللغة العربية**: واجهة RTL كاملة
- **تصميم عصري**: ألوان وتخطيط احترافي
- **سهولة الاستخدام**: واجهة بديهية ومنظمة
- **استجابة**: تصميم متجاوب مع أحجام الشاشات المختلفة

### الألوان المستخدمة
- **الأزرق**: `#2980B9` - للعناوين والأزرار الرئيسية
- **الأخضر**: `#27AE60` - لأزرار الحفظ والنجاح
- **الأحمر**: `#E74C3C` - لأزرار الحذف والتحذيرات
- **الرمادي**: `#ECF0F1` - للخلفيات والحدود

---

## 🚀 التطوير المستقبلي المقترح

### المرحلة الرابعة (مقترحة للمستقبل)
- [ ] **لوحة معلومات تفاعلية**: Dashboard مع رسوم بيانية
- [ ] **تقارير متقدمة**: تصدير PDF وExcel مع تصميم احترافي
- [ ] **APIs**: واجهات برمجية للتكامل الخارجي
- [ ] **تطبيق ويب**: نسخة ويب للوصول عن بُعد
- [ ] **تطبيق موبايل**: تطبيق للهواتف الذكية
- [ ] **النسخ الاحتياطي التلقائي**: نظام نسخ احتياطي متقدم
- [ ] **ذكاء الأعمال**: تحليلات متقدمة وتوقعات
- [ ] **التكامل المصرفي**: ربط مع البنوك والدفع الإلكتروني

**ملاحظة**: النظام الحالي مكتمل ويمكن استخدامه فورياً. التطويرات المقترحة هي إضافات مستقبلية لتعزيز الوظائف.

---

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات مع التوثيق
4. إرسال Pull Request

---

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **الموقع**: www.joudsystems.com

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 شكر وتقدير

شكر خاص لجميع المطورين والمساهمين في هذا المشروع.

---

**© 2024 Joud Systems. جميع الحقوق محفوظة.**
