using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة المخازن
    /// Warehouse Management Service
    /// </summary>
    public class WarehouseService
    {
        private readonly string _connectionString;

        public WarehouseService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// الحصول على جميع المخازن للشركة
        /// Get all warehouses for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة المخازن</returns>
        public async Task<List<Warehouse>> GetAllWarehousesAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.Warehouses
                .Where(w => w.CompanyId == companyId && w.IsActive)
                .Include(w => w.CreatedByUser)
                .Include(w => w.Products.Where(p => p.IsActive))
                .OrderBy(w => w.WarehouseName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على مخزن بالمعرف
        /// Get warehouse by ID
        /// </summary>
        /// <param name="warehouseId">معرف المخزن</param>
        /// <returns>بيانات المخزن</returns>
        public async Task<Warehouse?> GetWarehouseByIdAsync(int warehouseId)
        {
            using var context = CreateDbContext();
            return await context.Warehouses
                .Include(w => w.CreatedByUser)
                .Include(w => w.Company)
                .Include(w => w.Products.Where(p => p.IsActive))
                .Include(w => w.SalesInvoices.Where(si => si.IsActive))
                .Include(w => w.PurchaseInvoices.Where(pi => pi.IsActive))
                .FirstOrDefaultAsync(w => w.WarehouseId == warehouseId && w.IsActive);
        }

        /// <summary>
        /// الحصول على مخزن بالكود
        /// Get warehouse by code
        /// </summary>
        /// <param name="warehouseCode">كود المخزن</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات المخزن</returns>
        public async Task<Warehouse?> GetWarehouseByCodeAsync(string warehouseCode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.Warehouses
                .FirstOrDefaultAsync(w => w.WarehouseCode == warehouseCode && 
                                         w.CompanyId == companyId && w.IsActive);
        }

        /// <summary>
        /// البحث في المخازن
        /// Search warehouses
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة المخازن المطابقة</returns>
        public async Task<List<Warehouse>> SearchWarehousesAsync(string searchTerm, int companyId)
        {
            using var context = CreateDbContext();
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllWarehousesAsync(companyId);

            searchTerm = searchTerm.Trim().ToLower();

            return await context.Warehouses
                .Where(w => w.CompanyId == companyId && w.IsActive &&
                           (w.WarehouseName.ToLower().Contains(searchTerm) ||
                            w.WarehouseCode.ToLower().Contains(searchTerm) ||
                            (w.WarehouseNameEn != null && w.WarehouseNameEn.ToLower().Contains(searchTerm)) ||
                            (w.Location != null && w.Location.ToLower().Contains(searchTerm)) ||
                            (w.Description != null && w.Description.ToLower().Contains(searchTerm))))
                .Include(w => w.CreatedByUser)
                .Include(w => w.Products.Where(p => p.IsActive))
                .OrderBy(w => w.WarehouseName)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة مخزن جديد
        /// Add new warehouse
        /// </summary>
        /// <param name="warehouse">بيانات المخزن</param>
        /// <returns>معرف المخزن الجديد</returns>
        public async Task<int> AddWarehouseAsync(Warehouse warehouse)
        {
            using var context = CreateDbContext();
            
            // التحقق من عدم تكرار الكود
            bool codeExists = await context.Warehouses
                .AnyAsync(w => w.WarehouseCode == warehouse.WarehouseCode && 
                              w.CompanyId == warehouse.CompanyId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود المخزن '{warehouse.WarehouseCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الاسم
            bool nameExists = await context.Warehouses
                .AnyAsync(w => w.WarehouseName == warehouse.WarehouseName && 
                              w.CompanyId == warehouse.CompanyId);
            
            if (nameExists)
                throw new InvalidOperationException($"اسم المخزن '{warehouse.WarehouseName}' موجود مسبقاً");

            // إنشاء كود تلقائي إذا لم يتم تحديده
            if (string.IsNullOrEmpty(warehouse.WarehouseCode))
            {
                warehouse.WarehouseCode = await GenerateWarehouseCodeAsync(warehouse.CompanyId);
            }

            warehouse.CreatedDate = DateTime.Now;
            warehouse.IsActive = true;

            context.Warehouses.Add(warehouse);
            await context.SaveChangesAsync();

            return warehouse.WarehouseId;
        }

        /// <summary>
        /// تحديث بيانات مخزن
        /// Update warehouse
        /// </summary>
        /// <param name="warehouse">بيانات المخزن المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateWarehouseAsync(Warehouse warehouse)
        {
            using var context = CreateDbContext();

            var existingWarehouse = await context.Warehouses
                .FirstOrDefaultAsync(w => w.WarehouseId == warehouse.WarehouseId);

            if (existingWarehouse == null)
                throw new InvalidOperationException("المخزن غير موجود");

            // التحقق من عدم تكرار الكود مع مخازن أخرى
            bool codeExists = await context.Warehouses
                .AnyAsync(w => w.WarehouseCode == warehouse.WarehouseCode && 
                              w.CompanyId == warehouse.CompanyId &&
                              w.WarehouseId != warehouse.WarehouseId);

            if (codeExists)
                throw new InvalidOperationException($"كود المخزن '{warehouse.WarehouseCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الاسم مع مخازن أخرى
            bool nameExists = await context.Warehouses
                .AnyAsync(w => w.WarehouseName == warehouse.WarehouseName && 
                              w.CompanyId == warehouse.CompanyId &&
                              w.WarehouseId != warehouse.WarehouseId);

            if (nameExists)
                throw new InvalidOperationException($"اسم المخزن '{warehouse.WarehouseName}' موجود مسبقاً");

            // تحديث البيانات
            existingWarehouse.WarehouseCode = warehouse.WarehouseCode;
            existingWarehouse.WarehouseName = warehouse.WarehouseName;
            existingWarehouse.WarehouseNameEn = warehouse.WarehouseNameEn;
            existingWarehouse.Location = warehouse.Location;
            existingWarehouse.Description = warehouse.Description;
            existingWarehouse.ModifiedDate = DateTime.Now;
            existingWarehouse.ModifiedBy = warehouse.ModifiedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف مخزن (حذف منطقي)
        /// Delete warehouse (soft delete)
        /// </summary>
        /// <param name="warehouseId">معرف المخزن</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteWarehouseAsync(int warehouseId, int deletedBy)
        {
            using var context = CreateDbContext();

            var warehouse = await context.Warehouses
                .Include(w => w.Products)
                .Include(w => w.SalesInvoices)
                .Include(w => w.PurchaseInvoices)
                .FirstOrDefaultAsync(w => w.WarehouseId == warehouseId);

            if (warehouse == null)
                throw new InvalidOperationException("المخزن غير موجود");

            // التحقق من عدم وجود أصناف مرتبطة
            if (warehouse.Products.Any(p => p.IsActive))
                throw new InvalidOperationException("لا يمكن حذف المخزن لوجود أصناف مرتبطة به");

            // التحقق من عدم وجود فواتير مرتبطة
            if (warehouse.SalesInvoices.Any(si => si.IsActive) || warehouse.PurchaseInvoices.Any(pi => pi.IsActive))
                throw new InvalidOperationException("لا يمكن حذف المخزن لوجود فواتير مرتبطة به");

            // حذف منطقي
            warehouse.IsActive = false;
            warehouse.ModifiedDate = DateTime.Now;
            warehouse.ModifiedBy = deletedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// إنشاء كود مخزن تلقائي
        /// Generate automatic warehouse code
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>كود المخزن الجديد</returns>
        public async Task<string> GenerateWarehouseCodeAsync(int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على أعلى رقم موجود
            var lastWarehouse = await context.Warehouses
                .Where(w => w.CompanyId == companyId && w.WarehouseCode.StartsWith("WH"))
                .OrderByDescending(w => w.WarehouseCode)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastWarehouse != null)
            {
                string numberPart = lastWarehouse.WarehouseCode.Substring(2);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"WH{nextNumber:000}";
        }

        /// <summary>
        /// الحصول على إحصائيات المخازن
        /// Get warehouse statistics
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>إحصائيات المخازن</returns>
        public async Task<WarehouseStatistics> GetWarehouseStatisticsAsync(int companyId)
        {
            using var context = CreateDbContext();

            var totalWarehouses = await context.Warehouses
                .CountAsync(w => w.CompanyId == companyId && w.IsActive);

            var warehousesWithProducts = await context.Warehouses
                .Where(w => w.CompanyId == companyId && w.IsActive)
                .CountAsync(w => w.Products.Any(p => p.IsActive));

            var totalProducts = await context.Products
                .CountAsync(p => p.CompanyId == companyId && p.IsActive);

            var totalSalesInvoices = await context.SalesInvoices
                .CountAsync(si => si.CompanyId == companyId && si.IsActive);

            var totalPurchaseInvoices = await context.PurchaseInvoices
                .CountAsync(pi => pi.CompanyId == companyId && pi.IsActive);

            return new WarehouseStatistics
            {
                TotalWarehouses = totalWarehouses,
                WarehousesWithProducts = warehousesWithProducts,
                TotalProducts = totalProducts,
                TotalSalesInvoices = totalSalesInvoices,
                TotalPurchaseInvoices = totalPurchaseInvoices
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات المخزن
        /// Validate warehouse data
        /// </summary>
        /// <param name="warehouse">بيانات المخزن</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateWarehouse(Warehouse warehouse)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(warehouse.WarehouseName))
                errors.Add("اسم المخزن مطلوب");

            if (warehouse.WarehouseName?.Length > 200)
                errors.Add("اسم المخزن يجب أن يكون أقل من 200 حرف");

            if (string.IsNullOrWhiteSpace(warehouse.WarehouseCode))
                errors.Add("كود المخزن مطلوب");

            if (warehouse.WarehouseCode?.Length > 20)
                errors.Add("كود المخزن يجب أن يكون أقل من 20 حرف");

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }

    /// <summary>
    /// إحصائيات المخازن
    /// Warehouse Statistics
    /// </summary>
    public class WarehouseStatistics
    {
        public int TotalWarehouses { get; set; }
        public int WarehousesWithProducts { get; set; }
        public int TotalProducts { get; set; }
        public int TotalSalesInvoices { get; set; }
        public int TotalPurchaseInvoices { get; set; }
    }
}
