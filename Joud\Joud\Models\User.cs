using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات المستخدمين
    /// User Data Model
    /// </summary>
    [Table("Users")]
    public class User
    {
        [Key]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        [Display(Name = "كلمة المرور المشفرة")]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        [Display(Name = "مفتاح التشفير")]
        public string PasswordSalt { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(50)]
        [Display(Name = "الهاتف")]
        public string? Phone { get; set; }

        [Required]
        [Display(Name = "الدور")]
        public int RoleId { get; set; }

        [Required]
        [Display(Name = "الشركة")]
        public int CompanyId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLogin { get; set; }

        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLoginDate { get; set; }

        [StringLength(100)]
        [Display(Name = "اسم الدور")]
        public string? RoleName { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "أنشئ بواسطة")]
        public int? CreatedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "عدل بواسطة")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        public virtual UserRole UserRole { get; set; } = null!;
        public virtual Company Company { get; set; } = null!;
        public virtual User? CreatedByUser { get; set; }
        public virtual User? ModifiedByUser { get; set; }

        // Collections
        public virtual ICollection<Customer> CreatedCustomers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> CreatedSuppliers { get; set; } = new List<Supplier>();
        public virtual ICollection<Product> CreatedProducts { get; set; } = new List<Product>();
        public virtual ICollection<SalesInvoice> CreatedSalesInvoices { get; set; } = new List<SalesInvoice>();
        public virtual ICollection<PurchaseInvoice> CreatedPurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
    }
}
