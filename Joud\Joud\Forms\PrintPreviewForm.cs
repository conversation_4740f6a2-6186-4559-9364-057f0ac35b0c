using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة معاينة الطباعة - Print Preview Form
    /// تعرض معاينة للمستندات قبل الطباعة مع إمكانيات التحكم
    /// </summary>
    public partial class PrintPreviewForm : Form
    {
        #region Private Fields

        private PrintDocument _printDocument;
        private string _documentTitle;
        private double _currentZoom = 1.0;
        private int _currentPage = 1;
        private int _totalPages = 1;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ شاشة معاينة الطباعة
        /// </summary>
        public PrintPreviewForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// منشئ شاشة معاينة الطباعة مع مستند للطباعة
        /// </summary>
        /// <param name="printDocument">مستند الطباعة</param>
        /// <param name="documentTitle">عنوان المستند</param>
        public PrintPreviewForm(PrintDocument printDocument, string documentTitle = null) : this()
        {
            _printDocument = printDocument;
            _documentTitle = documentTitle ?? "مستند";
            SetupPrintPreview();
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل الشاشة
        /// </summary>
        private void PrintPreviewForm_Load(object sender, EventArgs e)
        {
            try
            {
                UpdateUI();
                UpdateStatus();
                
                if (_printDocument != null)
                {
                    RefreshPreview();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معاينة الطباعة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Toolbar Events

        /// <summary>
        /// حدث النقر على زر الطباعة
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                PrintDocument();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر إعداد الصفحة
        /// </summary>
        private void btnPageSetup_Click(object sender, EventArgs e)
        {
            try
            {
                ShowPageSetup();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد الصفحة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر التكبير
        /// </summary>
        private void btnZoomIn_Click(object sender, EventArgs e)
        {
            try
            {
                ZoomIn();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التكبير: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر التصغير
        /// </summary>
        private void btnZoomOut_Click(object sender, EventArgs e)
        {
            try
            {
                ZoomOut();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصغير: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث تغيير مستوى التكبير من القائمة المنسدلة
        /// </summary>
        private void cmbZoom_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                ApplyZoomFromComboBox();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق التكبير: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر ملائمة الحجم
        /// </summary>
        private void btnZoomFit_Click(object sender, EventArgs e)
        {
            try
            {
                ZoomToFit();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ملائمة الحجم: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الصفحة الأولى
        /// </summary>
        private void btnFirstPage_Click(object sender, EventArgs e)
        {
            try
            {
                GoToFirstPage();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الانتقال للصفحة الأولى: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الصفحة السابقة
        /// </summary>
        private void btnPreviousPage_Click(object sender, EventArgs e)
        {
            try
            {
                GoToPreviousPage();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الانتقال للصفحة السابقة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الصفحة التالية
        /// </summary>
        private void btnNextPage_Click(object sender, EventArgs e)
        {
            try
            {
                GoToNextPage();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الانتقال للصفحة التالية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الصفحة الأخيرة
        /// </summary>
        private void btnLastPage_Click(object sender, EventArgs e)
        {
            try
            {
                GoToLastPage();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الانتقال للصفحة الأخيرة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الإغلاق
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            try
            {
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إغلاق الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث الضغط على مفتاح في حقل رقم الصفحة
        /// </summary>
        private void txtCurrentPage_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    GoToPage();
                    e.Handled = true;
                }
                else if (!char.IsDigit(e.KeyChar) && !char.IsControl(e.KeyChar))
                {
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة إدخال رقم الصفحة: {ex.Message}");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تهيئة الشاشة
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // إعداد قائمة التكبير
                cmbZoom.SelectedIndex = 3; // 100%
                
                // إعداد حوارات الطباعة
                printDialog1.AllowSomePages = true;
                printDialog1.AllowSelection = false;
                printDialog1.AllowCurrentPage = true;
                
                // إعداد معاينة الطباعة
                printPreviewControl1.AutoZoom = false;
                printPreviewControl1.Zoom = 1.0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد معاينة الطباعة
        /// </summary>
        private void SetupPrintPreview()
        {
            try
            {
                if (_printDocument != null)
                {
                    printPreviewControl1.Document = _printDocument;
                    printDialog1.Document = _printDocument;
                    pageSetupDialog1.Document = _printDocument;
                    
                    // تحديث العنوان
                    lblTitle.Text = $"معاينة الطباعة - {_documentTitle}";
                    this.Text = $"معاينة الطباعة - {_documentTitle}";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إعداد معاينة الطباعة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث معاينة الطباعة
        /// </summary>
        private void RefreshPreview()
        {
            try
            {
                if (_printDocument != null)
                {
                    printPreviewControl1.InvalidatePreview();
                    UpdatePageInfo();
                    UpdateUI();
                    UpdateStatus();
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "خطأ في تحديث المعاينة";
                throw new Exception($"فشل في تحديث معاينة الطباعة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث معلومات الصفحات
        /// </summary>
        private void UpdatePageInfo()
        {
            try
            {
                _currentPage = printPreviewControl1.StartPage + 1;
                // محاولة الحصول على العدد الإجمالي للصفحات
                // هذا قد يتطلب طباعة تجريبية لحساب الصفحات
                
                txtCurrentPage.Text = _currentPage.ToString();
                lblPageCount.Text = $"/ {_totalPages}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث معلومات الصفحات: {ex.Message}");
            }
        }

        /// <summary>
        /// طباعة المستند
        /// </summary>
        private void PrintDocument()
        {
            try
            {
                if (_printDocument == null)
                {
                    MessageBox.Show("لا يوجد مستند للطباعة.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (printDialog1.ShowDialog() == DialogResult.OK)
                {
                    _printDocument.Print();
                    lblStatus.Text = "تم إرسال المستند للطباعة";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في طباعة المستند: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض حوار إعداد الصفحة
        /// </summary>
        private void ShowPageSetup()
        {
            try
            {
                if (_printDocument == null)
                {
                    MessageBox.Show("لا يوجد مستند لإعداد الصفحة.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (pageSetupDialog1.ShowDialog() == DialogResult.OK)
                {
                    RefreshPreview();
                    lblStatus.Text = "تم تحديث إعدادات الصفحة";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إعداد الصفحة: {ex.Message}");
            }
        }

        #endregion

        #region Zoom Operations

        /// <summary>
        /// تكبير المعاينة
        /// </summary>
        private void ZoomIn()
        {
            try
            {
                if (_currentZoom < 4.0) // حد أقصى 400%
                {
                    _currentZoom += 0.25;
                    ApplyZoom();
                    UpdateZoomComboBox();
                    lblStatus.Text = $"التكبير: {(_currentZoom * 100):F0}%";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تكبير المعاينة: {ex.Message}");
            }
        }

        /// <summary>
        /// تصغير المعاينة
        /// </summary>
        private void ZoomOut()
        {
            try
            {
                if (_currentZoom > 0.25) // حد أدنى 25%
                {
                    _currentZoom -= 0.25;
                    ApplyZoom();
                    UpdateZoomComboBox();
                    lblStatus.Text = $"التكبير: {(_currentZoom * 100):F0}%";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تصغير المعاينة: {ex.Message}");
            }
        }

        /// <summary>
        /// ملائمة حجم المعاينة
        /// </summary>
        private void ZoomToFit()
        {
            try
            {
                printPreviewControl1.AutoZoom = true;
                cmbZoom.SelectedIndex = 7; // "ملائمة العرض"
                lblStatus.Text = "تم ملائمة الحجم";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في ملائمة حجم المعاينة: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق مستوى التكبير
        /// </summary>
        private void ApplyZoom()
        {
            try
            {
                printPreviewControl1.AutoZoom = false;
                printPreviewControl1.Zoom = _currentZoom;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تطبيق التكبير: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق التكبير من القائمة المنسدلة
        /// </summary>
        private void ApplyZoomFromComboBox()
        {
            try
            {
                string selectedZoom = cmbZoom.SelectedItem?.ToString();

                if (string.IsNullOrEmpty(selectedZoom))
                    return;

                if (selectedZoom == "ملائمة العرض")
                {
                    printPreviewControl1.AutoZoom = true;
                    lblStatus.Text = "ملائمة العرض";
                }
                else if (selectedZoom == "ملائمة الصفحة")
                {
                    printPreviewControl1.AutoZoom = true;
                    lblStatus.Text = "ملائمة الصفحة";
                }
                else if (selectedZoom.EndsWith("%"))
                {
                    string percentStr = selectedZoom.Replace("%", "");
                    if (double.TryParse(percentStr, out double percent))
                    {
                        _currentZoom = percent / 100.0;
                        ApplyZoom();
                        lblStatus.Text = $"التكبير: {percent:F0}%";
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تطبيق التكبير من القائمة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث قائمة التكبير
        /// </summary>
        private void UpdateZoomComboBox()
        {
            try
            {
                int zoomPercent = (int)(_currentZoom * 100);
                string zoomText = $"{zoomPercent}%";

                // البحث عن القيمة في القائمة
                for (int i = 0; i < cmbZoom.Items.Count; i++)
                {
                    if (cmbZoom.Items[i].ToString() == zoomText)
                    {
                        cmbZoom.SelectedIndex = i;
                        return;
                    }
                }

                // إذا لم توجد القيمة، إضافتها مؤقتاً
                cmbZoom.Text = zoomText;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث قائمة التكبير: {ex.Message}");
            }
        }

        #endregion

        #region Page Navigation

        /// <summary>
        /// الانتقال للصفحة الأولى
        /// </summary>
        private void GoToFirstPage()
        {
            try
            {
                printPreviewControl1.StartPage = 0;
                UpdatePageInfo();
                UpdateUI();
                lblStatus.Text = "الصفحة الأولى";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في الانتقال للصفحة الأولى: {ex.Message}");
            }
        }

        /// <summary>
        /// الانتقال للصفحة السابقة
        /// </summary>
        private void GoToPreviousPage()
        {
            try
            {
                if (printPreviewControl1.StartPage > 0)
                {
                    printPreviewControl1.StartPage--;
                    UpdatePageInfo();
                    UpdateUI();
                    lblStatus.Text = $"الصفحة {_currentPage}";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في الانتقال للصفحة السابقة: {ex.Message}");
            }
        }

        /// <summary>
        /// الانتقال للصفحة التالية
        /// </summary>
        private void GoToNextPage()
        {
            try
            {
                printPreviewControl1.StartPage++;
                UpdatePageInfo();
                UpdateUI();
                lblStatus.Text = $"الصفحة {_currentPage}";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في الانتقال للصفحة التالية: {ex.Message}");
            }
        }

        /// <summary>
        /// الانتقال للصفحة الأخيرة
        /// </summary>
        private void GoToLastPage()
        {
            try
            {
                // محاولة الانتقال لآخر صفحة
                if (_totalPages > 1)
                {
                    printPreviewControl1.StartPage = _totalPages - 1;
                    UpdatePageInfo();
                    UpdateUI();
                    lblStatus.Text = "الصفحة الأخيرة";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في الانتقال للصفحة الأخيرة: {ex.Message}");
            }
        }

        /// <summary>
        /// الانتقال لصفحة محددة
        /// </summary>
        private void GoToPage()
        {
            try
            {
                if (int.TryParse(txtCurrentPage.Text, out int pageNumber))
                {
                    if (pageNumber >= 1 && pageNumber <= _totalPages)
                    {
                        printPreviewControl1.StartPage = pageNumber - 1;
                        UpdatePageInfo();
                        UpdateUI();
                        lblStatus.Text = $"الصفحة {pageNumber}";
                    }
                    else
                    {
                        MessageBox.Show($"رقم الصفحة يجب أن يكون بين 1 و {_totalPages}.", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCurrentPage.Text = _currentPage.ToString();
                    }
                }
                else
                {
                    txtCurrentPage.Text = _currentPage.ToString();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في الانتقال للصفحة المحددة: {ex.Message}");
            }
        }

        #endregion

        #region UI Updates

        /// <summary>
        /// تحديث واجهة المستخدم
        /// </summary>
        private void UpdateUI()
        {
            try
            {
                bool hasDocument = _printDocument != null;

                // تحديث حالة الأزرار
                btnPrint.Enabled = hasDocument;
                btnPageSetup.Enabled = hasDocument;
                btnZoomIn.Enabled = hasDocument && _currentZoom < 4.0;
                btnZoomOut.Enabled = hasDocument && _currentZoom > 0.25;
                btnZoomFit.Enabled = hasDocument;
                cmbZoom.Enabled = hasDocument;

                // تحديث أزرار التنقل
                btnFirstPage.Enabled = hasDocument && _currentPage > 1;
                btnPreviousPage.Enabled = hasDocument && _currentPage > 1;
                btnNextPage.Enabled = hasDocument && _currentPage < _totalPages;
                btnLastPage.Enabled = hasDocument && _currentPage < _totalPages;
                txtCurrentPage.Enabled = hasDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث شريط الحالة
        /// </summary>
        private void UpdateStatus()
        {
            try
            {
                if (_printDocument != null)
                {
                    lblDocumentInfo.Text = $"المستند: {_documentTitle} | الصفحة: {_currentPage} من {_totalPages}";
                }
                else
                {
                    lblDocumentInfo.Text = "لا يوجد مستند";
                }
            }
            catch (Exception ex)
            {
                lblDocumentInfo.Text = "خطأ في معلومات المستند";
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث شريط الحالة: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// تعيين مستند الطباعة
        /// </summary>
        /// <param name="printDocument">مستند الطباعة</param>
        /// <param name="documentTitle">عنوان المستند</param>
        public void SetPrintDocument(PrintDocument printDocument, string documentTitle = null)
        {
            try
            {
                _printDocument = printDocument;
                _documentTitle = documentTitle ?? "مستند";

                if (this.IsHandleCreated)
                {
                    SetupPrintPreview();
                    RefreshPreview();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعيين مستند الطباعة: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض معاينة الطباعة لمستند
        /// </summary>
        /// <param name="printDocument">مستند الطباعة</param>
        /// <param name="documentTitle">عنوان المستند</param>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>شاشة معاينة الطباعة</returns>
        public static PrintPreviewForm ShowPrintPreview(PrintDocument printDocument,
            string documentTitle = null, IWin32Window parent = null)
        {
            try
            {
                var previewForm = new PrintPreviewForm(printDocument, documentTitle);

                if (parent != null)
                {
                    previewForm.Show(parent);
                }
                else
                {
                    previewForm.Show();
                }

                return previewForm;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض معاينة الطباعة: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// عرض معاينة الطباعة كحوار
        /// </summary>
        /// <param name="printDocument">مستند الطباعة</param>
        /// <param name="documentTitle">عنوان المستند</param>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>نتيجة الحوار</returns>
        public static DialogResult ShowPrintPreviewDialog(PrintDocument printDocument,
            string documentTitle = null, IWin32Window parent = null)
        {
            try
            {
                using (var previewForm = new PrintPreviewForm(printDocument, documentTitle))
                {
                    return previewForm.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض معاينة الطباعة: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        #endregion
    }
}
