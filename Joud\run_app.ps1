Write-Host "Building and Running Joud POS Application..." -ForegroundColor Green

# Change to project directory
Set-Location "Joud"

# Restore packages
Write-Host "Restoring packages..." -ForegroundColor Yellow
dotnet restore

# Build the project
Write-Host "Building project..." -ForegroundColor Yellow
dotnet build --configuration Release

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "Starting application..." -ForegroundColor Yellow
    dotnet run --configuration Release
} else {
    Write-Host "Build failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
