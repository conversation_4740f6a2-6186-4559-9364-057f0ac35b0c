using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;
using Joud.Models;
using Joud.Services;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة إدارة المستخدمين - Users Management Form
    /// تسمح بإضافة وتعديل وحذف بيانات المستخدمين وإدارة الصلاحيات
    /// </summary>
    public partial class UsersForm : Form
    {
        #region Private Fields

        private readonly UserService _userService;
        private readonly RoleService _roleService;
        private List<User> _users;
        private List<Role> _roles;
        private User _selectedUser;
        private bool _isEditing = false;
        private bool _hasChanges = false;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ شاشة إدارة المستخدمين
        /// </summary>
        public UsersForm()
        {
            InitializeComponent();
            _userService = new UserService();
            _roleService = new RoleService();
            InitializeForm();
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل الشاشة
        /// </summary>
        private void UsersForm_Load(object sender, EventArgs e)
        {
            try
            {
                LoadRoles();
                LoadUsers();
                SetupDataGridView();
                UpdateUI();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Button Events

        /// <summary>
        /// حدث النقر على زر إضافة مستخدم جديد
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                StartAddMode();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء إضافة مستخدم جديد: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تعديل المستخدم
        /// </summary>
        private void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedUser != null)
                {
                    StartEditMode();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار مستخدم للتعديل.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء تعديل المستخدم: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر حذف المستخدم
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedUser != null)
                {
                    DeleteUser();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار مستخدم للحذف.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الحفظ
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                SaveUser();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المستخدم: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الإلغاء
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                CancelOperation();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء العملية: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر البحث
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                SearchUsers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadUsers();
                ClearForm();
                lblStatus.Text = "تم تحديث البيانات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر إعادة تعيين كلمة المرور
        /// </summary>
        private void btnResetPassword_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedUser != null)
                {
                    ResetUserPassword();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region DataGridView Events

        /// <summary>
        /// حدث تغيير التحديد في جدول المستخدمين
        /// </summary>
        private void dgvUsers_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dgvUsers.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvUsers.SelectedRows[0];
                    if (selectedRow.DataBoundItem is User user)
                    {
                        _selectedUser = user;
                        DisplayUserDetails(user);
                        UpdateUI();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تغيير التحديد: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على جدول المستخدمين
        /// </summary>
        private void dgvUsers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && _selectedUser != null)
                {
                    StartEditMode();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح المستخدم للتعديل: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region TextBox Events

        /// <summary>
        /// حدث تغيير نص البحث
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    SearchUsers();
                }
                else
                {
                    LoadUsers();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث التلقائي: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث تغيير البيانات في النموذج
        /// </summary>
        private void FormData_Changed(object sender, EventArgs e)
        {
            if (_isEditing)
            {
                _hasChanges = true;
                UpdateUI();
            }
        }

        /// <summary>
        /// حدث تغيير اسم المستخدم للتحقق من التفرد
        /// </summary>
        private void txtUsername_Leave(object sender, EventArgs e)
        {
            try
            {
                if (_isEditing && !string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    if (IsUsernameExists(txtUsername.Text))
                    {
                        txtUsername.BackColor = Color.LightPink;
                        lblStatus.Text = "اسم المستخدم موجود مسبقاً";
                    }
                    else
                    {
                        txtUsername.BackColor = SystemColors.Window;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من اسم المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// حدث تغيير البريد الإلكتروني للتحقق من الصحة
        /// </summary>
        private void txtEmail_Leave(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(txtEmail.Text))
                {
                    if (!IsValidEmail(txtEmail.Text))
                    {
                        txtEmail.BackColor = Color.LightPink;
                        lblStatus.Text = "البريد الإلكتروني غير صحيح";
                    }
                    else
                    {
                        txtEmail.BackColor = SystemColors.Window;
                    }
                }
                else
                {
                    txtEmail.BackColor = SystemColors.Window;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من البريد الإلكتروني: {ex.Message}");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تحميل قائمة المستخدمين
        /// </summary>
        private async void LoadUsers()
        {
            try
            {
                lblStatus.Text = "جاري تحميل المستخدمين...";

                var users = await _userService.GetAllUsersAsync();

                dgvUsers.DataSource = users.ToList();

                UpdateStatistics();

                lblStatus.Text = $"تم تحميل {users.Count()} مستخدم";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في تحميل البيانات";
            }
        }

        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void ClearForm()
        {
            try
            {
                 txtUserCode.Clear(); // تم تعطيل هذا السطر لأن txtUserCode غير موجود
                txtUsername.Clear();
                txtFullName.Clear();
                txtEmail.Clear();
                txtPhone.Clear();
                txtPassword.Clear();
                txtConfirmPassword.Clear();
                cmbRole.SelectedIndex = -1;
                chkIsActive.Checked = true;

                _selectedUser = null;
                _isEditing = false;

                SetEditMode(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مسح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تهيئة الشاشة
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // ربط أحداث تغيير البيانات
                txtUsername.TextChanged += FormData_Changed;
                txtFullName.TextChanged += FormData_Changed;
                txtEmail.TextChanged += FormData_Changed;
                txtPhone.TextChanged += FormData_Changed;
                txtPassword.TextChanged += FormData_Changed;
                txtConfirmPassword.TextChanged += FormData_Changed;
                cmbRole.SelectedIndexChanged += FormData_Changed;
                chkIsActive.CheckedChanged += FormData_Changed;

                // ربط أحداث التحقق
                txtUsername.Leave += txtUsername_Leave;
                txtEmail.Leave += txtEmail_Leave;

                // إعداد التحقق من صحة البيانات
                SetupValidation();
                
                // تعيين القيم الافتراضية
                chkIsActive.Checked = true;
                dtpCreatedDate.Value = DateTime.Now;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الشاشة: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد جدول البيانات
        /// </summary>
        private void SetupDataGridView()
        {
            try
            {
                dgvUsers.AutoGenerateColumns = false;
                dgvUsers.Columns.Clear();

                // إضافة الأعمدة
                dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Username",
                    HeaderText = "اسم المستخدم",
                    DataPropertyName = "Username",
                    Width = 150,
                    ReadOnly = true
                });

                dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "FullName",
                    HeaderText = "الاسم الكامل",
                    DataPropertyName = "FullName",
                    Width = 200,
                    ReadOnly = true
                });

                dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Email",
                    HeaderText = "البريد الإلكتروني",
                    DataPropertyName = "Email",
                    Width = 200,
                    ReadOnly = true
                });

                dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "RoleName",
                    HeaderText = "الدور",
                    DataPropertyName = "RoleName",
                    Width = 120,
                    ReadOnly = true
                });

                dgvUsers.Columns.Add(new DataGridViewCheckBoxColumn
                {
                    Name = "IsActive",
                    HeaderText = "نشط",
                    DataPropertyName = "IsActive",
                    Width = 60,
                    ReadOnly = true
                });

                dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "LastLoginDate",
                    HeaderText = "آخر دخول",
                    DataPropertyName = "LastLoginDate",
                    Width = 150,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd HH:mm" }
                });

                // إعداد خصائص الجدول
                dgvUsers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dgvUsers.MultiSelect = false;
                dgvUsers.ReadOnly = true;
                dgvUsers.AllowUserToAddRows = false;
                dgvUsers.AllowUserToDeleteRows = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد جدول البيانات: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد التحقق من صحة البيانات
        /// </summary>
        private void SetupValidation()
        {
            try
            {
                // إعداد التحقق من الحقول المطلوبة
                txtUsername.Tag = "required";
                txtFullName.Tag = "required";
                txtPassword.Tag = "required";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد التحقق: {ex.Message}");
            }
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// التحقق من صحة النموذج
        /// </summary>
        /// <returns>true إذا كان النموذج صحيح</returns>
        private bool ValidateForm()
        {
            try
            {
                // التحقق من الحقول المطلوبة
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(txtFullName.Text))
                {
                    MessageBox.Show("يرجى إدخال الاسم الكامل.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtFullName.Focus();
                    return false;
                }

                // التحقق من كلمة المرور في وضع الإضافة
                if (_selectedUser == null)
                {
                    if (string.IsNullOrWhiteSpace(txtPassword.Text))
                    {
                        MessageBox.Show("يرجى إدخال كلمة المرور.", "خطأ في التحقق",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtPassword.Focus();
                        return false;
                    }

                    if (txtPassword.Text != txtConfirmPassword.Text)
                    {
                        MessageBox.Show("كلمة المرور وتأكيدها غير متطابقتين.", "خطأ في التحقق",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtConfirmPassword.Focus();
                        return false;
                    }

                    if (txtPassword.Text.Length < 6)
                    {
                        MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل.", "خطأ في التحقق",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtPassword.Focus();
                        return false;
                    }
                }

                // التحقق من تفرد اسم المستخدم
                if (IsUsernameExists(txtUsername.Text))
                {
                    MessageBox.Show("اسم المستخدم موجود مسبقاً.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return false;
                }

                // التحقق من البريد الإلكتروني
                if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
                {
                    MessageBox.Show("البريد الإلكتروني غير صحيح.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return false;
                }

                // التحقق من اختيار الدور
                if (cmbRole.SelectedIndex == -1)
                {
                    MessageBox.Show("يرجى اختيار دور للمستخدم.", "خطأ في التحقق",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbRole.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من النموذج: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>true إذا كان موجود</returns>
        private bool IsUsernameExists(string username)
        {
            try
            {
                return _users.Any(u =>
                    u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) &&
                    (_selectedUser == null || u.UserId != _selectedUser.UserId));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>true إذا كان صحيح</returns>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// إنشاء كائن المستخدم من النموذج
        /// </summary>
        /// <returns>كائن المستخدم</returns>
        private User CreateUserFromForm()
        {
            try
            {
                var user = new User
                {
                    Username = txtUsername.Text.Trim(),
                    FullName = txtFullName.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    IsActive = chkIsActive.Checked,
                    RoleId = (int)cmbRole.SelectedValue,
                    CreatedDate = DateTime.Now
                };

                // تشفير كلمة المرور إذا تم إدخالها
                if (!string.IsNullOrEmpty(txtPassword.Text))
                {
                    user.PasswordHash = HashPassword(txtPassword.Text);
                }

                return user;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء كائن المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة</returns>
        private string HashPassword(string password)
        {
            try
            {
                using (var sha256 = SHA256.Create())
                {
                    byte[] hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                    return Convert.ToBase64String(hashedBytes);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تشفير كلمة المرور: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// عرض شاشة إدارة المستخدمين
        /// </summary>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>نتيجة الحوار</returns>
        public static DialogResult ShowUsersForm(IWin32Window parent = null)
        {
            try
            {
                using (var usersForm = new UsersForm())
                {
                    return usersForm.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض شاشة المستخدمين: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// الحصول على المستخدم المحدد
        /// </summary>
        /// <returns>المستخدم المحدد</returns>
        public User GetSelectedUser()
        {
            return _selectedUser;
        }

        #endregion

        #region CRUD Operations (Continued)

        /// <summary>
        /// بدء وضع التعديل
        /// </summary>
        private void StartEditMode()
        {
            try
            {
                if (_selectedUser == null)
                {
                    MessageBox.Show("يرجى اختيار مستخدم للتعديل.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                _isEditing = true;
                _hasChanges = false;

                // تركيز على أول حقل
                txtFullName.Focus();

                // إخفاء حقول كلمة المرور في وضع التعديل
                lblPassword.Visible = false;
                txtPassword.Visible = false;
                lblConfirmPassword.Visible = false;
                txtConfirmPassword.Visible = false;

                UpdateUI();
                lblStatus.Text = $"وضع تعديل المستخدم: {_selectedUser.Username}";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في بدء وضع التعديل: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ المستخدم
        /// </summary>
        private void SaveUser()
        {
            try
            {
                if (!ValidateForm())
                {
                    return;
                }

                var user = CreateUserFromForm();

                if (_selectedUser == null)
                {
                    // إضافة مستخدم جديد
                    var newUser = _userService.AddUser(user);
                    _users.Add(newUser);

                    MessageBox.Show("تم إضافة المستخدم بنجاح.", "نجح الحفظ",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = "تم إضافة المستخدم بنجاح";
                }
                else
                {
                    // تعديل مستخدم موجود
                    user.UserId = _selectedUser.UserId;
                    user.CreatedDate = _selectedUser.CreatedDate;
                    user.ModifiedDate = DateTime.Now;

                    // الاحتفاظ بكلمة المرور الحالية إذا لم يتم تغييرها
                    if (string.IsNullOrEmpty(txtPassword.Text))
                    {
                        user.PasswordHash = _selectedUser.PasswordHash;
                    }

                    _userService.UpdateUser(user);

                    // تحديث القائمة
                    var index = _users.FindIndex(u => u.UserId == user.UserId);
                    if (index >= 0)
                    {
                        _users[index] = user;
                    }

                    MessageBox.Show("تم تعديل المستخدم بنجاح.", "نجح الحفظ",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = "تم تعديل المستخدم بنجاح";
                }

                // إعادة تحميل البيانات وإنهاء وضع التعديل
                LoadUsers();
                ClearForm();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المستخدم: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف المستخدم
        /// </summary>
        private void DeleteUser()
        {
            try
            {
                if (_selectedUser == null)
                {
                    MessageBox.Show("يرجى اختيار مستخدم للحذف.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد حذف المستخدم '{_selectedUser.Username}'؟\n\nتحذير: لا يمكن التراجع عن هذه العملية.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _userService.DeleteUser(_selectedUser.UserId);

                    MessageBox.Show("تم حذف المستخدم بنجاح.", "نجح الحذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // إعادة تحميل البيانات
                    LoadUsers();
                    ClearForm();
                    UpdateStatistics();

                    lblStatus.Text = "تم حذف المستخدم بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void CancelOperation()
        {
            try
            {
                if (_hasChanges)
                {
                    var result = MessageBox.Show(
                        "هناك تغييرات لم يتم حفظها. هل تريد المتابعة بدون حفظ؟",
                        "تأكيد الإلغاء",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.No)
                    {
                        return;
                    }
                }

                if (_selectedUser != null)
                {
                    DisplayUserDetails(_selectedUser);
                }
                else
                {
                    ClearForm();
                }

                _isEditing = false;
                _hasChanges = false;
                UpdateUI();

                lblStatus.Text = "تم إلغاء العملية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء العملية: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة مرور المستخدم
        /// </summary>
        private void ResetUserPassword()
        {
            try
            {
                if (_selectedUser == null)
                {
                    MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد إعادة تعيين كلمة مرور المستخدم '{_selectedUser.Username}'؟\n\nسيتم تعيين كلمة المرور إلى '123456'.",
                    "تأكيد إعادة التعيين",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    string newPassword = "123456";
                    string hashedPassword = HashPassword(newPassword);

                    _userService.ResetUserPassword(_selectedUser.UserId, hashedPassword);

                    MessageBox.Show($"تم إعادة تعيين كلمة المرور بنجاح.\nكلمة المرور الجديدة: {newPassword}",
                        "نجحت العملية", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = "تم إعادة تعيين كلمة المرور";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region UI Helper Methods

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                if (_users != null)
                {
                    int totalUsers = _users.Count;
                    int activeUsers = _users.Count(u => u.IsActive);
                    int inactiveUsers = totalUsers - activeUsers;

                    lblTotalUsers.Text = totalUsers.ToString();
                    lblActiveUsers.Text = activeUsers.ToString();
                    lblInactiveUsers.Text = inactiveUsers.ToString();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث واجهة المستخدم
        /// </summary>
        private void UpdateUI()
        {
            try
            {
                // تحديث حالة الأزرار
                btnAdd.Enabled = !_isEditing;
                btnEdit.Enabled = !_isEditing && _selectedUser != null;
                btnDelete.Enabled = !_isEditing && _selectedUser != null;
                btnSave.Enabled = _isEditing;
                btnCancel.Enabled = _isEditing;
                btnRefresh.Enabled = !_isEditing;
                btnResetPassword.Enabled = !_isEditing && _selectedUser != null;

                // تحديث حالة الحقول
                bool enableFields = _isEditing;
                txtUsername.ReadOnly = !enableFields || (_selectedUser != null); // منع تعديل اسم المستخدم
                txtFullName.ReadOnly = !enableFields;
                txtEmail.ReadOnly = !enableFields;
                txtPhone.ReadOnly = !enableFields;
                txtPassword.ReadOnly = !enableFields;
                txtConfirmPassword.ReadOnly = !enableFields;
                cmbRole.Enabled = enableFields;
                chkIsActive.Enabled = enableFields;

                // تحديث لون الخلفية للحقول
                Color backColor = enableFields ? SystemColors.Window : SystemColors.Control;
                txtUsername.BackColor = (_selectedUser != null && enableFields) ? SystemColors.Control : backColor;
                txtFullName.BackColor = backColor;
                txtEmail.BackColor = backColor;
                txtPhone.BackColor = backColor;
                txtPassword.BackColor = backColor;
                txtConfirmPassword.BackColor = backColor;

                // تحديث حالة الجدول
                dgvUsers.Enabled = !_isEditing;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الأدوار
        /// </summary>
        private async void LoadRoles()
        {
            try
            {
                var roles = await _roleService.GetAllRolesAsync();

                cmbRole.DataSource = roles.ToList();
                cmbRole.DisplayMember = "RoleName";
                cmbRole.ValueMember = "RoleId";
                cmbRole.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأدوار: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل المستخدم
        /// </summary>
        /// <param name="user">المستخدم</param>
        private void DisplayUserDetails(User user)
        {
            try
            {
                if (user == null) return;

                // عرض معرف المستخدم في العنوان أو حفظه في متغير
                // يمكن إضافة txtUserCode لاحقاً إذا لزم الأمر
                txtUsername.Text = user.Username;
                txtFullName.Text = user.FullName;
                txtEmail.Text = user.Email ?? "";
                txtPhone.Text = user.Phone ?? "";
                chkIsActive.Checked = user.IsActive;

                // تعيين الدور
                if (user.RoleId > 0)
                {
                    cmbRole.SelectedValue = user.RoleId;
                }

                // تعيين تاريخ الإنشاء
                if (user.CreatedDate != default(DateTime))
                {
                    dtpCreatedDate.Value = user.CreatedDate;
                }

                // مسح حقول كلمة المرور
                txtPassword.Clear();
                txtConfirmPassword.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تفاصيل المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// بدء وضع الإضافة
        /// </summary>
        private void StartAddMode()
        {
            try
            {
                ClearForm();
                _isEditing = true;
                _hasChanges = false;
                _selectedUser = null!;

                // إظهار حقول كلمة المرور
                lblPassword.Visible = true;
                txtPassword.Visible = true;
                lblConfirmPassword.Visible = true;
                txtConfirmPassword.Visible = true;

                // تركيز على أول حقل
                txtUsername.Focus();

                UpdateUI();
                lblStatus.Text = "وضع إضافة مستخدم جديد";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في بدء وضع الإضافة: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن المستخدمين
        /// </summary>
        private async void SearchUsers()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    LoadUsers();
                    return;
                }

                lblStatus.Text = "جاري البحث...";

                var searchTerm = txtSearch.Text.Trim();
                var users = await _userService.SearchUsersAsync(searchTerm);

                dgvUsers.DataSource = users.ToList();
                _users = users.ToList();

                UpdateStatistics();

                lblStatus.Text = $"تم العثور على {users.Count()} مستخدم";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في البحث";
            }
        }

        /// <summary>
        /// تعيين وضع التحرير
        /// </summary>
        /// <param name="editMode">وضع التحرير</param>
        private void SetEditMode(bool editMode)
        {
            _isEditing = editMode;
            UpdateUI();
        }

        /// <summary>
        /// حدث تغيير فلتر الدور
        /// </summary>
        private void cmbRoleFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                // تطبيق فلتر الدور على قائمة المستخدمين
                if (cmbRoleFilter.SelectedIndex > 0)
                {
                    var selectedRole = cmbRoleFilter.SelectedItem as Role;
                    if (selectedRole != null)
                    {
                        var filteredUsers = _users.Where(u => u.RoleId == selectedRole.RoleId).ToList();
                        dgvUsers.DataSource = filteredUsers;
                    }
                }
                else
                {
                    dgvUsers.DataSource = _users;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فلترة الأدوار: {ex.Message}");
            }
        }

        #endregion
    }
}
