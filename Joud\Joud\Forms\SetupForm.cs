using Joud.DAL;
using Joud.Models;
using Joud.Utilities;
using Microsoft.EntityFrameworkCore;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة الإعداد الأولي للتطبيق
    /// Initial Setup Form
    /// </summary>
    public partial class SetupForm : Form
    {
        private string? _connectionString;

        public SetupForm()
        {
            InitializeComponent();
            InitializeFormSettings();
        }

        private void InitializeFormSettings()
        {
            // إعدادات النموذج الأساسية
            this.Text = "إعداد نظام جود للمحاسبة المالية - الإعداد الأولي";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // تطبيق الألوان والتصميم
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private async void btnSetup_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInputs())
                    return;

                // تعطيل الأزرار أثناء العملية
                SetControlsEnabled(false);
                lblStatus.Text = "جاري إعداد النظام...";
                lblStatus.ForeColor = Color.Blue;

                // إنشاء نص الاتصال
                _connectionString = DatabaseHelper.GetConnectionString(
                    txtServerName.Text.Trim(),
                    "JoudAccountingDB",
                    true
                );

                // إنشاء قاعدة البيانات (مع حذف القديمة إن وجدت)
                lblStatus.Text = "جاري إنشاء قاعدة البيانات...";
                Application.DoEvents();

                bool dbCreated = await DatabaseHelper.CreateDatabaseAsync(_connectionString, "JoudAccountingDB");
                if (!dbCreated)
                {
                    ShowError("فشل في إنشاء قاعدة البيانات");
                    return;
                }

                // تنفيذ ملفات SQL
                lblStatus.Text = "جاري إنشاء الجداول...";
                Application.DoEvents();

                string sqlFile1 = Path.Combine(Application.StartupPath, "Database", "JoudAccountingDB.sql");
                string sqlFile2 = Path.Combine(Application.StartupPath, "Database", "JoudAccountingDB_Part2.sql");
                string sqlFile3 = Path.Combine(Application.StartupPath, "Database", "AddMissingColumns.sql");
                string sqlFile4 = Path.Combine(Application.StartupPath, "Database", "UpdateTables.sql");
                string sqlFile5 = Path.Combine(Application.StartupPath, "Database", "InitialData.sql");

                try
                {
                    // إنشاء الجداول الأساسية
                    lblStatus.Text = "جاري إنشاء الجداول الأساسية...";
                    Application.DoEvents();
                    await DatabaseHelper.ExecuteSqlFileAsync(_connectionString, sqlFile1);
                    await DatabaseHelper.ExecuteSqlFileAsync(_connectionString, sqlFile2);

                    // إضافة الأعمدة المفقودة
                    lblStatus.Text = "جاري إضافة الأعمدة المفقودة...";
                    Application.DoEvents();
                    await DatabaseHelper.ExecuteSqlFileAsync(_connectionString, sqlFile3);

                    // تحديث الجداول بإضافة أعمدة إضافية
                    lblStatus.Text = "جاري تحديث الجداول...";
                    Application.DoEvents();
                    await DatabaseHelper.ExecuteSqlFileAsync(_connectionString, sqlFile4);

                    // إدراج البيانات الأولية
                    lblStatus.Text = "جاري إدراج البيانات الأولية...";
                    Application.DoEvents();
                    await DatabaseHelper.ExecuteSqlFileAsync(_connectionString, sqlFile5);
                }
                catch (Exception ex)
                {
                    ShowError($"فشل في تنفيذ ملفات SQL: {ex.Message}");
                    return;
                }

                // إنشاء بيانات الشركة والمستخدم الأول
                lblStatus.Text = "جاري إنشاء بيانات الشركة...";
                Application.DoEvents();

                await CreateCompanyAndAdminUser();

                // حفظ إعدادات الاتصال
                SaveConnectionSettings();

                lblStatus.Text = "تم إعداد النظام بنجاح!";
                lblStatus.ForeColor = Color.Green;

                MessageBox.Show(
                    "تم إعداد نظام جود للمحاسبة المالية بنجاح!\n\nسيتم الآن إعادة تشغيل التطبيق.",
                    "نجح الإعداد",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );

                // إعادة تشغيل التطبيق
                Application.Restart();
            }
            catch (Exception ex)
            {
                string errorMessage = $"حدث خطأ أثناء الإعداد: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية: {ex.InnerException.Message}";
                }
                ShowError(errorMessage);
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private bool ValidateInputs()
        {
            // التحقق من اسم الخادم
            if (string.IsNullOrWhiteSpace(txtServerName.Text))
            {
                ShowError("يرجى إدخال اسم خادم قاعدة البيانات");
                txtServerName.Focus();
                return false;
            }

            // التحقق من بيانات الشركة
            if (string.IsNullOrWhiteSpace(txtCompanyName.Text))
            {
                ShowError("يرجى إدخال اسم الشركة");
                txtCompanyName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtCountry.Text))
            {
                ShowError("يرجى إدخال البلد");
                txtCountry.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbCurrency.Text))
            {
                ShowError("يرجى اختيار العملة");
                cmbCurrency.Focus();
                return false;
            }

            // التحقق من بيانات المستخدم
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                ShowError("يرجى إدخال اسم المستخدم");
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowError("يرجى إدخال كلمة المرور");
                txtPassword.Focus();
                return false;
            }

            if (txtPassword.Text != txtConfirmPassword.Text)
            {
                ShowError("كلمة المرور وتأكيد كلمة المرور غير متطابقتين");
                txtConfirmPassword.Focus();
                return false;
            }

            // التحقق من قوة كلمة المرور
            var (isValid, message) = PasswordHelper.ValidatePasswordStrength(txtPassword.Text);
            if (!isValid)
            {
                ShowError(message);
                txtPassword.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                ShowError("يرجى إدخال الاسم الكامل");
                txtFullName.Focus();
                return false;
            }

            return true;
        }

        private async Task CreateCompanyAndAdminUser()
        {
            try
            {
                var options = new DbContextOptionsBuilder<JoudDbContext>()
                    .UseSqlServer(_connectionString)
                    .EnableSensitiveDataLogging()
                    .EnableDetailedErrors()
                    .Options;

                using var context = new JoudDbContext(options);

                // التحقق من الاتصال بقاعدة البيانات
                try
                {
                    await context.Database.OpenConnectionAsync();
                    await context.Database.CloseConnectionAsync();
                }
                catch (Exception ex)
                {
                    throw new Exception($"فشل في الاتصال بقاعدة البيانات: {ex.Message}", ex);
                }

                // إنشاء الشركة
                var company = new Company
                {
                    CompanyName = txtCompanyName.Text.Trim(),
                    CompanyNameEn = string.IsNullOrWhiteSpace(txtCompanyNameEn.Text) ? null : txtCompanyNameEn.Text.Trim(),
                    Address = string.IsNullOrWhiteSpace(txtAddress.Text) ? null : txtAddress.Text.Trim(),
                    Phone = string.IsNullOrWhiteSpace(txtPhone.Text) ? null : txtPhone.Text.Trim(),
                    Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text.Trim(),
                    Country = string.IsNullOrWhiteSpace(txtCountry.Text) ? null : txtCountry.Text.Trim(),
                    Currency = cmbCurrency.Text.Trim(),
                    IsActive = true
                    // لا نحدد CreatedDate لأنه سيتم تعيينه تلقائياً من قاعدة البيانات
                };

                context.Companies.Add(company);

                try
                {
                    await context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    string errorDetails = ex.Message;
                    if (ex.InnerException != null)
                    {
                        errorDetails += $"\n\nتفاصيل إضافية: {ex.InnerException.Message}";

                        // التحقق من أخطاء الأعمدة المفقودة
                        if (ex.InnerException.Message.Contains("Invalid column name"))
                        {
                            errorDetails += "\n\nيبدو أن هناك أعمدة مفقودة في قاعدة البيانات. تأكد من تنفيذ ملفات SQL المحدثة.";
                        }
                    }
                    throw new Exception($"خطأ في حفظ بيانات الشركة: {errorDetails}", ex);
                }

                // الحصول على دور المدير
                var adminRole = await context.UserRoles.FirstOrDefaultAsync(r => r.RoleName == "مدير النظام");
                if (adminRole == null)
                {
                    throw new Exception("لم يتم العثور على دور مدير النظام. تأكد من تنفيذ ملف InitialData.sql بنجاح.");
                }

                // التحقق من عدم وجود اسم المستخدم مسبقاً
                var existingUser = await context.Users.FirstOrDefaultAsync(u => u.Username == txtUsername.Text.Trim());
                if (existingUser != null)
                {
                    throw new Exception($"اسم المستخدم '{txtUsername.Text.Trim()}' موجود مسبقاً.");
                }

                // إنشاء المستخدم الأول (المدير)
                var (hashedPassword, salt) = PasswordHelper.CreateHashedPassword(txtPassword.Text);

                var adminUser = new User
                {
                    Username = txtUsername.Text.Trim(),
                    PasswordHash = hashedPassword,
                    PasswordSalt = salt,
                    FullName = txtFullName.Text.Trim(),
                    Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text.Trim(),
                    RoleId = adminRole.RoleId,
                    CompanyId = company.CompanyId,
                    IsActive = true,
                    CreatedBy = null // المستخدم الأول لا يوجد له منشئ
                    // لا نحدد CreatedDate لأنه سيتم تعيينه تلقائياً من قاعدة البيانات
                };

                context.Users.Add(adminUser);

                try
                {
                    await context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    string errorDetails = ex.Message;
                    if (ex.InnerException != null)
                    {
                        errorDetails += $"\n\nتفاصيل إضافية: {ex.InnerException.Message}";

                        // التحقق من أخطاء الأعمدة المفقودة
                        if (ex.InnerException.Message.Contains("Invalid column name"))
                        {
                            errorDetails += "\n\nيبدو أن هناك أعمدة مفقودة في قاعدة البيانات. تأكد من تنفيذ ملفات SQL المحدثة.";
                        }
                    }
                    throw new Exception($"خطأ في حفظ بيانات المستخدم: {errorDetails}", ex);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء بيانات الشركة والمستخدم: {ex.Message}", ex);
            }
        }

        private void SaveConnectionSettings()
        {
            try
            {
                // حفظ إعدادات الاتصال في ملف التكوين
                var config = System.Configuration.ConfigurationManager.OpenExeConfiguration(
                    System.Configuration.ConfigurationUserLevel.None);

                config.ConnectionStrings.ConnectionStrings.Clear();
                config.ConnectionStrings.ConnectionStrings.Add(
                    new System.Configuration.ConnectionStringSettings(
                        "DefaultConnection", _connectionString));

                config.Save(System.Configuration.ConfigurationSaveMode.Modified);
                System.Configuration.ConfigurationManager.RefreshSection("connectionStrings");
            }
            catch (Exception ex)
            {
                // في حالة فشل حفظ الإعدادات، نعرض تحذير فقط
                MessageBox.Show($"تحذير: لم يتم حفظ إعدادات الاتصال: {ex.Message}",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void SetControlsEnabled(bool enabled)
        {
            foreach (Control control in this.Controls)
            {
                if (control is Button || control is TextBox || control is ComboBox)
                {
                    control.Enabled = enabled;
                }
            }
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد إلغاء عملية الإعداد والخروج من التطبيق؟",
                "تأكيد الإلغاء", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
    }
}
