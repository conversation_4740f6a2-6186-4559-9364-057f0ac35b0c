using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Joud.Models;

namespace Joud.Services
{
    /// <summary>
    /// خدمة إدارة الأدوار
    /// </summary>
    public class RoleService
    {
        private static List<Role> _roles = new List<Role>();
        private static int _nextId = 1;

        static RoleService()
        {
            // إضافة الأدوار الافتراضية
            _roles.AddRange(new[]
            {
                new Role { RoleId = _nextId++, RoleName = "مدير النظام", Description = "صلاحيات كاملة", IsActive = true, CreatedDate = DateTime.Now },
                new Role { RoleId = _nextId++, RoleName = "مدير", Description = "صلاحيات إدارية", IsActive = true, CreatedDate = DateTime.Now },
                new Role { RoleId = _nextId++, RoleName = "محاسب", Description = "صلاحيات محاسبية", IsActive = true, CreatedDate = DateTime.Now },
                new Role { RoleId = _nextId++, RoleName = "مستخدم", Description = "صلاحيات أساسية", IsActive = true, CreatedDate = DateTime.Now }
            });
        }

        /// <summary>
        /// الحصول على جميع الأدوار
        /// </summary>
        public IEnumerable<Role> GetAllRoles()
        {
            return _roles.AsEnumerable();
        }

        /// <summary>
        /// الحصول على جميع الأدوار (async)
        /// </summary>
        public async Task<IEnumerable<Role>> GetAllRolesAsync()
        {
            return await Task.FromResult(_roles.AsEnumerable());
        }

        /// <summary>
        /// الحصول على دور بالمعرف
        /// </summary>
        public Role? GetRoleById(int id)
        {
            return _roles.FirstOrDefault(r => r.RoleId == id);
        }

        /// <summary>
        /// إضافة دور جديد
        /// </summary>
        public Role AddRole(Role role)
        {
            role.RoleId = _nextId++;
            role.CreatedDate = DateTime.Now;
            _roles.Add(role);
            return role;
        }

        /// <summary>
        /// تحديث دور
        /// </summary>
        public void UpdateRole(Role role)
        {
            var existingRole = GetRoleById(role.RoleId);
            if (existingRole != null)
            {
                var index = _roles.IndexOf(existingRole);
                role.ModifiedDate = DateTime.Now;
                _roles[index] = role;
            }
        }

        /// <summary>
        /// حذف دور
        /// </summary>
        public void DeleteRole(int id)
        {
            var role = GetRoleById(id);
            if (role != null)
            {
                _roles.Remove(role);
            }
        }
    }
}
