-- ===================================================================
-- نظام جود للمحاسبة المالية - البيانات الأولية
-- Joud Accounting System - Initial Data
-- ===================================================================

USE [JoudAccountingDB]
GO

-- ===================================================================
-- إدراج أدوار المستخدمين الافتراضية
-- ===================================================================
IF NOT EXISTS (SELECT * FROM UserRoles WHERE RoleName = N'مدير النظام')
BEGIN
    INSERT INTO UserRoles (RoleName, RoleNameEn, Description)
    VALUES 
    (N'مدير النظام', 'System Administrator', N'صلاحيات كاملة على النظام'),
    (N'مدير مالي', 'Financial Manager', N'إدارة العمليات المالية والمحاسبية'),
    (N'محاسب', 'Accountant', N'إدخال وإدارة البيانات المحاسبية'),
    (N'موظف مبيعات', 'Sales Employee', N'إدارة المبيعات والعملاء'),
    (N'موظف مشتريات', 'Purchase Employee', N'إدارة المشتريات والموردين'),
    (N'موظف مخازن', 'Warehouse Employee', N'إدارة المخازن والمخزون')
END
GO

-- ===================================================================
-- إدراج أنواع الحسابات الافتراضية
-- ===================================================================
IF NOT EXISTS (SELECT * FROM AccountTypes WHERE TypeName = N'الأصول')
BEGIN
    INSERT INTO AccountTypes (TypeCode, TypeName, TypeNameEn, Description)
    VALUES 
    ('ASSETS', N'الأصول', 'Assets', N'حسابات الأصول'),
    ('LIABILITIES', N'الخصوم', 'Liabilities', N'حسابات الخصوم'),
    ('EQUITY', N'حقوق الملكية', 'Equity', N'حسابات حقوق الملكية'),
    ('REVENUE', N'الإيرادات', 'Revenue', N'حسابات الإيرادات'),
    ('EXPENSES', N'المصاريف', 'Expenses', N'حسابات المصاريف'),
    ('COST', N'تكلفة البضاعة المباعة', 'Cost of Goods Sold', N'تكلفة البضاعة المباعة')
END
GO

-- ===================================================================
-- إنشاء إجراءات مخزنة للعمليات الأساسية
-- ===================================================================

-- إجراء لإنشاء كود تلقائي للعملاء
CREATE OR ALTER PROCEDURE sp_GenerateCustomerCode
    @CompanyId INT,
    @CustomerCode NVARCHAR(20) OUTPUT
AS
BEGIN
    DECLARE @MaxCode INT
    SELECT @MaxCode = ISNULL(MAX(CAST(SUBSTRING(CustomerCode, 5, LEN(CustomerCode)) AS INT)), 0)
    FROM Customers 
    WHERE CompanyId = @CompanyId AND CustomerCode LIKE 'CUST%'
    
    SET @CustomerCode = 'CUST' + RIGHT('000000' + CAST(@MaxCode + 1 AS NVARCHAR), 6)
END
GO

-- إجراء لإنشاء كود تلقائي للموردين
CREATE OR ALTER PROCEDURE sp_GenerateSupplierCode
    @CompanyId INT,
    @SupplierCode NVARCHAR(20) OUTPUT
AS
BEGIN
    DECLARE @MaxCode INT
    SELECT @MaxCode = ISNULL(MAX(CAST(SUBSTRING(SupplierCode, 5, LEN(SupplierCode)) AS INT)), 0)
    FROM Suppliers 
    WHERE CompanyId = @CompanyId AND SupplierCode LIKE 'SUPP%'
    
    SET @SupplierCode = 'SUPP' + RIGHT('000000' + CAST(@MaxCode + 1 AS NVARCHAR), 6)
END
GO

-- إجراء لإنشاء كود تلقائي للأصناف
CREATE OR ALTER PROCEDURE sp_GenerateProductCode
    @CompanyId INT,
    @ProductCode NVARCHAR(20) OUTPUT
AS
BEGIN
    DECLARE @MaxCode INT
    SELECT @MaxCode = ISNULL(MAX(CAST(SUBSTRING(ProductCode, 5, LEN(ProductCode)) AS INT)), 0)
    FROM Products 
    WHERE CompanyId = @CompanyId AND ProductCode LIKE 'PROD%'
    
    SET @ProductCode = 'PROD' + RIGHT('000000' + CAST(@MaxCode + 1 AS NVARCHAR), 6)
END
GO

-- إجراء لإنشاء رقم فاتورة مبيعات تلقائي
CREATE OR ALTER PROCEDURE sp_GenerateSalesInvoiceNumber
    @CompanyId INT,
    @InvoiceNumber NVARCHAR(50) OUTPUT
AS
BEGIN
    DECLARE @MaxNumber INT
    DECLARE @Year NVARCHAR(4) = CAST(YEAR(GETDATE()) AS NVARCHAR(4))
    
    SELECT @MaxNumber = ISNULL(MAX(CAST(SUBSTRING(InvoiceNumber, 6, LEN(InvoiceNumber)) AS INT)), 0)
    FROM SalesInvoices 
    WHERE CompanyId = @CompanyId AND InvoiceNumber LIKE 'SALES%'
    
    SET @InvoiceNumber = 'SALES' + @Year + RIGHT('000000' + CAST(@MaxNumber + 1 AS NVARCHAR), 6)
END
GO

-- إجراء لإنشاء رقم فاتورة مشتريات تلقائي
CREATE OR ALTER PROCEDURE sp_GeneratePurchaseInvoiceNumber
    @CompanyId INT,
    @InvoiceNumber NVARCHAR(50) OUTPUT
AS
BEGIN
    DECLARE @MaxNumber INT
    DECLARE @Year NVARCHAR(4) = CAST(YEAR(GETDATE()) AS NVARCHAR(4))
    
    SELECT @MaxNumber = ISNULL(MAX(CAST(SUBSTRING(InvoiceNumber, 6, LEN(InvoiceNumber)) AS INT)), 0)
    FROM PurchaseInvoices 
    WHERE CompanyId = @CompanyId AND InvoiceNumber LIKE 'PURCH%'
    
    SET @InvoiceNumber = 'PURCH' + @Year + RIGHT('000000' + CAST(@MaxNumber + 1 AS NVARCHAR), 6)
END
GO

-- ===================================================================
-- إنشاء فهارس لتحسين الأداء
-- ===================================================================

-- فهارس جدول العملاء
CREATE NONCLUSTERED INDEX IX_Customers_CompanyId ON Customers(CompanyId)
CREATE NONCLUSTERED INDEX IX_Customers_CustomerCode ON Customers(CustomerCode)
CREATE NONCLUSTERED INDEX IX_Customers_CustomerName ON Customers(CustomerName)

-- فهارس جدول الموردين
CREATE NONCLUSTERED INDEX IX_Suppliers_CompanyId ON Suppliers(CompanyId)
CREATE NONCLUSTERED INDEX IX_Suppliers_SupplierCode ON Suppliers(SupplierCode)
CREATE NONCLUSTERED INDEX IX_Suppliers_SupplierName ON Suppliers(SupplierName)

-- فهارس جدول الأصناف
CREATE NONCLUSTERED INDEX IX_Products_CompanyId ON Products(CompanyId)
CREATE NONCLUSTERED INDEX IX_Products_ProductCode ON Products(ProductCode)
CREATE NONCLUSTERED INDEX IX_Products_Barcode ON Products(Barcode)
CREATE NONCLUSTERED INDEX IX_Products_ProductName ON Products(ProductName)
CREATE NONCLUSTERED INDEX IX_Products_MainCategoryId ON Products(MainCategoryId)

-- فهارس جدول فواتير المبيعات
CREATE NONCLUSTERED INDEX IX_SalesInvoices_CompanyId ON SalesInvoices(CompanyId)
CREATE NONCLUSTERED INDEX IX_SalesInvoices_InvoiceDate ON SalesInvoices(InvoiceDate)
CREATE NONCLUSTERED INDEX IX_SalesInvoices_CustomerId ON SalesInvoices(CustomerId)
CREATE NONCLUSTERED INDEX IX_SalesInvoices_InvoiceNumber ON SalesInvoices(InvoiceNumber)

-- فهارس جدول فواتير المشتريات
CREATE NONCLUSTERED INDEX IX_PurchaseInvoices_CompanyId ON PurchaseInvoices(CompanyId)
CREATE NONCLUSTERED INDEX IX_PurchaseInvoices_InvoiceDate ON PurchaseInvoices(InvoiceDate)
CREATE NONCLUSTERED INDEX IX_PurchaseInvoices_SupplierId ON PurchaseInvoices(SupplierId)
CREATE NONCLUSTERED INDEX IX_PurchaseInvoices_InvoiceNumber ON PurchaseInvoices(InvoiceNumber)

-- فهارس جدول المعاملات المالية
CREATE NONCLUSTERED INDEX IX_FinancialTransactions_CompanyId ON FinancialTransactions(CompanyId)
CREATE NONCLUSTERED INDEX IX_FinancialTransactions_TransactionDate ON FinancialTransactions(TransactionDate)
CREATE NONCLUSTERED INDEX IX_FinancialTransactions_AccountId ON FinancialTransactions(AccountId)

-- ===================================================================
-- إنشاء مشاهد للتقارير
-- ===================================================================

-- مشهد تقرير المبيعات
CREATE OR ALTER VIEW vw_SalesReport
AS
SELECT 
    si.SalesInvoiceId,
    si.InvoiceNumber,
    si.InvoiceDate,
    c.CustomerName,
    c.CustomerCode,
    si.SubTotal,
    si.DiscountAmount,
    si.TaxAmount,
    si.TotalAmount,
    si.PaidAmount,
    si.RemainingAmount,
    si.PaymentStatus,
    u.FullName AS CreatedByUser,
    co.CompanyName
FROM SalesInvoices si
INNER JOIN Customers c ON si.CustomerId = c.CustomerId
INNER JOIN Users u ON si.CreatedBy = u.UserId
INNER JOIN Companies co ON si.CompanyId = co.CompanyId
WHERE si.IsActive = 1
GO

-- مشهد تقرير المشتريات
CREATE OR ALTER VIEW vw_PurchaseReport
AS
SELECT 
    pi.PurchaseInvoiceId,
    pi.InvoiceNumber,
    pi.InvoiceDate,
    s.SupplierName,
    s.SupplierCode,
    pi.SubTotal,
    pi.DiscountAmount,
    pi.TaxAmount,
    pi.TotalAmount,
    pi.PaidAmount,
    pi.RemainingAmount,
    pi.PaymentStatus,
    u.FullName AS CreatedByUser,
    co.CompanyName
FROM PurchaseInvoices pi
INNER JOIN Suppliers s ON pi.SupplierId = s.SupplierId
INNER JOIN Users u ON pi.CreatedBy = u.UserId
INNER JOIN Companies co ON pi.CompanyId = co.CompanyId
WHERE pi.IsActive = 1
GO

-- مشهد تقرير المخزون
CREATE OR ALTER VIEW vw_InventoryReport
AS
SELECT 
    p.ProductId,
    p.ProductCode,
    p.Barcode,
    p.ProductName,
    mc.CategoryName AS MainCategory,
    sc.CategoryName AS SubCategory,
    u.UnitName,
    w.WarehouseName,
    p.CurrentStock,
    p.MinimumStock,
    p.PurchasePrice,
    p.RetailPrice,
    (p.CurrentStock * p.PurchasePrice) AS StockValue,
    CASE 
        WHEN p.CurrentStock <= p.MinimumStock THEN N'نفاد المخزون'
        ELSE N'متوفر'
    END AS StockStatus,
    co.CompanyName
FROM Products p
INNER JOIN MainCategories mc ON p.MainCategoryId = mc.MainCategoryId
LEFT JOIN SubCategories sc ON p.SubCategoryId = sc.SubCategoryId
INNER JOIN Units u ON p.UnitId = u.UnitId
INNER JOIN Warehouses w ON p.WarehouseId = w.WarehouseId
INNER JOIN Companies co ON p.CompanyId = co.CompanyId
WHERE p.IsActive = 1
GO
