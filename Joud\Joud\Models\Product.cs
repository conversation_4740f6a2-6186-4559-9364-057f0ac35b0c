using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات الأصناف
    /// Product Data Model
    /// </summary>
    [Table("Products")]
    public class Product
    {
        [Key]
        public int ProductId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "كود الصنف")]
        public string ProductCode { get; set; } = string.Empty;

        [StringLength(50)]
        [Display(Name = "الباركود")]
        public string? Barcode { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الصنف")]
        public string ProductName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "اسم الصنف بالإنجليزية")]
        public string? ProductNameEn { get; set; }

        [StringLength(1000)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "الفئة الرئيسية")]
        public int MainCategoryId { get; set; }

        [Display(Name = "الفئة الفرعية")]
        public int? SubCategoryId { get; set; }

        [Required]
        [Display(Name = "الوحدة")]
        public int UnitId { get; set; }

        [Required]
        [Display(Name = "المخزن")]
        public int WarehouseId { get; set; }

        [Display(Name = "صورة المنتج")]
        public byte[]? ProductImage { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "سعر الشراء")]
        public decimal PurchasePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "سعر الجملة")]
        public decimal WholesalePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "سعر نصف الجملة")]
        public decimal HalfWholesalePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "سعر البيع")]
        public decimal RetailPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "سعر البيع")]
        public decimal SalePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "أقل سعر للبيع")]
        public decimal MinimumPrice { get; set; } = 0;

        [Display(Name = "فعال للشراء")]
        public bool IsActiveForPurchase { get; set; } = true;

        [Display(Name = "فعال للبيع")]
        public bool IsActiveForSale { get; set; } = true;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الرصيد الافتتاحي")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المخزون الحالي")]
        public decimal CurrentStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "الحد الأدنى للمخزون")]
        public decimal MinimumStock { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة الخصم")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "نسبة الضريبة")]
        public decimal TaxPercentage { get; set; } = 0;

        [Required]
        [Display(Name = "الشركة")]
        public int CompanyId { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "أنشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "تاريخ التعديل")]
        public DateTime? ModifiedDate { get; set; }

        [Display(Name = "عدل بواسطة")]
        public int? ModifiedBy { get; set; }

        // Navigation Properties
        [ForeignKey("MainCategoryId")]
        public virtual MainCategory MainCategory { get; set; } = null!;

        [ForeignKey("SubCategoryId")]
        public virtual SubCategory? SubCategory { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; } = null!;

        [ForeignKey("WarehouseId")]
        public virtual Warehouse Warehouse { get; set; } = null!;

        [ForeignKey("CompanyId")]
        public virtual Company Company { get; set; } = null!;

        [ForeignKey("CreatedBy")]
        public virtual User CreatedByUser { get; set; } = null!;

        [ForeignKey("ModifiedBy")]
        public virtual User? ModifiedByUser { get; set; }

        // Collections
        public virtual ICollection<SalesInvoiceDetail> SalesInvoiceDetails { get; set; } = new List<SalesInvoiceDetail>();
        public virtual ICollection<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; } = new List<PurchaseInvoiceDetail>();
    }
}
