using System;
using System.ComponentModel;
using System.Data;
using System.Drawing.Printing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using Joud.Models;
using Joud.Services;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة عارض التقارير - Report Viewer Form
    /// تعرض التقارير المختلفة مع إمكانيات الطباعة والتصدير
    /// </summary>
    public partial class ReportViewerForm : Form
    {
        #region Private Fields

        private ReportData _reportData;
        private string _reportHtml;
        private string _reportTitle;
        private ReportType _reportType;
        private int _currentZoom = 100;
        private readonly ReportService _reportService;
        private readonly ExportService _exportService;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ شاشة عارض التقارير
        /// </summary>
        public ReportViewerForm()
        {
            InitializeComponent();
            _reportService = new ReportService();
            _exportService = new ExportService();
            InitializeForm();
        }

        /// <summary>
        /// منشئ شاشة عارض التقارير مع عنوان فقط
        /// </summary>
        /// <param name="reportTitle">عنوان التقرير</param>
        public ReportViewerForm(string reportTitle) : this()
        {
            _reportTitle = reportTitle;
            _reportType = ReportType.Custom;
        }

        /// <summary>
        /// منشئ شاشة عارض التقارير مع بيانات التقرير
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <param name="reportTitle">عنوان التقرير</param>
        /// <param name="reportType">نوع التقرير</param>
        public ReportViewerForm(ReportData reportData, string reportTitle, ReportType reportType) : this()
        {
            _reportData = reportData;
            _reportTitle = reportTitle;
            _reportType = reportType;
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل الشاشة
        /// </summary>
        private void ReportViewerForm_Load(object sender, EventArgs e)
        {
            try
            {
                if (_reportData != null)
                {
                    LoadReport();
                }

                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث اكتمال تحميل المستند في المتصفح
        /// </summary>
        private void reportViewer_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            try
            {
                progressBar.Visible = false;
                lblStatus.Text = "تم تحميل التقرير بنجاح";

                // تحديث معلومات التقرير
                if (_reportData != null)
                {
                    lblReportInfo.Text = $"التقرير: {_reportTitle} | عدد السجلات: {_reportData.Rows.Count}";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "خطأ في تحميل التقرير";
                System.Diagnostics.Debug.WriteLine($"خطأ في اكتمال تحميل المستند: {ex.Message}");
            }
        }

        #endregion

        #region Toolbar Events

        /// <summary>
        /// حدث النقر على زر الطباعة
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                PrintReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر معاينة الطباعة
        /// </summary>
        private void btnPrintPreview_Click(object sender, EventArgs e)
        {
            try
            {
                ShowPrintPreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تصدير PDF
        /// </summary>
        private void btnExportPDF_Click(object sender, EventArgs e)
        {
            try
            {
                ExportToPDF();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تصدير Excel
        /// </summary>
        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                ExportToExcel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تصدير Word
        /// </summary>
        private void btnExportWord_Click(object sender, EventArgs e)
        {
            try
            {
                ExportToWord();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Word: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر التكبير
        /// </summary>
        private void btnZoomIn_Click(object sender, EventArgs e)
        {
            try
            {
                ZoomIn();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تكبير التقرير: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر التصغير
        /// </summary>
        private void btnZoomOut_Click(object sender, EventArgs e)
        {
            try
            {
                ZoomOut();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصغير التقرير: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر ملائمة الحجم
        /// </summary>
        private void btnZoomFit_Click(object sender, EventArgs e)
        {
            try
            {
                ZoomFit();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ملائمة حجم التقرير: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر التحديث
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                RefreshReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث التقرير: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر الإغلاق
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            try
            {
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إغلاق الشاشة: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// تعيين محتوى التقرير
        /// </summary>
        /// <param name="content">محتوى التقرير</param>
        public void SetContent(string content)
        {
            try
            {
                _reportHtml = content;
                reportViewer.DocumentText = content;
                lblStatus.Text = "تم تحميل التقرير بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل محتوى التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في تحميل التقرير";
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تهيئة الشاشة
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // إعداد المتصفح
                reportViewer.ScriptErrorsSuppressed = true;
                reportViewer.WebBrowserShortcutsEnabled = false;
                reportViewer.IsWebBrowserContextMenuEnabled = false;

                // إعداد شريط التقدم
                progressBar.Visible = false;
                progressBar.Style = ProgressBarStyle.Marquee;

                // تعيين القيم الافتراضية
                _currentZoom = 100;
                lblStatus.Text = "جاهز";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة الشاشة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل التقرير
        /// </summary>
        private void LoadReport()
        {
            try
            {
                if (_reportData == null)
                {
                    lblStatus.Text = "لا توجد بيانات للتقرير";
                    return;
                }

                progressBar.Visible = true;
                lblStatus.Text = "جاري تحميل التقرير...";

                // تحديث عنوان التقرير
                lblReportTitle.Text = _reportTitle ?? "تقرير";
                this.Text = _reportTitle ?? "عارض التقارير";

                // إنشاء HTML للتقرير
                _reportHtml = GenerateReportHtml();

                // عرض التقرير في المتصفح
                reportViewer.DocumentText = _reportHtml;
            }
            catch (Exception ex)
            {
                progressBar.Visible = false;
                lblStatus.Text = "خطأ في تحميل التقرير";
                throw new Exception($"فشل في تحميل التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء HTML للتقرير
        /// </summary>
        /// <returns>نص HTML للتقرير</returns>
        private string GenerateReportHtml()
        {
            try
            {
                var html = new StringBuilder();

                // بداية HTML
                html.AppendLine("<!DOCTYPE html>");
                html.AppendLine("<html dir='rtl' lang='ar'>");
                html.AppendLine("<head>");
                html.AppendLine("<meta charset='UTF-8'>");
                html.AppendLine("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
                html.AppendLine($"<title>{_reportTitle}</title>");
                html.AppendLine(GetReportStyles());
                html.AppendLine("</head>");
                html.AppendLine("<body>");

                // رأس التقرير
                html.AppendLine(GenerateReportHeader());

                // محتوى التقرير
                html.AppendLine(GenerateReportContent());

                // ذيل التقرير
                html.AppendLine(GenerateReportFooter());

                html.AppendLine("</body>");
                html.AppendLine("</html>");

                return html.ToString();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء HTML للتقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على أنماط CSS للتقرير
        /// </summary>
        /// <returns>نص CSS</returns>
        private string GetReportStyles()
        {
            return @"
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                        margin: 20px;
                        background-color: #f8f9fa;
                        color: #333;
                        direction: rtl;
                    }
                    .report-container {
                        background-color: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        max-width: 1200px;
                        margin: 0 auto;
                    }
                    .report-header {
                        text-align: center;
                        border-bottom: 3px solid #3498db;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }
                    .report-title {
                        font-size: 24px;
                        font-weight: bold;
                        color: #2c3e50;
                        margin-bottom: 10px;
                    }
                    .report-info {
                        font-size: 14px;
                        color: #7f8c8d;
                    }
                    .report-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 14px;
                    }
                    .report-table th {
                        background-color: #3498db;
                        color: white;
                        padding: 12px 8px;
                        text-align: center;
                        border: 1px solid #2980b9;
                        font-weight: bold;
                    }
                    .report-table td {
                        padding: 10px 8px;
                        border: 1px solid #bdc3c7;
                        text-align: center;
                    }
                    .report-table tr:nth-child(even) {
                        background-color: #f8f9fa;
                    }
                    .report-table tr:hover {
                        background-color: #e8f4f8;
                    }
                    .report-footer {
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 2px solid #ecf0f1;
                        text-align: center;
                        font-size: 12px;
                        color: #7f8c8d;
                    }
                    .summary-section {
                        background-color: #ecf0f1;
                        padding: 15px;
                        border-radius: 5px;
                        margin: 20px 0;
                    }
                    .summary-item {
                        display: inline-block;
                        margin: 5px 15px;
                        font-weight: bold;
                    }
                    @media print {
                        body { margin: 0; background-color: white; }
                        .report-container { box-shadow: none; }
                    }
                </style>";
        }

        /// <summary>
        /// إنشاء رأس التقرير
        /// </summary>
        /// <returns>HTML رأس التقرير</returns>
        private string GenerateReportHeader()
        {
            var header = new StringBuilder();

            header.AppendLine("<div class='report-container'>");
            header.AppendLine("<div class='report-header'>");
            header.AppendLine($"<div class='report-title'>{_reportTitle}</div>");
            header.AppendLine($"<div class='report-info'>تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm}</div>");
            header.AppendLine($"<div class='report-info'>نظام جود للمحاسبة المالية</div>");
            header.AppendLine("</div>");

            return header.ToString();
        }

        /// <summary>
        /// إنشاء محتوى التقرير
        /// </summary>
        /// <returns>HTML محتوى التقرير</returns>
        private string GenerateReportContent()
        {
            try
            {
                if (_reportData == null || _reportData.Rows.Count == 0)
                {
                    return "<div style='text-align: center; padding: 50px; color: #7f8c8d;'>لا توجد بيانات لعرضها</div>";
                }

                var content = new StringBuilder();

                // إضافة ملخص إذا كان متوفراً
                if (_reportData.Summary != null && _reportData.Summary.Count > 0)
                {
                    content.AppendLine("<div class='summary-section'>");
                    content.AppendLine("<h3>ملخص التقرير:</h3>");

                    foreach (var summaryItem in _reportData.Summary)
                    {
                        content.AppendLine($"<span class='summary-item'>{summaryItem.Key}: {summaryItem.Value}</span>");
                    }

                    content.AppendLine("</div>");
                }

                // إنشاء الجدول
                content.AppendLine("<table class='report-table'>");

                // رأس الجدول
                if (_reportData.Columns.Count > 0)
                {
                    content.AppendLine("<thead><tr>");
                    foreach (var column in _reportData.Columns)
                    {
                        content.AppendLine($"<th>{column}</th>");
                    }
                    content.AppendLine("</tr></thead>");
                }

                // بيانات الجدول
                content.AppendLine("<tbody>");
                foreach (var row in _reportData.Rows)
                {
                    content.AppendLine("<tr>");
                    foreach (var cell in row)
                    {
                        content.AppendLine($"<td>{cell ?? ""}</td>");
                    }
                    content.AppendLine("</tr>");
                }
                content.AppendLine("</tbody>");

                content.AppendLine("</table>");

                return content.ToString();
            }
            catch (Exception ex)
            {
                return $"<div style='color: red; text-align: center; padding: 20px;'>خطأ في إنشاء محتوى التقرير: {ex.Message}</div>";
            }
        }

        /// <summary>
        /// إنشاء ذيل التقرير
        /// </summary>
        /// <returns>HTML ذيل التقرير</returns>
        private string GenerateReportFooter()
        {
            var footer = new StringBuilder();

            footer.AppendLine("<div class='report-footer'>");
            footer.AppendLine($"<div>تم إنشاء هذا التقرير بواسطة نظام جود للمحاسبة المالية</div>");
            footer.AppendLine($"<div>تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm:ss}</div>");

            if (_reportData != null)
            {
                footer.AppendLine($"<div>عدد السجلات: {_reportData.Rows.Count}</div>");
            }

            footer.AppendLine("</div>");
            footer.AppendLine("</div>"); // إغلاق report-container

            return footer.ToString();
        }

        #endregion

        #region Report Operations

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void PrintReport()
        {
            try
            {
                if (string.IsNullOrEmpty(_reportHtml))
                {
                    MessageBox.Show("لا يوجد تقرير للطباعة.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                reportViewer.Print();
                lblStatus.Text = "تم إرسال التقرير للطباعة";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في طباعة التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض معاينة الطباعة
        /// </summary>
        private void ShowPrintPreview()
        {
            try
            {
                if (string.IsNullOrEmpty(_reportHtml))
                {
                    MessageBox.Show("لا يوجد تقرير لمعاينة الطباعة.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                reportViewer.ShowPrintPreviewDialog();
                lblStatus.Text = "تم عرض معاينة الطباعة";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في عرض معاينة الطباعة: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير التقرير إلى PDF
        /// </summary>
        private void ExportToPDF()
        {
            try
            {
                if (_reportData == null)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "ملفات PDF (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"{_reportTitle}_{DateTime.Now:yyyyMMdd}.pdf"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    progressBar.Visible = true;
                    lblStatus.Text = "جاري تصدير PDF...";

                    _exportService.ExportToPDF(_reportData, _reportTitle, saveDialog.FileName);

                    progressBar.Visible = false;
                    lblStatus.Text = "تم تصدير PDF بنجاح";

                    MessageBox.Show("تم تصدير التقرير إلى PDF بنجاح.", "نجح التصدير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                progressBar.Visible = false;
                lblStatus.Text = "فشل في تصدير PDF";
                throw new Exception($"فشل في تصدير PDF: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير التقرير إلى Excel
        /// </summary>
        private void ExportToExcel()
        {
            try
            {
                if (_reportData == null)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "ملفات Excel (*.xlsx)|*.xlsx|ملفات Excel القديمة (*.xls)|*.xls",
                    DefaultExt = "xlsx",
                    FileName = $"{_reportTitle}_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    progressBar.Visible = true;
                    lblStatus.Text = "جاري تصدير Excel...";

                    _exportService.ExportToExcel(_reportData, _reportTitle, saveDialog.FileName);

                    progressBar.Visible = false;
                    lblStatus.Text = "تم تصدير Excel بنجاح";

                    MessageBox.Show("تم تصدير التقرير إلى Excel بنجاح.", "نجح التصدير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                progressBar.Visible = false;
                lblStatus.Text = "فشل في تصدير Excel";
                throw new Exception($"فشل في تصدير Excel: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير التقرير إلى Word
        /// </summary>
        private void ExportToWord()
        {
            try
            {
                if (_reportData == null)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "ملفات Word (*.docx)|*.docx|ملفات Word القديمة (*.doc)|*.doc",
                    DefaultExt = "docx",
                    FileName = $"{_reportTitle}_{DateTime.Now:yyyyMMdd}.docx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    progressBar.Visible = true;
                    lblStatus.Text = "جاري تصدير Word...";

                    _exportService.ExportToWord(_reportData, _reportTitle, saveDialog.FileName);

                    progressBar.Visible = false;
                    lblStatus.Text = "تم تصدير Word بنجاح";

                    MessageBox.Show("تم تصدير التقرير إلى Word بنجاح.", "نجح التصدير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                progressBar.Visible = false;
                lblStatus.Text = "فشل في تصدير Word";
                throw new Exception($"فشل في تصدير Word: {ex.Message}");
            }
        }

        #endregion

        #region Zoom Operations

        /// <summary>
        /// تكبير التقرير
        /// </summary>
        private void ZoomIn()
        {
            try
            {
                if (_currentZoom < 200)
                {
                    _currentZoom += 25;
                    ApplyZoom();
                    lblStatus.Text = $"التكبير: {_currentZoom}%";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تكبير التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// تصغير التقرير
        /// </summary>
        private void ZoomOut()
        {
            try
            {
                if (_currentZoom > 50)
                {
                    _currentZoom -= 25;
                    ApplyZoom();
                    lblStatus.Text = $"التكبير: {_currentZoom}%";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تصغير التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// ملائمة حجم التقرير
        /// </summary>
        private void ZoomFit()
        {
            try
            {
                _currentZoom = 100;
                ApplyZoom();
                lblStatus.Text = "تم ملائمة الحجم";
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في ملائمة حجم التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق التكبير/التصغير
        /// </summary>
        private void ApplyZoom()
        {
            try
            {
                if (reportViewer.Document != null && reportViewer.Document.Body != null)
                {
                    reportViewer.Document.Body.Style = $"zoom: {_currentZoom}%";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التكبير: {ex.Message}");
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// تحديث التقرير
        /// </summary>
        private void RefreshReport()
        {
            try
            {
                if (_reportData != null)
                {
                    LoadReport();
                    lblStatus.Text = "تم تحديث التقرير";
                }
                else
                {
                    MessageBox.Show("لا يوجد تقرير للتحديث.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحديث التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث واجهة المستخدم
        /// </summary>
        private void UpdateUI()
        {
            try
            {
                bool hasData = _reportData != null && _reportData.Rows.Count > 0;

                // تحديث حالة الأزرار
                btnPrint.Enabled = hasData;
                btnPrintPreview.Enabled = hasData;
                btnExportPDF.Enabled = hasData;
                btnExportExcel.Enabled = hasData;
                btnExportWord.Enabled = hasData;
                btnRefresh.Enabled = _reportData != null;

                // تحديث معلومات التقرير
                if (hasData)
                {
                    lblReportInfo.Text = $"التقرير: {_reportTitle} | عدد السجلات: {_reportData.Rows.Count}";
                }
                else
                {
                    lblReportInfo.Text = "لا توجد بيانات";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة المستخدم: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// تحديد بيانات التقرير
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <param name="reportTitle">عنوان التقرير</param>
        /// <param name="reportType">نوع التقرير</param>
        public void SetReportData(ReportData reportData, string reportTitle, ReportType reportType)
        {
            try
            {
                _reportData = reportData;
                _reportTitle = reportTitle;
                _reportType = reportType;

                if (this.IsHandleCreated)
                {
                    LoadReport();
                    UpdateUI();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد بيانات التقرير: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض تقرير جديد
        /// </summary>
        /// <param name="reportData">بيانات التقرير</param>
        /// <param name="reportTitle">عنوان التقرير</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="parent">النافذة الأب</param>
        /// <returns>شاشة عارض التقارير</returns>
        public static ReportViewerForm ShowReport(ReportData reportData, string reportTitle,
            ReportType reportType, IWin32Window parent = null)
        {
            try
            {
                var reportForm = new ReportViewerForm(reportData, reportTitle, reportType);

                if (parent != null)
                {
                    reportForm.Show(parent);
                }
                else
                {
                    reportForm.Show();
                }

                return reportForm;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التقرير: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        #endregion
    }

    #region Enums

    /// <summary>
    /// أنواع التقارير
    /// </summary>
    public enum ReportType
    {
        Sales,          // تقارير المبيعات
        Purchases,      // تقارير المشتريات
        Inventory,      // تقارير المخزون
        Financial,      // التقارير المالية
        Customers,      // تقارير العملاء
        Suppliers,      // تقارير الموردين
        Products,       // تقارير الأصناف
        Custom          // تقارير مخصصة
    }

    #endregion
}