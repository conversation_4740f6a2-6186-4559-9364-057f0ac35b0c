using Microsoft.Data.SqlClient;
using System.Text;

namespace Joud.Utilities
{
    /// <summary>
    /// فئة مساعدة لإدارة قاعدة البيانات
    /// Database Helper Class
    /// </summary>
    public static class DatabaseHelper
    {
        /// <summary>
        /// إنشاء قاعدة البيانات (مع حذف القديمة إن وجدت)
        /// Create Database (drop existing if exists)
        /// </summary>
        /// <param name="connectionString">نص الاتصال</param>
        /// <param name="databaseName">اسم قاعدة البيانات</param>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        public static async Task<bool> CreateDatabaseAsync(string connectionString, string databaseName)
        {
            try
            {
                // إنشاء نص اتصال للخادم فقط (بدون قاعدة البيانات)
                var builder = new SqlConnectionStringBuilder(connectionString);
                string serverConnectionString = $"Server={builder.DataSource};Integrated Security=true;TrustServerCertificate=true;";

                using var connection = new SqlConnection(serverConnectionString);
                await connection.OpenAsync();

                // التحقق من وجود قاعدة البيانات
                string checkDbQuery = $"SELECT COUNT(*) FROM sys.databases WHERE name = '{databaseName}'";
                using var checkCommand = new SqlCommand(checkDbQuery, connection);
                int dbExists = (int)await checkCommand.ExecuteScalarAsync();

                if (dbExists > 0)
                {
                    try
                    {
                        // حذف قاعدة البيانات القديمة لضمان إنشاء جداول محدثة
                        string dropDbQuery = $@"
                            ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
                            DROP DATABASE [{databaseName}];";

                        using var dropCommand = new SqlCommand(dropDbQuery, connection);
                        await dropCommand.ExecuteNonQueryAsync();
                    }
                    catch (Exception ex)
                    {
                        // إذا فشل حذف قاعدة البيانات، نحاول إنشاء اسم جديد
                        databaseName = $"{databaseName}_{DateTime.Now:yyyyMMddHHmmss}";
                    }
                }

                // إنشاء قاعدة البيانات
                string createDbQuery = $@"
                    CREATE DATABASE [{databaseName}]
                    COLLATE Arabic_CI_AS";

                using var createCommand = new SqlCommand(createDbQuery, connection);
                await createCommand.ExecuteNonQueryAsync();

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء قاعدة البيانات إذا لم تكن موجودة
        /// Create Database if not exists
        /// </summary>
        /// <param name="connectionString">نص الاتصال</param>
        /// <param name="databaseName">اسم قاعدة البيانات</param>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        public static async Task<bool> CreateDatabaseIfNotExistsAsync(string connectionString, string databaseName)
        {
            try
            {
                // إنشاء نص اتصال للخادم فقط (بدون قاعدة البيانات)
                var builder = new SqlConnectionStringBuilder(connectionString);
                string serverConnectionString = $"Server={builder.DataSource};Integrated Security=true;TrustServerCertificate=true;";

                using var connection = new SqlConnection(serverConnectionString);
                await connection.OpenAsync();

                // التحقق من وجود قاعدة البيانات
                string checkDbQuery = $"SELECT COUNT(*) FROM sys.databases WHERE name = '{databaseName}'";
                using var checkCommand = new SqlCommand(checkDbQuery, connection);
                int dbExists = (int)await checkCommand.ExecuteScalarAsync();

                if (dbExists > 0)
                {
                    // حذف قاعدة البيانات القديمة لضمان إنشاء جداول محدثة
                    string dropDbQuery = $@"
                        ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
                        DROP DATABASE [{databaseName}];";

                    using var dropCommand = new SqlCommand(dropDbQuery, connection);
                    await dropCommand.ExecuteNonQueryAsync();
                }

                // إنشاء قاعدة البيانات
                string createDbQuery = $@"
                    CREATE DATABASE [{databaseName}]
                    COLLATE Arabic_CI_AS";

                using var createCommand = new SqlCommand(createDbQuery, connection);
                await createCommand.ExecuteNonQueryAsync();

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ ملف SQL
        /// Execute SQL File
        /// </summary>
        /// <param name="connectionString">نص الاتصال</param>
        /// <param name="sqlFilePath">مسار ملف SQL</param>
        /// <returns>true إذا تم التنفيذ بنجاح</returns>
        public static async Task<bool> ExecuteSqlFileAsync(string connectionString, string sqlFilePath)
        {
            try
            {
                if (!File.Exists(sqlFilePath))
                    throw new FileNotFoundException($"ملف SQL غير موجود: {sqlFilePath}");

                string sqlContent = await File.ReadAllTextAsync(sqlFilePath, Encoding.UTF8);
                
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // تقسيم المحتوى إلى أوامر منفصلة
                string[] sqlCommands = sqlContent.Split(new string[] { "GO" }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string sqlCommand in sqlCommands)
                {
                    string trimmedCommand = sqlCommand.Trim();
                    if (!string.IsNullOrEmpty(trimmedCommand))
                    {
                        try
                        {
                            using var command = new SqlCommand(trimmedCommand, connection);
                            command.CommandTimeout = 300; // 5 دقائق
                            await command.ExecuteNonQueryAsync();
                        }
                        catch (Exception ex)
                        {
                            throw new Exception($"خطأ في تنفيذ الأمر SQL: {trimmedCommand.Substring(0, Math.Min(100, trimmedCommand.Length))}...\nالخطأ: {ex.Message}", ex);
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ ملف SQL: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من الاتصال بقاعدة البيانات
        /// Test Database Connection
        /// </summary>
        /// <param name="connectionString">نص الاتصال</param>
        /// <returns>true إذا كان الاتصال ناجح</returns>
        public static async Task<bool> TestConnectionAsync(string connectionString)
        {
            try
            {
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();
                return connection.State == System.Data.ConnectionState.Open;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على نص اتصال قاعدة البيانات
        /// Get Database Connection String
        /// </summary>
        /// <param name="serverName">اسم الخادم</param>
        /// <param name="databaseName">اسم قاعدة البيانات</param>
        /// <param name="integratedSecurity">استخدام الأمان المتكامل</param>
        /// <param name="username">اسم المستخدم (اختياري)</param>
        /// <param name="password">كلمة المرور (اختياري)</param>
        /// <returns>نص الاتصال</returns>
        public static string GetConnectionString(string serverName, string databaseName, 
            bool integratedSecurity = true, string? username = null, string? password = null)
        {
            var builder = new SqlConnectionStringBuilder
            {
                DataSource = serverName,
                InitialCatalog = databaseName,
                IntegratedSecurity = integratedSecurity,
                TrustServerCertificate = true,
                ConnectTimeout = 30,
                CommandTimeout = 300
            };

            if (!integratedSecurity && !string.IsNullOrEmpty(username))
            {
                builder.UserID = username;
                builder.Password = password ?? string.Empty;
            }

            return builder.ConnectionString;
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// Create Database Backup
        /// </summary>
        /// <param name="connectionString">نص الاتصال</param>
        /// <param name="databaseName">اسم قاعدة البيانات</param>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        public static async Task<bool> CreateBackupAsync(string connectionString, string databaseName, string backupPath)
        {
            try
            {
                // التأكد من وجود المجلد
                string? directory = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                string backupQuery = $@"
                    BACKUP DATABASE [{databaseName}] 
                    TO DISK = '{backupPath}'
                    WITH FORMAT, INIT, COMPRESSION";

                using var command = new SqlCommand(backupQuery, connection);
                command.CommandTimeout = 600; // 10 دقائق
                await command.ExecuteNonQueryAsync();

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// Restore Database from Backup
        /// </summary>
        /// <param name="connectionString">نص الاتصال</param>
        /// <param name="databaseName">اسم قاعدة البيانات</param>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تمت الاستعادة بنجاح</returns>
        public static async Task<bool> RestoreBackupAsync(string connectionString, string databaseName, string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                    throw new FileNotFoundException($"ملف النسخة الاحتياطية غير موجود: {backupPath}");

                var builder = new SqlConnectionStringBuilder(connectionString);
                string serverConnectionString = $"Server={builder.DataSource};Integrated Security=true;TrustServerCertificate=true;";

                using var connection = new SqlConnection(serverConnectionString);
                await connection.OpenAsync();

                // قطع الاتصالات الموجودة
                string killConnectionsQuery = $@"
                    ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE";

                using var killCommand = new SqlCommand(killConnectionsQuery, connection);
                await killCommand.ExecuteNonQueryAsync();

                // استعادة قاعدة البيانات
                string restoreQuery = $@"
                    RESTORE DATABASE [{databaseName}] 
                    FROM DISK = '{backupPath}'
                    WITH REPLACE";

                using var restoreCommand = new SqlCommand(restoreQuery, connection);
                restoreCommand.CommandTimeout = 600; // 10 دقائق
                await restoreCommand.ExecuteNonQueryAsync();

                // إعادة تفعيل الاتصالات المتعددة
                string multiUserQuery = $@"
                    ALTER DATABASE [{databaseName}] SET MULTI_USER";

                using var multiUserCommand = new SqlCommand(multiUserQuery, connection);
                await multiUserCommand.ExecuteNonQueryAsync();

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", ex);
            }
        }
    }
}
