using Joud.Forms;
using Joud.Utilities;
using System.Configuration;

namespace Joud
{
    internal static class Program
    {
        /// <summary>
        ///  نقطة الدخول الرئيسية للتطبيق
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // تكوين التطبيق
            ApplicationConfiguration.Initialize();

            // تطبيق إعدادات اللغة العربية والتصميم
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // التحقق من وجود الإعداد الأولي
                if (IsFirstRun())
                {
                    // تشغيل شاشة الإعداد الأولي
                    Application.Run(new SetupForm());
                }
                else
                {
                    // تشغيل شاشة تسجيل الدخول
                    using (var loginForm = new LoginForm())
                    {
                        if (loginForm.ShowDialog() == DialogResult.OK)
                        {
                            // تشغيل الشاشة الرئيسية مع بيانات المستخدم
                            Application.Run(new MainForm(loginForm.LoggedInUser!, loginForm.UserCompany!));
                        }
                        // إذا تم إلغاء تسجيل الدخول، يتم إغلاق التطبيق تلقائياً
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ في تشغيل التطبيق:\n{ex.Message}",
                    "خطأ في التطبيق",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// التحقق من كون هذا أول تشغيل للتطبيق
        /// Check if this is the first run of the application
        /// </summary>
        /// <returns>true إذا كان أول تشغيل</returns>
        private static bool IsFirstRun()
        {
            try
            {
                // التحقق من وجود نص الاتصال في ملف التكوين
                string? connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;

                if (string.IsNullOrEmpty(connectionString))
                    return true;

                // التحقق من إمكانية الاتصال بقاعدة البيانات
                var testConnection = DatabaseHelper.TestConnectionAsync(connectionString);
                testConnection.Wait(5000); // انتظار 5 ثواني كحد أقصى

                return !testConnection.Result;
            }
            catch
            {
                return true;
            }
        }
    }
}