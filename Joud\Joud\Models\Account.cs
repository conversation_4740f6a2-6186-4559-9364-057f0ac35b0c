using System;
using System.ComponentModel.DataAnnotations;

namespace Joud.Models
{
    /// <summary>
    /// نموذج الحساب المالي
    /// </summary>
    public class Account
    {
        [Key]
        public int AccountId { get; set; }

        [Required]
        [StringLength(20)]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string AccountName { get; set; } = string.Empty;

        public int? ParentAccountId { get; set; }

        [Required]
        public AccountType AccountType { get; set; }

        public decimal Balance { get; set; }

        public decimal CurrentBalance { get; set; }

        public bool IsActive { get; set; } = true;

        public bool IsFinalAccount { get; set; } = true;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public int CompanyId { get; set; }

        public decimal OpeningBalance { get; set; } = 0;

        [StringLength(100)]
        public string? CreatedBy { get; set; }

        [StringLength(100)]
        public string? ModifiedBy { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        // Navigation Properties
        public virtual Account? ParentAccount { get; set; }
        public virtual Company? Company { get; set; }
        public virtual ICollection<Account> SubAccounts { get; set; } = new List<Account>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
    }

    /// <summary>
    /// أنواع الحسابات المالية
    /// </summary>
    public enum AccountType
    {
        Asset = 1,          // أصول
        Liability = 2,      // خصوم
        Equity = 3,         // حقوق الملكية
        Revenue = 4,        // إيرادات
        Expense = 5         // مصروفات
    }
}
