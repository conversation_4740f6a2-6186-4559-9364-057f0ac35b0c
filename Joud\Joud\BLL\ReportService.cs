using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة التقارير
    /// Report Service
    /// </summary>
    public class ReportService
    {
        private readonly string _connectionString;

        public ReportService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// تقرير المبيعات حسب الفترة
        /// Sales report by period
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <param name="dateFrom">من تاريخ</param>
        /// <param name="dateTo">إلى تاريخ</param>
        /// <returns>تقرير المبيعات</returns>
        public async Task<SalesReport> GetSalesReportAsync(int companyId, DateTime dateFrom, DateTime dateTo)
        {
            using var context = CreateDbContext();

            var salesInvoices = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive &&
                            si.InvoiceDate >= dateFrom && si.InvoiceDate <= dateTo)
                .Include(si => si.Customer)
                .Include(si => si.SalesInvoiceItems)
                    .ThenInclude(sii => sii.Product)
                .ToListAsync();

            var totalInvoices = salesInvoices.Count;
            var totalSales = salesInvoices.Sum(si => si.TotalAmount);
            var totalTax = salesInvoices.Sum(si => si.TaxAmount);
            var totalDiscount = salesInvoices.Sum(si => si.DiscountAmount);
            var totalQuantity = salesInvoices.SelectMany(si => si.SalesInvoiceItems).Sum(sii => sii.Quantity);

            // أفضل العملاء
            var topCustomers = salesInvoices
                .GroupBy(si => new { si.CustomerId, si.Customer.CustomerName })
                .Select(g => new CustomerSalesData
                {
                    CustomerId = g.Key.CustomerId,
                    CustomerName = g.Key.CustomerName,
                    TotalSales = g.Sum(si => si.TotalAmount),
                    InvoiceCount = g.Count()
                })
                .OrderByDescending(c => c.TotalSales)
                .Take(10)
                .ToList();

            // أفضل الأصناف
            var topProducts = salesInvoices
                .SelectMany(si => si.SalesInvoiceItems)
                .GroupBy(sii => new { sii.ProductId, sii.Product.ProductName })
                .Select(g => new ProductSalesData
                {
                    ProductId = g.Key.ProductId,
                    ProductName = g.Key.ProductName,
                    TotalQuantity = g.Sum(sii => sii.Quantity),
                    TotalSales = g.Sum(sii => sii.Total)
                })
                .OrderByDescending(p => p.TotalSales)
                .Take(10)
                .ToList();

            // المبيعات اليومية
            var dailySales = salesInvoices
                .GroupBy(si => si.InvoiceDate.Date)
                .Select(g => new DailySalesData
                {
                    Date = g.Key,
                    TotalSales = g.Sum(si => si.TotalAmount),
                    InvoiceCount = g.Count()
                })
                .OrderBy(d => d.Date)
                .ToList();

            return new SalesReport
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                TotalInvoices = totalInvoices,
                TotalSales = totalSales,
                TotalTax = totalTax,
                TotalDiscount = totalDiscount,
                TotalQuantity = totalQuantity,
                TopCustomers = topCustomers,
                TopProducts = topProducts,
                DailySales = dailySales
            };
        }

        /// <summary>
        /// تقرير المشتريات حسب الفترة
        /// Purchase report by period
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <param name="dateFrom">من تاريخ</param>
        /// <param name="dateTo">إلى تاريخ</param>
        /// <returns>تقرير المشتريات</returns>
        public async Task<PurchaseReport> GetPurchaseReportAsync(int companyId, DateTime dateFrom, DateTime dateTo)
        {
            using var context = CreateDbContext();

            var purchaseInvoices = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive &&
                            pi.InvoiceDate >= dateFrom && pi.InvoiceDate <= dateTo)
                .Include(pi => pi.Supplier)
                .Include(pi => pi.PurchaseInvoiceItems)
                    .ThenInclude(pii => pii.Product)
                .ToListAsync();

            var totalInvoices = purchaseInvoices.Count;
            var totalPurchases = purchaseInvoices.Sum(pi => pi.TotalAmount);
            var totalTax = purchaseInvoices.Sum(pi => pi.TaxAmount);
            var totalDiscount = purchaseInvoices.Sum(pi => pi.DiscountAmount);
            var totalQuantity = purchaseInvoices.SelectMany(pi => pi.PurchaseInvoiceItems).Sum(pii => pii.Quantity);

            // أفضل الموردين
            var topSuppliers = purchaseInvoices
                .GroupBy(pi => new { pi.SupplierId, pi.Supplier.SupplierName })
                .Select(g => new SupplierPurchaseData
                {
                    SupplierId = g.Key.SupplierId,
                    SupplierName = g.Key.SupplierName,
                    TotalPurchases = g.Sum(pi => pi.TotalAmount),
                    InvoiceCount = g.Count()
                })
                .OrderByDescending(s => s.TotalPurchases)
                .Take(10)
                .ToList();

            // أكثر الأصناف شراءً
            var topProducts = purchaseInvoices
                .SelectMany(pi => pi.PurchaseInvoiceItems)
                .GroupBy(pii => new { pii.ProductId, pii.Product.ProductName })
                .Select(g => new ProductPurchaseData
                {
                    ProductId = g.Key.ProductId,
                    ProductName = g.Key.ProductName,
                    TotalQuantity = g.Sum(pii => pii.Quantity),
                    TotalPurchases = g.Sum(pii => pii.Total)
                })
                .OrderByDescending(p => p.TotalPurchases)
                .Take(10)
                .ToList();

            // المشتريات اليومية
            var dailyPurchases = purchaseInvoices
                .GroupBy(pi => pi.InvoiceDate.Date)
                .Select(g => new DailyPurchaseData
                {
                    Date = g.Key,
                    TotalPurchases = g.Sum(pi => pi.TotalAmount),
                    InvoiceCount = g.Count()
                })
                .OrderBy(d => d.Date)
                .ToList();

            return new PurchaseReport
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                TotalInvoices = totalInvoices,
                TotalPurchases = totalPurchases,
                TotalTax = totalTax,
                TotalDiscount = totalDiscount,
                TotalQuantity = totalQuantity,
                TopSuppliers = topSuppliers,
                TopProducts = topProducts,
                DailyPurchases = dailyPurchases
            };
        }

        /// <summary>
        /// تقرير أرصدة العملاء
        /// Customer balances report
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>تقرير أرصدة العملاء</returns>
        public async Task<CustomerBalanceReport> GetCustomerBalanceReportAsync(int companyId)
        {
            using var context = CreateDbContext();

            var customers = await context.Customers
                .Where(c => c.CompanyId == companyId && c.IsActive)
                .Select(c => new CustomerBalanceData
                {
                    CustomerId = c.CustomerId,
                    CustomerName = c.CustomerName,
                    CustomerCode = c.CustomerCode,
                    Phone = c.Phone,
                    CurrentBalance = c.CurrentBalance,
                    CreditLimit = c.CreditLimit
                })
                .OrderByDescending(c => c.CurrentBalance)
                .ToListAsync();

            var totalBalance = customers.Sum(c => c.CurrentBalance);
            var customersWithBalance = customers.Count(c => c.CurrentBalance > 0);
            var customersOverLimit = customers.Count(c => c.CurrentBalance > c.CreditLimit);

            return new CustomerBalanceReport
            {
                Customers = customers,
                TotalBalance = totalBalance,
                CustomersWithBalance = customersWithBalance,
                CustomersOverLimit = customersOverLimit
            };
        }

        /// <summary>
        /// تقرير أرصدة الموردين
        /// Supplier balances report
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>تقرير أرصدة الموردين</returns>
        public async Task<SupplierBalanceReport> GetSupplierBalanceReportAsync(int companyId)
        {
            using var context = CreateDbContext();

            var suppliers = await context.Suppliers
                .Where(s => s.CompanyId == companyId && s.IsActive)
                .Select(s => new SupplierBalanceData
                {
                    SupplierId = s.SupplierId,
                    SupplierName = s.SupplierName,
                    SupplierCode = s.SupplierCode,
                    Phone = s.Phone,
                    CurrentBalance = s.CurrentBalance,
                    CreditLimit = s.CreditLimit
                })
                .OrderByDescending(s => s.CurrentBalance)
                .ToListAsync();

            var totalBalance = suppliers.Sum(s => s.CurrentBalance);
            var suppliersWithBalance = suppliers.Count(s => s.CurrentBalance > 0);
            var suppliersOverLimit = suppliers.Count(s => s.CurrentBalance > s.CreditLimit);

            return new SupplierBalanceReport
            {
                Suppliers = suppliers,
                TotalBalance = totalBalance,
                SuppliersWithBalance = suppliersWithBalance,
                SuppliersOverLimit = suppliersOverLimit
            };
        }

        /// <summary>
        /// تقرير المخزون
        /// Inventory report
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>تقرير المخزون</returns>
        public async Task<InventoryReport> GetInventoryReportAsync(int companyId)
        {
            using var context = CreateDbContext();

            var products = await context.Products
                .Where(p => p.CompanyId == companyId && p.IsActive)
                .Include(p => p.MainCategory)
                .Include(p => p.SubCategory)
                .Include(p => p.Unit)
                .Include(p => p.Warehouse)
                .Select(p => new ProductInventoryData
                {
                    ProductId = p.ProductId,
                    ProductCode = p.ProductCode,
                    ProductName = p.ProductName,
                    MainCategoryName = p.MainCategory.CategoryName,
                    SubCategoryName = p.SubCategory != null ? p.SubCategory.CategoryName : "",
                    UnitName = p.Unit.UnitName,
                    WarehouseName = p.Warehouse.WarehouseName,
                    CurrentStock = p.CurrentStock,
                    MinimumStock = p.MinimumStock,
                    PurchasePrice = p.PurchasePrice,
                    SalePrice = p.SalePrice,
                    TotalValue = p.CurrentStock * p.PurchasePrice
                })
                .OrderBy(p => p.ProductName)
                .ToListAsync();

            var totalProducts = products.Count;
            var lowStockProducts = products.Count(p => p.CurrentStock <= p.MinimumStock);
            var outOfStockProducts = products.Count(p => p.CurrentStock <= 0);
            var totalInventoryValue = products.Sum(p => p.TotalValue);

            return new InventoryReport
            {
                Products = products,
                TotalProducts = totalProducts,
                LowStockProducts = lowStockProducts,
                OutOfStockProducts = outOfStockProducts,
                TotalInventoryValue = totalInventoryValue
            };
        }

        /// <summary>
        /// تقرير حركة المخزون
        /// Inventory movement report
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <param name="productId">معرف الصنف (اختياري)</param>
        /// <param name="dateFrom">من تاريخ</param>
        /// <param name="dateTo">إلى تاريخ</param>
        /// <returns>تقرير حركة المخزون</returns>
        public async Task<InventoryMovementReport> GetInventoryMovementReportAsync(int companyId, int? productId, DateTime dateFrom, DateTime dateTo)
        {
            using var context = CreateDbContext();

            var movements = new List<InventoryMovementData>();

            // حركات المبيعات (خروج)
            var salesQuery = context.SalesInvoiceItems
                .Where(sii => sii.SalesInvoice.CompanyId == companyId && 
                             sii.SalesInvoice.IsActive &&
                             sii.SalesInvoice.InvoiceDate >= dateFrom && 
                             sii.SalesInvoice.InvoiceDate <= dateTo)
                .Include(sii => sii.Product)
                .Include(sii => sii.SalesInvoice)
                    .ThenInclude(si => si.Customer)
                .AsQueryable();

            if (productId.HasValue)
                salesQuery = salesQuery.Where(sii => sii.ProductId == productId.Value);

            var salesMovements = await salesQuery
                .Select(sii => new InventoryMovementData
                {
                    Date = sii.SalesInvoice.InvoiceDate,
                    ProductName = sii.Product.ProductName,
                    MovementType = "مبيعات",
                    ReferenceNumber = sii.SalesInvoice.InvoiceNumber,
                    ReferenceName = sii.SalesInvoice.Customer.CustomerName,
                    QuantityIn = 0,
                    QuantityOut = sii.Quantity,
                    UnitPrice = sii.UnitPrice,
                    Total = sii.Total
                })
                .ToListAsync();

            movements.AddRange(salesMovements);

            // حركات المشتريات (دخول)
            var purchaseQuery = context.PurchaseInvoiceItems
                .Where(pii => pii.PurchaseInvoice.CompanyId == companyId && 
                             pii.PurchaseInvoice.IsActive &&
                             pii.PurchaseInvoice.InvoiceDate >= dateFrom && 
                             pii.PurchaseInvoice.InvoiceDate <= dateTo)
                .Include(pii => pii.Product)
                .Include(pii => pii.PurchaseInvoice)
                    .ThenInclude(pi => pi.Supplier)
                .AsQueryable();

            if (productId.HasValue)
                purchaseQuery = purchaseQuery.Where(pii => pii.ProductId == productId.Value);

            var purchaseMovements = await purchaseQuery
                .Select(pii => new InventoryMovementData
                {
                    Date = pii.PurchaseInvoice.InvoiceDate,
                    ProductName = pii.Product.ProductName,
                    MovementType = "مشتريات",
                    ReferenceNumber = pii.PurchaseInvoice.InvoiceNumber,
                    ReferenceName = pii.PurchaseInvoice.Supplier.SupplierName,
                    QuantityIn = pii.Quantity,
                    QuantityOut = 0,
                    UnitPrice = pii.UnitPrice,
                    Total = pii.Total
                })
                .ToListAsync();

            movements.AddRange(purchaseMovements);

            // ترتيب الحركات حسب التاريخ
            movements = movements.OrderBy(m => m.Date).ToList();

            var totalQuantityIn = movements.Sum(m => m.QuantityIn);
            var totalQuantityOut = movements.Sum(m => m.QuantityOut);
            var totalValueIn = movements.Where(m => m.QuantityIn > 0).Sum(m => m.Total);
            var totalValueOut = movements.Where(m => m.QuantityOut > 0).Sum(m => m.Total);

            return new InventoryMovementReport
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                ProductId = productId,
                Movements = movements,
                TotalQuantityIn = totalQuantityIn,
                TotalQuantityOut = totalQuantityOut,
                TotalValueIn = totalValueIn,
                TotalValueOut = totalValueOut
            };
        }

        /// <summary>
        /// التقرير المالي
        /// Financial report
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <param name="dateFrom">من تاريخ</param>
        /// <param name="dateTo">إلى تاريخ</param>
        /// <returns>التقرير المالي</returns>
        public async Task<FinancialReport> GetFinancialReportAsync(int companyId, DateTime dateFrom, DateTime dateTo)
        {
            using var context = CreateDbContext();

            // إجمالي المبيعات
            var totalSales = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive &&
                            si.InvoiceDate >= dateFrom && si.InvoiceDate <= dateTo)
                .SumAsync(si => si.TotalAmount);

            // إجمالي المشتريات
            var totalPurchases = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive &&
                            pi.InvoiceDate >= dateFrom && pi.InvoiceDate <= dateTo)
                .SumAsync(pi => pi.TotalAmount);

            // إجمالي الضرائب
            var totalTaxSales = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive &&
                            si.InvoiceDate >= dateFrom && si.InvoiceDate <= dateTo)
                .SumAsync(si => si.TaxAmount);

            var totalTaxPurchases = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive &&
                            pi.InvoiceDate >= dateFrom && pi.InvoiceDate <= dateTo)
                .SumAsync(pi => pi.TaxAmount);

            // إجمالي الخصومات
            var totalDiscountSales = await context.SalesInvoices
                .Where(si => si.CompanyId == companyId && si.IsActive &&
                            si.InvoiceDate >= dateFrom && si.InvoiceDate <= dateTo)
                .SumAsync(si => si.DiscountAmount);

            var totalDiscountPurchases = await context.PurchaseInvoices
                .Where(pi => pi.CompanyId == companyId && pi.IsActive &&
                            pi.InvoiceDate >= dateFrom && pi.InvoiceDate <= dateTo)
                .SumAsync(pi => pi.DiscountAmount);

            // حساب الأرباح
            var grossProfit = totalSales - totalPurchases;
            var netProfit = grossProfit - (totalTaxSales - totalTaxPurchases);

            return new FinancialReport
            {
                DateFrom = dateFrom,
                DateTo = dateTo,
                TotalRevenue = totalSales,
                TotalExpenses = totalPurchases,
                GrossProfit = grossProfit,
                NetProfit = netProfit,
                TotalTax = totalTaxSales - totalTaxPurchases,
                TotalDiscount = totalDiscountSales + totalDiscountPurchases,
                ProfitMargin = totalSales > 0 ? (netProfit / totalSales) * 100 : 0
            };
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }
}
