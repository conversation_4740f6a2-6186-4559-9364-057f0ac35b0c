using System;
using System.Collections.Generic;

namespace Joud.Models
{
    /// <summary>
    /// نموذج بيانات التقرير
    /// </summary>
    public class ReportData
    {
        public List<string> Columns { get; set; } = new List<string>();
        public List<List<object>> Rows { get; set; } = new List<List<object>>();
        public Dictionary<string, object> Summary { get; set; } = new Dictionary<string, object>();
        public string Title { get; set; } = string.Empty;
        public DateTime GeneratedDate { get; set; } = DateTime.Now;
        public string GeneratedBy { get; set; } = string.Empty;
    }
}
