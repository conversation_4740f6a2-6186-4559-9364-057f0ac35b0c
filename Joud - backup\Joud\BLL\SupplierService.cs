using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة الموردين
    /// Supplier Management Service
    /// </summary>
    public class SupplierService
    {
        private readonly string _connectionString;

        public SupplierService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// الحصول على جميع الموردين للشركة
        /// Get all suppliers for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الموردين</returns>
        public async Task<List<Supplier>> GetAllSuppliersAsync(int companyId)
        {
            using var context = CreateDbContext();
            return await context.Suppliers
                .Where(s => s.CompanyId == companyId && s.IsActive)
                .Include(s => s.CreatedByUser)
                .OrderBy(s => s.SupplierName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على مورد بالمعرف
        /// Get supplier by ID
        /// </summary>
        /// <param name="supplierId">معرف المورد</param>
        /// <returns>بيانات المورد</returns>
        public async Task<Supplier?> GetSupplierByIdAsync(int supplierId)
        {
            using var context = CreateDbContext();
            return await context.Suppliers
                .Include(s => s.CreatedByUser)
                .Include(s => s.Company)
                .FirstOrDefaultAsync(s => s.SupplierId == supplierId && s.IsActive);
        }

        /// <summary>
        /// الحصول على مورد بالكود
        /// Get supplier by code
        /// </summary>
        /// <param name="supplierCode">كود المورد</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات المورد</returns>
        public async Task<Supplier?> GetSupplierByCodeAsync(string supplierCode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.Suppliers
                .FirstOrDefaultAsync(s => s.SupplierCode == supplierCode && 
                                         s.CompanyId == companyId && s.IsActive);
        }

        /// <summary>
        /// البحث في الموردين
        /// Search suppliers
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الموردين المطابقة</returns>
        public async Task<List<Supplier>> SearchSuppliersAsync(string searchTerm, int companyId)
        {
            using var context = CreateDbContext();
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllSuppliersAsync(companyId);

            searchTerm = searchTerm.Trim().ToLower();

            return await context.Suppliers
                .Where(s => s.CompanyId == companyId && s.IsActive &&
                           (s.SupplierName.ToLower().Contains(searchTerm) ||
                            s.SupplierCode.ToLower().Contains(searchTerm) ||
                            (s.Phone != null && s.Phone.Contains(searchTerm)) ||
                            (s.Mobile != null && s.Mobile.Contains(searchTerm)) ||
                            (s.Email != null && s.Email.ToLower().Contains(searchTerm))))
                .Include(s => s.CreatedByUser)
                .OrderBy(s => s.SupplierName)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة مورد جديد
        /// Add new supplier
        /// </summary>
        /// <param name="supplier">بيانات المورد</param>
        /// <returns>معرف المورد الجديد</returns>
        public async Task<int> AddSupplierAsync(Supplier supplier)
        {
            using var context = CreateDbContext();
            
            // التحقق من عدم تكرار الكود
            bool codeExists = await context.Suppliers
                .AnyAsync(s => s.SupplierCode == supplier.SupplierCode && 
                              s.CompanyId == supplier.CompanyId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود المورد '{supplier.SupplierCode}' موجود مسبقاً");

            // إنشاء كود تلقائي إذا لم يتم تحديده
            if (string.IsNullOrEmpty(supplier.SupplierCode))
            {
                supplier.SupplierCode = await GenerateSupplierCodeAsync(supplier.CompanyId);
            }

            supplier.CreatedDate = DateTime.Now;
            supplier.IsActive = true;

            context.Suppliers.Add(supplier);
            await context.SaveChangesAsync();

            return supplier.SupplierId;
        }

        /// <summary>
        /// تحديث بيانات مورد
        /// Update supplier
        /// </summary>
        /// <param name="supplier">بيانات المورد المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateSupplierAsync(Supplier supplier)
        {
            using var context = CreateDbContext();

            var existingSupplier = await context.Suppliers
                .FirstOrDefaultAsync(s => s.SupplierId == supplier.SupplierId);

            if (existingSupplier == null)
                throw new InvalidOperationException("المورد غير موجود");

            // التحقق من عدم تكرار الكود مع موردين آخرين
            bool codeExists = await context.Suppliers
                .AnyAsync(s => s.SupplierCode == supplier.SupplierCode && 
                              s.CompanyId == supplier.CompanyId &&
                              s.SupplierId != supplier.SupplierId);

            if (codeExists)
                throw new InvalidOperationException($"كود المورد '{supplier.SupplierCode}' موجود مسبقاً");

            // تحديث البيانات
            existingSupplier.SupplierCode = supplier.SupplierCode;
            existingSupplier.SupplierName = supplier.SupplierName;
            existingSupplier.SupplierNameEn = supplier.SupplierNameEn;
            existingSupplier.Phone = supplier.Phone;
            existingSupplier.Mobile = supplier.Mobile;
            existingSupplier.Email = supplier.Email;
            existingSupplier.Address = supplier.Address;
            existingSupplier.City = supplier.City;
            existingSupplier.Country = supplier.Country;
            existingSupplier.TaxNumber = supplier.TaxNumber;
            existingSupplier.CreditLimit = supplier.CreditLimit;
            existingSupplier.ModifiedDate = DateTime.Now;
            existingSupplier.ModifiedBy = supplier.ModifiedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف مورد (حذف منطقي)
        /// Delete supplier (soft delete)
        /// </summary>
        /// <param name="supplierId">معرف المورد</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteSupplierAsync(int supplierId, int deletedBy)
        {
            using var context = CreateDbContext();

            var supplier = await context.Suppliers
                .FirstOrDefaultAsync(s => s.SupplierId == supplierId);

            if (supplier == null)
                throw new InvalidOperationException("المورد غير موجود");

            // التحقق من عدم وجود فواتير مرتبطة
            bool hasInvoices = await context.PurchaseInvoices
                .AnyAsync(pi => pi.SupplierId == supplierId);

            if (hasInvoices)
                throw new InvalidOperationException("لا يمكن حذف المورد لوجود فواتير مرتبطة به");

            // حذف منطقي
            supplier.IsActive = false;
            supplier.ModifiedDate = DateTime.Now;
            supplier.ModifiedBy = deletedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// إنشاء كود مورد تلقائي
        /// Generate automatic supplier code
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>كود المورد الجديد</returns>
        public async Task<string> GenerateSupplierCodeAsync(int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على أعلى رقم موجود
            var lastSupplier = await context.Suppliers
                .Where(s => s.CompanyId == companyId && s.SupplierCode.StartsWith("SUPP"))
                .OrderByDescending(s => s.SupplierCode)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastSupplier != null)
            {
                string numberPart = lastSupplier.SupplierCode.Substring(4);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"SUPP{nextNumber:000000}";
        }

        /// <summary>
        /// الحصول على إحصائيات الموردين
        /// Get supplier statistics
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>إحصائيات الموردين</returns>
        public async Task<SupplierStatistics> GetSupplierStatisticsAsync(int companyId)
        {
            using var context = CreateDbContext();

            var totalSuppliers = await context.Suppliers
                .CountAsync(s => s.CompanyId == companyId && s.IsActive);

            var suppliersWithBalance = await context.Suppliers
                .CountAsync(s => s.CompanyId == companyId && s.IsActive && s.CurrentBalance > 0);

            var totalBalance = await context.Suppliers
                .Where(s => s.CompanyId == companyId && s.IsActive)
                .SumAsync(s => s.CurrentBalance);

            var newSuppliersThisMonth = await context.Suppliers
                .CountAsync(s => s.CompanyId == companyId && s.IsActive &&
                               s.CreatedDate.Month == DateTime.Now.Month &&
                               s.CreatedDate.Year == DateTime.Now.Year);

            return new SupplierStatistics
            {
                TotalSuppliers = totalSuppliers,
                SuppliersWithBalance = suppliersWithBalance,
                TotalBalance = totalBalance,
                NewSuppliersThisMonth = newSuppliersThisMonth
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات المورد
        /// Validate supplier data
        /// </summary>
        /// <param name="supplier">بيانات المورد</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateSupplier(Supplier supplier)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(supplier.SupplierName))
                errors.Add("اسم المورد مطلوب");

            if (supplier.SupplierName?.Length > 200)
                errors.Add("اسم المورد يجب أن يكون أقل من 200 حرف");

            if (!string.IsNullOrEmpty(supplier.Email) && !IsValidEmail(supplier.Email))
                errors.Add("البريد الإلكتروني غير صحيح");

            if (supplier.CreditLimit < 0)
                errors.Add("الحد الائتماني لا يمكن أن يكون سالباً");

            return errors;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }

    /// <summary>
    /// إحصائيات الموردين
    /// Supplier Statistics
    /// </summary>
    public class SupplierStatistics
    {
        public int TotalSuppliers { get; set; }
        public int SuppliersWithBalance { get; set; }
        public decimal TotalBalance { get; set; }
        public int NewSuppliersThisMonth { get; set; }
    }
}
