using Joud.DAL;
using Joud.Models;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة إدارة الفئات الرئيسية
    /// Main Category Management Service
    /// </summary>
    public class MainCategoryService
    {
        private readonly string _connectionString;

        public MainCategoryService()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new InvalidOperationException("لم يتم العثور على نص الاتصال بقاعدة البيانات");
        }

        /// <summary>
        /// الحصول على جميع الفئات الرئيسية للشركة
        /// Get all main categories for company
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الفئات الرئيسية</returns>
        public async Task<List<MainCategory>> GetAllMainCategoriesAsync(int companyId)
        {
            using var context = CreateDbContext();

            try
            {
                return await context.MainCategories
                    .Where(mc => mc.CompanyId == companyId && mc.IsActive)
                    .Include(mc => mc.CreatedByUser)
                    .Include(mc => mc.SubCategories.Where(sc => sc.IsActive))
                    .OrderBy(mc => mc.CategoryName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                // إذا فشل تحميل SubCategories، نحمل MainCategories فقط
                if (ex.InnerException?.Message.Contains("Invalid column name") == true)
                {
                    return await context.MainCategories
                        .Where(mc => mc.CompanyId == companyId && mc.IsActive)
                        .Include(mc => mc.CreatedByUser)
                        .OrderBy(mc => mc.CategoryName)
                        .ToListAsync();
                }
                throw;
            }
        }

        /// <summary>
        /// الحصول على فئة رئيسية بالمعرف
        /// Get main category by ID
        /// </summary>
        /// <param name="mainCategoryId">معرف الفئة الرئيسية</param>
        /// <returns>بيانات الفئة الرئيسية</returns>
        public async Task<MainCategory?> GetMainCategoryByIdAsync(int mainCategoryId)
        {
            using var context = CreateDbContext();
            return await context.MainCategories
                .Include(mc => mc.CreatedByUser)
                .Include(mc => mc.Company)
                .Include(mc => mc.SubCategories.Where(sc => sc.IsActive))
                .FirstOrDefaultAsync(mc => mc.MainCategoryId == mainCategoryId && mc.IsActive);
        }

        /// <summary>
        /// الحصول على فئة رئيسية بالكود
        /// Get main category by code
        /// </summary>
        /// <param name="categoryCode">كود الفئة</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>بيانات الفئة الرئيسية</returns>
        public async Task<MainCategory?> GetMainCategoryByCodeAsync(string categoryCode, int companyId)
        {
            using var context = CreateDbContext();
            return await context.MainCategories
                .FirstOrDefaultAsync(mc => mc.CategoryCode == categoryCode && 
                                          mc.CompanyId == companyId && mc.IsActive);
        }

        /// <summary>
        /// البحث في الفئات الرئيسية
        /// Search main categories
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>قائمة الفئات الرئيسية المطابقة</returns>
        public async Task<List<MainCategory>> SearchMainCategoriesAsync(string searchTerm, int companyId)
        {
            using var context = CreateDbContext();
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllMainCategoriesAsync(companyId);

            searchTerm = searchTerm.Trim().ToLower();

            return await context.MainCategories
                .Where(mc => mc.CompanyId == companyId && mc.IsActive &&
                            (mc.CategoryName.ToLower().Contains(searchTerm) ||
                             mc.CategoryCode.ToLower().Contains(searchTerm) ||
                             (mc.CategoryNameEn != null && mc.CategoryNameEn.ToLower().Contains(searchTerm)) ||
                             (mc.Description != null && mc.Description.ToLower().Contains(searchTerm))))
                .Include(mc => mc.CreatedByUser)
                .Include(mc => mc.SubCategories.Where(sc => sc.IsActive))
                .OrderBy(mc => mc.CategoryName)
                .ToListAsync();
        }

        /// <summary>
        /// إضافة فئة رئيسية جديدة
        /// Add new main category
        /// </summary>
        /// <param name="mainCategory">بيانات الفئة الرئيسية</param>
        /// <returns>معرف الفئة الرئيسية الجديدة</returns>
        public async Task<int> AddMainCategoryAsync(MainCategory mainCategory)
        {
            using var context = CreateDbContext();
            
            // التحقق من عدم تكرار الكود
            bool codeExists = await context.MainCategories
                .AnyAsync(mc => mc.CategoryCode == mainCategory.CategoryCode && 
                               mc.CompanyId == mainCategory.CompanyId);
            
            if (codeExists)
                throw new InvalidOperationException($"كود الفئة '{mainCategory.CategoryCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الاسم
            bool nameExists = await context.MainCategories
                .AnyAsync(mc => mc.CategoryName == mainCategory.CategoryName && 
                               mc.CompanyId == mainCategory.CompanyId);
            
            if (nameExists)
                throw new InvalidOperationException($"اسم الفئة '{mainCategory.CategoryName}' موجود مسبقاً");

            // إنشاء كود تلقائي إذا لم يتم تحديده
            if (string.IsNullOrEmpty(mainCategory.CategoryCode))
            {
                mainCategory.CategoryCode = await GenerateMainCategoryCodeAsync(mainCategory.CompanyId);
            }

            mainCategory.CreatedDate = DateTime.Now;
            mainCategory.IsActive = true;

            context.MainCategories.Add(mainCategory);
            await context.SaveChangesAsync();

            return mainCategory.MainCategoryId;
        }

        /// <summary>
        /// تحديث بيانات فئة رئيسية
        /// Update main category
        /// </summary>
        /// <param name="mainCategory">بيانات الفئة الرئيسية المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public async Task<bool> UpdateMainCategoryAsync(MainCategory mainCategory)
        {
            using var context = CreateDbContext();

            var existingCategory = await context.MainCategories
                .FirstOrDefaultAsync(mc => mc.MainCategoryId == mainCategory.MainCategoryId);

            if (existingCategory == null)
                throw new InvalidOperationException("الفئة الرئيسية غير موجودة");

            // التحقق من عدم تكرار الكود مع فئات أخرى
            bool codeExists = await context.MainCategories
                .AnyAsync(mc => mc.CategoryCode == mainCategory.CategoryCode && 
                               mc.CompanyId == mainCategory.CompanyId &&
                               mc.MainCategoryId != mainCategory.MainCategoryId);

            if (codeExists)
                throw new InvalidOperationException($"كود الفئة '{mainCategory.CategoryCode}' موجود مسبقاً");

            // التحقق من عدم تكرار الاسم مع فئات أخرى
            bool nameExists = await context.MainCategories
                .AnyAsync(mc => mc.CategoryName == mainCategory.CategoryName && 
                               mc.CompanyId == mainCategory.CompanyId &&
                               mc.MainCategoryId != mainCategory.MainCategoryId);

            if (nameExists)
                throw new InvalidOperationException($"اسم الفئة '{mainCategory.CategoryName}' موجود مسبقاً");

            // تحديث البيانات
            existingCategory.CategoryCode = mainCategory.CategoryCode;
            existingCategory.CategoryName = mainCategory.CategoryName;
            existingCategory.CategoryNameEn = mainCategory.CategoryNameEn;
            existingCategory.Description = mainCategory.Description;
            existingCategory.ModifiedDate = DateTime.Now;
            existingCategory.ModifiedBy = mainCategory.ModifiedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// حذف فئة رئيسية (حذف منطقي)
        /// Delete main category (soft delete)
        /// </summary>
        /// <param name="mainCategoryId">معرف الفئة الرئيسية</param>
        /// <param name="deletedBy">معرف المستخدم الذي قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteMainCategoryAsync(int mainCategoryId, int deletedBy)
        {
            using var context = CreateDbContext();

            var mainCategory = await context.MainCategories
                .Include(mc => mc.SubCategories)
                .Include(mc => mc.Products)
                .FirstOrDefaultAsync(mc => mc.MainCategoryId == mainCategoryId);

            if (mainCategory == null)
                throw new InvalidOperationException("الفئة الرئيسية غير موجودة");

            // التحقق من عدم وجود فئات فرعية مرتبطة
            if (mainCategory.SubCategories.Any(sc => sc.IsActive))
                throw new InvalidOperationException("لا يمكن حذف الفئة الرئيسية لوجود فئات فرعية مرتبطة بها");

            // التحقق من عدم وجود أصناف مرتبطة
            if (mainCategory.Products.Any(p => p.IsActive))
                throw new InvalidOperationException("لا يمكن حذف الفئة الرئيسية لوجود أصناف مرتبطة بها");

            // حذف منطقي
            mainCategory.IsActive = false;
            mainCategory.ModifiedDate = DateTime.Now;
            mainCategory.ModifiedBy = deletedBy;

            await context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// إنشاء كود فئة رئيسية تلقائي
        /// Generate automatic main category code
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>كود الفئة الرئيسية الجديد</returns>
        public async Task<string> GenerateMainCategoryCodeAsync(int companyId)
        {
            using var context = CreateDbContext();

            // الحصول على أعلى رقم موجود
            var lastCategory = await context.MainCategories
                .Where(mc => mc.CompanyId == companyId && mc.CategoryCode.StartsWith("MCAT"))
                .OrderByDescending(mc => mc.CategoryCode)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastCategory != null)
            {
                string numberPart = lastCategory.CategoryCode.Substring(4);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"MCAT{nextNumber:000}";
        }

        /// <summary>
        /// الحصول على إحصائيات الفئات الرئيسية
        /// Get main category statistics
        /// </summary>
        /// <param name="companyId">معرف الشركة</param>
        /// <returns>إحصائيات الفئات الرئيسية</returns>
        public async Task<MainCategoryStatistics> GetMainCategoryStatisticsAsync(int companyId)
        {
            using var context = CreateDbContext();

            var totalMainCategories = await context.MainCategories
                .CountAsync(mc => mc.CompanyId == companyId && mc.IsActive);

            var totalSubCategories = await context.SubCategories
                .CountAsync(sc => sc.CompanyId == companyId && sc.IsActive);

            var totalProducts = await context.Products
                .CountAsync(p => p.CompanyId == companyId && p.IsActive);

            var categoriesWithProducts = await context.MainCategories
                .Where(mc => mc.CompanyId == companyId && mc.IsActive)
                .CountAsync(mc => mc.Products.Any(p => p.IsActive));

            return new MainCategoryStatistics
            {
                TotalMainCategories = totalMainCategories,
                TotalSubCategories = totalSubCategories,
                TotalProducts = totalProducts,
                CategoriesWithProducts = categoriesWithProducts
            };
        }

        /// <summary>
        /// التحقق من صحة بيانات الفئة الرئيسية
        /// Validate main category data
        /// </summary>
        /// <param name="mainCategory">بيانات الفئة الرئيسية</param>
        /// <returns>قائمة أخطاء التحقق</returns>
        public List<string> ValidateMainCategory(MainCategory mainCategory)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(mainCategory.CategoryName))
                errors.Add("اسم الفئة مطلوب");

            if (mainCategory.CategoryName?.Length > 200)
                errors.Add("اسم الفئة يجب أن يكون أقل من 200 حرف");

            if (string.IsNullOrWhiteSpace(mainCategory.CategoryCode))
                errors.Add("كود الفئة مطلوب");

            if (mainCategory.CategoryCode?.Length > 20)
                errors.Add("كود الفئة يجب أن يكون أقل من 20 حرف");

            return errors;
        }

        private JoudDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;
            return new JoudDbContext(options);
        }
    }

    /// <summary>
    /// إحصائيات الفئات الرئيسية
    /// Main Category Statistics
    /// </summary>
    public class MainCategoryStatistics
    {
        public int TotalMainCategories { get; set; }
        public int TotalSubCategories { get; set; }
        public int TotalProducts { get; set; }
        public int CategoriesWithProducts { get; set; }
    }
}
