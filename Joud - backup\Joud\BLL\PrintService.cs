using Joud.Models;
using System.Drawing.Printing;
using System.Text;

namespace Joud.BLL
{
    /// <summary>
    /// خدمة الطباعة
    /// Print Service
    /// </summary>
    public class PrintService
    {
        private string _printContent = string.Empty;
        private Font _printFont = new Font("Arial", 10);
        private Font _headerFont = new Font("Arial", 14, FontStyle.Bold);
        private Font _titleFont = new Font("Arial", 16, FontStyle.Bold);

        /// <summary>
        /// طباعة فاتورة مبيعات
        /// Print sales invoice
        /// </summary>
        /// <param name="invoice">فاتورة المبيعات</param>
        /// <param name="company">بيانات الشركة</param>
        public void PrintSalesInvoice(SalesInvoice invoice, Company company)
        {
            _printContent = GenerateSalesInvoiceContent(invoice, company);
            
            var printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
            
            // إظهار معاينة الطباعة
            var printPreviewDialog = new PrintPreviewDialog
            {
                Document = printDocument,
                Width = 800,
                Height = 600,
                UseAntiAlias = true
            };
            
            printPreviewDialog.ShowDialog();
        }

        /// <summary>
        /// طباعة فاتورة مشتريات
        /// Print purchase invoice
        /// </summary>
        /// <param name="invoice">فاتورة المشتريات</param>
        /// <param name="company">بيانات الشركة</param>
        public void PrintPurchaseInvoice(PurchaseInvoice invoice, Company company)
        {
            _printContent = GeneratePurchaseInvoiceContent(invoice, company);
            
            var printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
            
            // إظهار معاينة الطباعة
            var printPreviewDialog = new PrintPreviewDialog
            {
                Document = printDocument,
                Width = 800,
                Height = 600,
                UseAntiAlias = true
            };
            
            printPreviewDialog.ShowDialog();
        }

        /// <summary>
        /// طباعة تقرير
        /// Print report
        /// </summary>
        /// <param name="reportTitle">عنوان التقرير</param>
        /// <param name="reportContent">محتوى التقرير</param>
        /// <param name="company">بيانات الشركة</param>
        public void PrintReport(string reportTitle, string reportContent, Company company)
        {
            _printContent = GenerateReportContent(reportTitle, reportContent, company);
            
            var printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
            
            // إظهار معاينة الطباعة
            var printPreviewDialog = new PrintPreviewDialog
            {
                Document = printDocument,
                Width = 800,
                Height = 600,
                UseAntiAlias = true
            };
            
            printPreviewDialog.ShowDialog();
        }

        private void PrintDocument_PrintPage(object? sender, PrintPageEventArgs e)
        {
            if (e.Graphics == null) return;

            var lines = _printContent.Split('\n');
            float yPosition = 50;
            float leftMargin = 50;
            float rightMargin = e.PageBounds.Width - 50;

            foreach (var line in lines)
            {
                if (yPosition > e.PageBounds.Height - 100)
                {
                    e.HasMorePages = true;
                    return;
                }

                Font currentFont = _printFont;
                
                // تحديد نوع الخط حسب محتوى السطر
                if (line.Contains("===") || line.StartsWith("شركة") || line.StartsWith("فاتورة"))
                {
                    currentFont = _titleFont;
                }
                else if (line.Contains("---") || line.StartsWith("رقم") || line.StartsWith("تاريخ"))
                {
                    currentFont = _headerFont;
                }

                // قياس النص
                var textSize = e.Graphics.MeasureString(line, currentFont);
                
                // محاذاة النص للوسط للعناوين
                float xPosition = leftMargin;
                if (line.Contains("===") || line.StartsWith("شركة"))
                {
                    xPosition = (e.PageBounds.Width - textSize.Width) / 2;
                }

                e.Graphics.DrawString(line, currentFont, Brushes.Black, xPosition, yPosition);
                yPosition += currentFont.GetHeight(e.Graphics) + 5;
            }

            e.HasMorePages = false;
        }

        private string GenerateSalesInvoiceContent(SalesInvoice invoice, Company company)
        {
            var content = new StringBuilder();
            
            // رأس الفاتورة
            content.AppendLine("===============================================");
            content.AppendLine($"شركة {company.CompanyName}");
            content.AppendLine($"{company.Address}");
            content.AppendLine($"هاتف: {company.Phone} | فاكس: {company.Fax}");
            content.AppendLine($"البريد الإلكتروني: {company.Email}");
            content.AppendLine("===============================================");
            content.AppendLine();
            content.AppendLine("                  فاتورة مبيعات");
            content.AppendLine();
            content.AppendLine("-----------------------------------------------");
            content.AppendLine($"رقم الفاتورة: {invoice.InvoiceNumber}");
            content.AppendLine($"تاريخ الفاتورة: {invoice.InvoiceDate:yyyy/MM/dd}");
            content.AppendLine($"العميل: {invoice.Customer?.CustomerName}");
            content.AppendLine($"المخزن: {invoice.Warehouse?.WarehouseName}");
            content.AppendLine("-----------------------------------------------");
            content.AppendLine();

            // بنود الفاتورة
            content.AppendLine("تفاصيل الفاتورة:");
            content.AppendLine("-----------------------------------------------");
            content.AppendLine("الصنف                    الكمية    السعر    الإجمالي");
            content.AppendLine("-----------------------------------------------");

            foreach (var item in invoice.SalesInvoiceItems ?? new List<SalesInvoiceItem>())
            {
                var productName = item.Product?.ProductName ?? "غير محدد";
                if (productName.Length > 20) productName = productName.Substring(0, 17) + "...";
                
                content.AppendLine($"{productName,-20} {item.Quantity,8:N2} {item.UnitPrice,8:N2} {item.Total,10:N2}");
            }

            content.AppendLine("-----------------------------------------------");
            content.AppendLine();

            // الإجماليات
            content.AppendLine("الإجماليات:");
            content.AppendLine("-----------------------------------------------");
            content.AppendLine($"المجموع الفرعي:                    {invoice.Subtotal:N2} {company.Currency}");
            
            if (invoice.DiscountAmount > 0)
                content.AppendLine($"الخصم ({invoice.DiscountRate:N1}%):                     {invoice.DiscountAmount:N2} {company.Currency}");
            
            if (invoice.TaxAmount > 0)
                content.AppendLine($"الضريبة ({invoice.TaxRate:N1}%):                    {invoice.TaxAmount:N2} {company.Currency}");
            
            content.AppendLine("-----------------------------------------------");
            content.AppendLine($"الإجمالي النهائي:                  {invoice.TotalAmount:N2} {company.Currency}");
            content.AppendLine("===============================================");

            if (!string.IsNullOrEmpty(invoice.Notes))
            {
                content.AppendLine();
                content.AppendLine("ملاحظات:");
                content.AppendLine(invoice.Notes);
            }

            content.AppendLine();
            content.AppendLine($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}");
            content.AppendLine($"المستخدم: {invoice.CreatedByUser?.FullName}");

            return content.ToString();
        }

        private string GeneratePurchaseInvoiceContent(PurchaseInvoice invoice, Company company)
        {
            var content = new StringBuilder();
            
            // رأس الفاتورة
            content.AppendLine("===============================================");
            content.AppendLine($"شركة {company.CompanyName}");
            content.AppendLine($"{company.Address}");
            content.AppendLine($"هاتف: {company.Phone} | فاكس: {company.Fax}");
            content.AppendLine($"البريد الإلكتروني: {company.Email}");
            content.AppendLine("===============================================");
            content.AppendLine();
            content.AppendLine("                 فاتورة مشتريات");
            content.AppendLine();
            content.AppendLine("-----------------------------------------------");
            content.AppendLine($"رقم الفاتورة: {invoice.InvoiceNumber}");
            content.AppendLine($"تاريخ الفاتورة: {invoice.InvoiceDate:yyyy/MM/dd}");
            content.AppendLine($"المورد: {invoice.Supplier?.SupplierName}");
            content.AppendLine($"المخزن: {invoice.Warehouse?.WarehouseName}");
            content.AppendLine("-----------------------------------------------");
            content.AppendLine();

            // بنود الفاتورة
            content.AppendLine("تفاصيل الفاتورة:");
            content.AppendLine("-----------------------------------------------");
            content.AppendLine("الصنف                    الكمية    السعر    الإجمالي");
            content.AppendLine("-----------------------------------------------");

            foreach (var item in invoice.PurchaseInvoiceItems ?? new List<PurchaseInvoiceItem>())
            {
                var productName = item.Product?.ProductName ?? "غير محدد";
                if (productName.Length > 20) productName = productName.Substring(0, 17) + "...";
                
                content.AppendLine($"{productName,-20} {item.Quantity,8:N2} {item.UnitPrice,8:N2} {item.Total,10:N2}");
            }

            content.AppendLine("-----------------------------------------------");
            content.AppendLine();

            // الإجماليات
            content.AppendLine("الإجماليات:");
            content.AppendLine("-----------------------------------------------");
            content.AppendLine($"المجموع الفرعي:                    {invoice.Subtotal:N2} {company.Currency}");
            
            if (invoice.DiscountAmount > 0)
                content.AppendLine($"الخصم ({invoice.DiscountRate:N1}%):                     {invoice.DiscountAmount:N2} {company.Currency}");
            
            if (invoice.TaxAmount > 0)
                content.AppendLine($"الضريبة ({invoice.TaxRate:N1}%):                    {invoice.TaxAmount:N2} {company.Currency}");
            
            content.AppendLine("-----------------------------------------------");
            content.AppendLine($"الإجمالي النهائي:                  {invoice.TotalAmount:N2} {company.Currency}");
            content.AppendLine("===============================================");

            if (!string.IsNullOrEmpty(invoice.Notes))
            {
                content.AppendLine();
                content.AppendLine("ملاحظات:");
                content.AppendLine(invoice.Notes);
            }

            content.AppendLine();
            content.AppendLine($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}");
            content.AppendLine($"المستخدم: {invoice.CreatedByUser?.FullName}");

            return content.ToString();
        }

        private string GenerateReportContent(string reportTitle, string reportContent, Company company)
        {
            var content = new StringBuilder();
            
            // رأس التقرير
            content.AppendLine("===============================================");
            content.AppendLine($"شركة {company.CompanyName}");
            content.AppendLine($"{company.Address}");
            content.AppendLine($"هاتف: {company.Phone} | فاكس: {company.Fax}");
            content.AppendLine("===============================================");
            content.AppendLine();
            content.AppendLine($"                  {reportTitle}");
            content.AppendLine();
            content.AppendLine("-----------------------------------------------");
            content.AppendLine();

            // محتوى التقرير
            content.AppendLine(reportContent);

            content.AppendLine();
            content.AppendLine("===============================================");
            content.AppendLine($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}");

            return content.ToString();
        }

        /// <summary>
        /// تصدير فاتورة إلى ملف نصي
        /// Export invoice to text file
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <param name="company">بيانات الشركة</param>
        /// <param name="filePath">مسار الملف</param>
        public void ExportSalesInvoiceToFile(SalesInvoice invoice, Company company, string filePath)
        {
            var content = GenerateSalesInvoiceContent(invoice, company);
            File.WriteAllText(filePath, content, Encoding.UTF8);
        }

        /// <summary>
        /// تصدير فاتورة مشتريات إلى ملف نصي
        /// Export purchase invoice to text file
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <param name="company">بيانات الشركة</param>
        /// <param name="filePath">مسار الملف</param>
        public void ExportPurchaseInvoiceToFile(PurchaseInvoice invoice, Company company, string filePath)
        {
            var content = GeneratePurchaseInvoiceContent(invoice, company);
            File.WriteAllText(filePath, content, Encoding.UTF8);
        }

        /// <summary>
        /// تصدير تقرير إلى ملف نصي
        /// Export report to text file
        /// </summary>
        /// <param name="reportTitle">عنوان التقرير</param>
        /// <param name="reportContent">محتوى التقرير</param>
        /// <param name="company">بيانات الشركة</param>
        /// <param name="filePath">مسار الملف</param>
        public void ExportReportToFile(string reportTitle, string reportContent, Company company, string filePath)
        {
            var content = GenerateReportContent(reportTitle, reportContent, company);
            File.WriteAllText(filePath, content, Encoding.UTF8);
        }
    }
}
