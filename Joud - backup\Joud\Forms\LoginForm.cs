using Joud.DAL;
using Joud.Models;
using Joud.Utilities;
using Microsoft.EntityFrameworkCore;
using System.Configuration;

namespace Joud.Forms
{
    /// <summary>
    /// شاشة تسجيل الدخول
    /// Login Form
    /// </summary>
    public partial class LoginForm : Form
    {
        private int _loginAttempts = 0;
        private readonly int _maxLoginAttempts;
        private string? _connectionString;

        public User? LoggedInUser { get; private set; }
        public Company? UserCompany { get; private set; }

        public LoginForm()
        {
            InitializeComponent();
            InitializeFormSettings();
            
            // قراءة الحد الأقصى لمحاولات تسجيل الدخول من الإعدادات
            _maxLoginAttempts = int.Parse(ConfigurationManager.AppSettings["MaxLoginAttempts"] ?? "3");
            
            // قراءة نص الاتصال
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
        }

        private void InitializeFormSettings()
        {
            // إعدادات النموذج الأساسية
            this.Text = "تسجيل الدخول - نظام جود للمحاسبة المالية";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // تطبيق الألوان والتصميم
            this.BackColor = Color.FromArgb(240, 244, 247);
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInputs())
                    return;

                // تعطيل الأزرار أثناء العملية
                SetControlsEnabled(false);
                lblStatus.Text = "جاري التحقق من بيانات تسجيل الدخول...";
                lblStatus.ForeColor = Color.Blue;

                // محاولة تسجيل الدخول
                bool loginSuccess = await AttemptLogin();

                if (loginSuccess)
                {
                    lblStatus.Text = "تم تسجيل الدخول بنجاح!";
                    lblStatus.ForeColor = Color.Green;

                    // إغلاق النموذج مع نتيجة إيجابية
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    _loginAttempts++;
                    
                    if (_loginAttempts >= _maxLoginAttempts)
                    {
                        lblStatus.Text = "تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول";
                        lblStatus.ForeColor = Color.Red;
                        
                        MessageBox.Show(
                            $"تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول ({_maxLoginAttempts} محاولات).\nسيتم إغلاق التطبيق.",
                            "تجاوز الحد الأقصى",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning
                        );
                        
                        Application.Exit();
                        return;
                    }

                    lblStatus.Text = $"اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: {_maxLoginAttempts - _loginAttempts}";
                    lblStatus.ForeColor = Color.Red;
                    
                    // مسح كلمة المرور والتركيز عليها
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private async Task<bool> AttemptLogin()
        {
            if (string.IsNullOrEmpty(_connectionString))
            {
                ShowError("لم يتم العثور على إعدادات الاتصال بقاعدة البيانات");
                return false;
            }

            var options = new DbContextOptionsBuilder<JoudDbContext>()
                .UseSqlServer(_connectionString)
                .Options;

            using var context = new JoudDbContext(options);

            // البحث عن المستخدم
            var user = await context.Users
                .Include(u => u.UserRole)
                .Include(u => u.Company)
                .FirstOrDefaultAsync(u => u.Username == txtUsername.Text.Trim() && u.IsActive);

            if (user == null)
                return false;

            // التحقق من كلمة المرور
            bool passwordValid = PasswordHelper.VerifyPassword(
                txtPassword.Text, 
                user.PasswordSalt, 
                user.PasswordHash
            );

            if (!passwordValid)
                return false;

            // التحقق من حالة الشركة
            if (!user.Company.IsActive)
            {
                ShowError("الشركة غير نشطة. يرجى الاتصال بالمدير");
                return false;
            }

            // تحديث آخر تسجيل دخول
            user.LastLogin = DateTime.Now;
            await context.SaveChangesAsync();

            // حفظ بيانات المستخدم المسجل
            LoggedInUser = user;
            UserCompany = user.Company;

            return true;
        }

        private bool ValidateInputs()
        {
            // التحقق من اسم المستخدم
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                ShowError("يرجى إدخال اسم المستخدم");
                txtUsername.Focus();
                return false;
            }

            // التحقق من كلمة المرور
            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowError("يرجى إدخال كلمة المرور");
                txtPassword.Focus();
                return false;
            }

            return true;
        }

        private void SetControlsEnabled(bool enabled)
        {
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            btnLogin.Enabled = enabled;
            btnCancel.Enabled = enabled;
        }

        private void ShowError(string message)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Red;
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            // تسجيل الدخول عند الضغط على Enter
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnLogin_Click(sender, e);
            }
        }

        private void txtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            // الانتقال لكلمة المرور عند الضغط على Enter
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
            }
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // التركيز على حقل اسم المستخدم عند تحميل النموذج
            txtUsername.Focus();
            
            // إعادة تعيين حالة النموذج
            lblStatus.Text = "يرجى إدخال بيانات تسجيل الدخول";
            lblStatus.ForeColor = Color.Black;
        }

        private void lnkForgotPassword_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            MessageBox.Show(
                "لاستعادة كلمة المرور، يرجى الاتصال بمدير النظام.",
                "استعادة كلمة المرور",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }

        private void chkShowPassword_CheckedChanged(object sender, EventArgs e)
        {
            // إظهار/إخفاء كلمة المرور
            txtPassword.PasswordChar = chkShowPassword.Checked ? '\0' : '*';
        }
    }
}
